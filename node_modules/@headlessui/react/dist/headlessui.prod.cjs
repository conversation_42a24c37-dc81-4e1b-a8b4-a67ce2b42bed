"use strict";var To=Object.create;var gt=Object.defineProperty;var bo=Object.getOwnPropertyDescriptor;var yo=Object.getOwnPropertyNames;var go=Object.getPrototypeOf,vo=Object.prototype.hasOwnProperty;var xo=(e,n,t)=>n in e?gt(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t;var Eo=(e,n)=>{for(var t in n)gt(e,t,{get:n[t],enumerable:!0})},Jn=(e,n,t,r)=>{if(n&&typeof n=="object"||typeof n=="function")for(let i of yo(n))!vo.call(e,i)&&i!==t&&gt(e,i,{get:()=>n[i],enumerable:!(r=bo(n,i))||r.enumerable});return e};var ie=(e,n,t)=>(t=e!=null?To(go(e)):{},Jn(n||!e||!e.__esModule?gt(t,"default",{value:e,enumerable:!0}):t,e)),Po=e=>Jn(gt({},"__esModule",{value:!0}),e);var _t=(e,n,t)=>(xo(e,typeof n!="symbol"?n+"":n,t),t);var ys={};Eo(ys,{Combobox:()=>ai,Dialog:()=>na,Disclosure:()=>ba,FocusTrap:()=>et,Listbox:()=>wa,Menu:()=>Ya,Popover:()=>gl,Portal:()=>mt,RadioGroup:()=>Dl,Switch:()=>kl,Tab:()=>os,Transition:()=>bs});module.exports=Po(ys);function vt(){return vt=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},vt.apply(this,arguments)}var ke=ie(require("react"),1);function xt(){return xt=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},xt.apply(this,arguments)}function Ke(e,n,t){var r,i=(r=t.initialDeps)!=null?r:[],o;return function(){var a;t.key&&t.debug!=null&&t.debug()&&(a=Date.now());var s=e(),l=s.length!==i.length||s.some(function(b,f){return i[f]!==b});if(!l)return o;i=s;var u;if(t.key&&t.debug!=null&&t.debug()&&(u=Date.now()),o=n.apply(void 0,s),t.key&&t.debug!=null&&t.debug()){var d=Math.round((Date.now()-a)*100)/100,p=Math.round((Date.now()-u)*100)/100,m=p/16,T=function(f,c){for(f=String(f);f.length<c;)f=" "+f;return f};console.info("%c\u23F1 "+T(p,5)+" /"+T(d,5)+" ms",`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(`+Math.max(0,Math.min(120-120*m,120))+"deg 100% 31%);",t==null?void 0:t.key)}return t==null||t.onChange==null||t.onChange(o),o}}function wt(e,n){if(e===void 0)throw new Error("Unexpected undefined"+(n?": "+n:""));return e}var qn=function(n,t){return Math.abs(n-t)<1};var Ro=function(n){return n},ho=function(n){for(var t=Math.max(n.startIndex-n.overscan,0),r=Math.min(n.endIndex+n.overscan,n.count-1),i=[],o=t;o<=r;o++)i.push(o);return i},Yn=function(n,t){var r=n.scrollElement;if(r){var i=function(s){var l=s.width,u=s.height;t({width:Math.round(l),height:Math.round(u)})};i(r.getBoundingClientRect());var o=new ResizeObserver(function(a){var s=a[0];if(s!=null&&s.borderBoxSize){var l=s.borderBoxSize[0];if(l){i({width:l.inlineSize,height:l.blockSize});return}}i(r.getBoundingClientRect())});return o.observe(r,{box:"border-box"}),function(){o.unobserve(r)}}};var Qn=function(n,t){var r=n.scrollElement;if(r){var i=function(){t(r[n.options.horizontal?"scrollLeft":"scrollTop"])};return i(),r.addEventListener("scroll",i,{passive:!0}),function(){r.removeEventListener("scroll",i)}}};var So=function(n,t,r){if(t!=null&&t.borderBoxSize){var i=t.borderBoxSize[0];if(i){var o=Math.round(i[r.options.horizontal?"inlineSize":"blockSize"]);return o}}return Math.round(n.getBoundingClientRect()[r.options.horizontal?"width":"height"])};var Zn=function(n,t,r){var i,o,a=t.adjustments,s=a===void 0?0:a,l=t.behavior,u=n+s;(i=r.scrollElement)==null||i.scrollTo==null||i.scrollTo((o={},o[r.options.horizontal?"left":"top"]=u,o.behavior=l,o))},er=function(n){var t=this;this.unsubs=[],this.scrollElement=null,this.isScrolling=!1,this.isScrollingTimeoutId=null,this.scrollToIndexTimeoutId=null,this.measurementsCache=[],this.itemSizeCache=new Map,this.pendingMeasuredCacheIndexes=[],this.scrollDirection=null,this.scrollAdjustments=0,this.measureElementCache=new Map,this.observer=function(){var r=null,i=function(){return r||(typeof ResizeObserver!="undefined"?r=new ResizeObserver(function(a){a.forEach(function(s){t._measureElement(s.target,s)})}):null)};return{disconnect:function(){var a;return(a=i())==null?void 0:a.disconnect()},observe:function(a){var s;return(s=i())==null?void 0:s.observe(a,{box:"border-box"})},unobserve:function(a){var s;return(s=i())==null?void 0:s.unobserve(a)}}}(),this.range={startIndex:0,endIndex:0},this.setOptions=function(r){Object.entries(r).forEach(function(i){var o=i[0],a=i[1];typeof a=="undefined"&&delete r[o]}),t.options=xt({debug:!1,initialOffset:0,overscan:1,paddingStart:0,paddingEnd:0,scrollPaddingStart:0,scrollPaddingEnd:0,horizontal:!1,getItemKey:Ro,rangeExtractor:ho,onChange:function(){},measureElement:So,initialRect:{width:0,height:0},scrollMargin:0,scrollingDelay:150,indexAttribute:"data-index",initialMeasurementsCache:[],lanes:1},r)},this.notify=function(){t.options.onChange==null||t.options.onChange(t)},this.cleanup=function(){t.unsubs.filter(Boolean).forEach(function(r){return r()}),t.unsubs=[],t.scrollElement=null},this._didMount=function(){return t.measureElementCache.forEach(t.observer.observe),function(){t.observer.disconnect(),t.cleanup()}},this._willUpdate=function(){var r=t.options.getScrollElement();t.scrollElement!==r&&(t.cleanup(),t.scrollElement=r,t._scrollToOffset(t.scrollOffset,{adjustments:void 0,behavior:void 0}),t.unsubs.push(t.options.observeElementRect(t,function(i){var o=t.scrollRect;t.scrollRect=i,(t.options.horizontal?i.width!==o.width:i.height!==o.height)&&t.maybeNotify()})),t.unsubs.push(t.options.observeElementOffset(t,function(i){t.scrollAdjustments=0,t.scrollOffset!==i&&(t.isScrollingTimeoutId!==null&&(clearTimeout(t.isScrollingTimeoutId),t.isScrollingTimeoutId=null),t.isScrolling=!0,t.scrollDirection=t.scrollOffset<i?"forward":"backward",t.scrollOffset=i,t.maybeNotify(),t.isScrollingTimeoutId=setTimeout(function(){t.isScrollingTimeoutId=null,t.isScrolling=!1,t.scrollDirection=null,t.maybeNotify()},t.options.scrollingDelay))})))},this.getSize=function(){return t.scrollRect[t.options.horizontal?"width":"height"]},this.memoOptions=Ke(function(){return[t.options.count,t.options.paddingStart,t.options.scrollMargin,t.options.getItemKey]},function(r,i,o,a){return t.pendingMeasuredCacheIndexes=[],{count:r,paddingStart:i,scrollMargin:o,getItemKey:a}},{key:!1}),this.getFurthestMeasurement=function(r,i){for(var o=new Map,a=new Map,s=i-1;s>=0;s--){var l=r[s];if(!o.has(l.lane)){var u=a.get(l.lane);if(u==null||l.end>u.end?a.set(l.lane,l):l.end<u.end&&o.set(l.lane,!0),o.size===t.options.lanes)break}}return a.size===t.options.lanes?Array.from(a.values()).sort(function(d,p){return d.end-p.end})[0]:void 0},this.getMeasurements=Ke(function(){return[t.memoOptions(),t.itemSizeCache]},function(r,i){var o=r.count,a=r.paddingStart,s=r.scrollMargin,l=r.getItemKey,u=t.pendingMeasuredCacheIndexes.length>0?Math.min.apply(Math,t.pendingMeasuredCacheIndexes):0;t.pendingMeasuredCacheIndexes=[];for(var d=t.measurementsCache.slice(0,u),p=u;p<o;p++){var m=l(p),T=t.options.lanes===1?d[p-1]:t.getFurthestMeasurement(d,p),b=T?T.end:a+s,f=i.get(m),c=typeof f=="number"?f:t.options.estimateSize(p),g=b+c,v=T?T.lane:p%t.options.lanes;d[p]={index:p,start:b,size:c,end:g,key:m,lane:v}}return t.measurementsCache=d,d},{key:!1,debug:function(){return t.options.debug}}),this.calculateRange=Ke(function(){return[t.getMeasurements(),t.getSize(),t.scrollOffset]},function(r,i,o){return t.range=Oo({measurements:r,outerSize:i,scrollOffset:o})},{key:!1,debug:function(){return t.options.debug}}),this.maybeNotify=Ke(function(){var r=t.calculateRange();return[r.startIndex,r.endIndex,t.isScrolling]},function(){t.notify()},{key:!1,debug:function(){return t.options.debug},initialDeps:[this.range.startIndex,this.range.endIndex,this.isScrolling]}),this.getIndexes=Ke(function(){return[t.options.rangeExtractor,t.calculateRange(),t.options.overscan,t.options.count]},function(r,i,o,a){return r(xt({},i,{overscan:o,count:a}))},{key:!1,debug:function(){return t.options.debug}}),this.indexFromElement=function(r){var i=t.options.indexAttribute,o=r.getAttribute(i);return o?parseInt(o,10):(console.warn("Missing attribute name '"+i+"={index}' on measured element."),-1)},this._measureElement=function(r,i){var o=t.measurementsCache[t.indexFromElement(r)];if(!o){t.measureElementCache.forEach(function(l,u){l===r&&(t.observer.unobserve(r),t.measureElementCache.delete(u))});return}var a=t.measureElementCache.get(o.key);if(!r.isConnected){a&&(t.observer.unobserve(a),t.measureElementCache.delete(o.key));return}a!==r&&(a&&t.observer.unobserve(a),t.observer.observe(r),t.measureElementCache.set(o.key,r));var s=t.options.measureElement(r,i,t);t.resizeItem(o,s)},this.resizeItem=function(r,i){var o,a=(o=t.itemSizeCache.get(r.key))!=null?o:r.size,s=i-a;s!==0&&(r.start<t.scrollOffset&&t._scrollToOffset(t.scrollOffset,{adjustments:t.scrollAdjustments+=s,behavior:void 0}),t.pendingMeasuredCacheIndexes.push(r.index),t.itemSizeCache=new Map(t.itemSizeCache.set(r.key,i)),t.notify())},this.measureElement=function(r){r&&t._measureElement(r,void 0)},this.getVirtualItems=Ke(function(){return[t.getIndexes(),t.getMeasurements()]},function(r,i){for(var o=[],a=0,s=r.length;a<s;a++){var l=r[a],u=i[l];o.push(u)}return o},{key:!1,debug:function(){return t.options.debug}}),this.getVirtualItemForOffset=function(r){var i=t.getMeasurements();return wt(i[tr(0,i.length-1,function(o){return wt(i[o]).start},r)])},this.getOffsetForAlignment=function(r,i){var o=t.getSize();i==="auto"&&(r<=t.scrollOffset?i="start":r>=t.scrollOffset+o?i="end":i="start"),i==="start"?r=r:i==="end"?r=r-o:i==="center"&&(r=r-o/2);var a=t.options.horizontal?"scrollWidth":"scrollHeight",s=t.scrollElement?"document"in t.scrollElement?t.scrollElement.document.documentElement[a]:t.scrollElement[a]:0,l=s-t.getSize();return Math.max(Math.min(l,r),0)},this.getOffsetForIndex=function(r,i){i===void 0&&(i="auto"),r=Math.max(0,Math.min(r,t.options.count-1));var o=wt(t.getMeasurements()[r]);if(i==="auto")if(o.end>=t.scrollOffset+t.getSize()-t.options.scrollPaddingEnd)i="end";else if(o.start<=t.scrollOffset+t.options.scrollPaddingStart)i="start";else return[t.scrollOffset,i];var a=i==="end"?o.end+t.options.scrollPaddingEnd:o.start-t.options.scrollPaddingStart;return[t.getOffsetForAlignment(a,i),i]},this.isDynamicMode=function(){return t.measureElementCache.size>0},this.cancelScrollToIndex=function(){t.scrollToIndexTimeoutId!==null&&(clearTimeout(t.scrollToIndexTimeoutId),t.scrollToIndexTimeoutId=null)},this.scrollToOffset=function(r,i){var o=i===void 0?{}:i,a=o.align,s=a===void 0?"start":a,l=o.behavior;t.cancelScrollToIndex(),l==="smooth"&&t.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),t._scrollToOffset(t.getOffsetForAlignment(r,s),{adjustments:void 0,behavior:l})},this.scrollToIndex=function(r,i){var o=i===void 0?{}:i,a=o.align,s=a===void 0?"auto":a,l=o.behavior;r=Math.max(0,Math.min(r,t.options.count-1)),t.cancelScrollToIndex(),l==="smooth"&&t.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size.");var u=t.getOffsetForIndex(r,s),d=u[0],p=u[1];t._scrollToOffset(d,{adjustments:void 0,behavior:l}),l!=="smooth"&&t.isDynamicMode()&&(t.scrollToIndexTimeoutId=setTimeout(function(){t.scrollToIndexTimeoutId=null;var m=t.measureElementCache.has(t.options.getItemKey(r));if(m){var T=t.getOffsetForIndex(r,p),b=T[0];qn(b,t.scrollOffset)||t.scrollToIndex(r,{align:p,behavior:l})}else t.scrollToIndex(r,{align:p,behavior:l})}))},this.scrollBy=function(r,i){var o=i===void 0?{}:i,a=o.behavior;t.cancelScrollToIndex(),a==="smooth"&&t.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),t._scrollToOffset(t.scrollOffset+r,{adjustments:void 0,behavior:a})},this.getTotalSize=function(){var r;return(((r=t.getMeasurements()[t.options.count-1])==null?void 0:r.end)||t.options.paddingStart)-t.options.scrollMargin+t.options.paddingEnd},this._scrollToOffset=function(r,i){var o=i.adjustments,a=i.behavior;t.options.scrollToFn(r,{behavior:a,adjustments:o},t)},this.measure=function(){t.itemSizeCache=new Map,t.notify()},this.setOptions(n),this.scrollRect=this.options.initialRect,this.scrollOffset=this.options.initialOffset,this.measurementsCache=this.options.initialMeasurementsCache,this.measurementsCache.forEach(function(r){t.itemSizeCache.set(r.key,r.size)}),this.maybeNotify()},tr=function(n,t,r,i){for(;n<=t;){var o=(n+t)/2|0,a=r(o);if(a<i)n=o+1;else if(a>i)t=o-1;else return o}return n>0?n-1:0};function Oo(e){for(var n=e.measurements,t=e.outerSize,r=e.scrollOffset,i=n.length-1,o=function(u){return n[u].start},a=tr(0,i,o,r),s=a;s<i&&n[s].end<r+t;)s++;return{startIndex:a,endIndex:s}}var Co=typeof document!="undefined"?ke.useLayoutEffect:ke.useEffect;function Ao(e){var n=ke.useReducer(function(){return{}},{})[1],t=vt({},e,{onChange:function(a){n(),e.onChange==null||e.onChange(a)}}),r=ke.useState(function(){return new er(t)}),i=r[0];return i.setOptions(t),ke.useEffect(function(){return i._didMount()},[]),Co(function(){return i._willUpdate()}),i}function nr(e){return Ao(vt({observeElementRect:Yn,observeElementOffset:Qn,scrollToFn:Zn},e))}var N=ie(require("react"),1);var or=require("react");var Ht=require("react");var sn=class{constructor(){_t(this,"current",this.detect());_t(this,"handoffState","pending");_t(this,"currentId",0)}set(n){this.current!==n&&(this.handoffState="pending",this.currentId=0,this.current=n)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return this.current==="server"}get isClient(){return this.current==="client"}detect(){return typeof window=="undefined"||typeof document=="undefined"?"server":"client"}handoff(){this.handoffState==="pending"&&(this.handoffState="complete")}get isHandoffComplete(){return this.handoffState==="complete"}},he=new sn;var I=(e,n)=>{he.isServer?(0,Ht.useEffect)(e,n):(0,Ht.useLayoutEffect)(e,n)};var rr=require("react");function ee(e){let n=(0,rr.useRef)(e);return I(()=>{n.current=e},[e]),n}function $e(e,n){let[t,r]=(0,or.useState)(e),i=ee(e);return I(()=>r(i.current),[i,r,...n]),t}var ot=require("react");var ir=ie(require("react"),1);var y=function(n){let t=ee(n);return ir.default.useCallback((...r)=>t.current(...r),[t])};function Ve(e,n,t){let[r,i]=(0,ot.useState)(t),o=e!==void 0,a=(0,ot.useRef)(o),s=(0,ot.useRef)(!1),l=(0,ot.useRef)(!1);return o&&!a.current&&!s.current?(s.current=!0,a.current=o,console.error("A component is changing from uncontrolled to controlled. This may be caused by the value changing from undefined to a defined value, which should not happen.")):!o&&a.current&&!l.current&&(l.current=!0,a.current=o,console.error("A component is changing from controlled to uncontrolled. This may be caused by the value changing from a defined value to undefined, which should not happen.")),[o?e:r,y(u=>(o||i(u),n==null?void 0:n(u)))]}var kt=require("react");function Ne(e){typeof queueMicrotask=="function"?queueMicrotask(e):Promise.resolve().then(e).catch(n=>setTimeout(()=>{throw n}))}function le(){let e=[],n={addEventListener(t,r,i,o){return t.addEventListener(r,i,o),n.add(()=>t.removeEventListener(r,i,o))},requestAnimationFrame(...t){let r=requestAnimationFrame(...t);return n.add(()=>cancelAnimationFrame(r))},nextFrame(...t){return n.requestAnimationFrame(()=>n.requestAnimationFrame(...t))},setTimeout(...t){let r=setTimeout(...t);return n.add(()=>clearTimeout(r))},microTask(...t){let r={current:!0};return Ne(()=>{r.current&&t[0]()}),n.add(()=>{r.current=!1})},style(t,r,i){let o=t.style.getPropertyValue(r);return Object.assign(t.style,{[r]:i}),this.add(()=>{Object.assign(t.style,{[r]:o})})},group(t){let r=le();return t(r),this.add(()=>r.dispose())},add(t){return e.push(t),()=>{let r=e.indexOf(t);if(r>=0)for(let i of e.splice(r,1))i()}},dispose(){for(let t of e.splice(0))t()}};return n}function ce(){let[e]=(0,kt.useState)(le);return(0,kt.useEffect)(()=>()=>e.dispose(),[e]),e}var un=ie(require("react"),1);var ze=ie(require("react"),1);function Lo(){let e=typeof document=="undefined";return"useSyncExternalStore"in ze?(r=>r.useSyncExternalStore)(ze)(()=>()=>{},()=>!1,()=>!e):!1}function De(){let e=Lo(),[n,t]=ze.useState(he.isHandoffComplete);return n&&he.isHandoffComplete===!1&&t(!1),ze.useEffect(()=>{n!==!0&&t(!0)},[n]),ze.useEffect(()=>he.handoff(),[]),e?!1:n}var ar,W=(ar=un.default.useId)!=null?ar:function(){let n=De(),[t,r]=un.default.useState(n?()=>he.nextId():null);return I(()=>{t===null&&r(he.nextId())},[t]),t!=null?""+t:void 0};var Pt=require("react");function F(e,n,...t){if(e in n){let i=n[e];return typeof i=="function"?i(...t):i}let r=new Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(n).map(i=>`"${i}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(r,F),r}function ve(e){return he.isServer?null:e instanceof Node?e.ownerDocument:e!=null&&e.hasOwnProperty("current")&&e.current instanceof Node?e.current.ownerDocument:document}var pn=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>`${e}:not([tabindex='-1'])`).join(",");function it(e=document.body){return e==null?[]:Array.from(e.querySelectorAll(pn)).sort((n,t)=>Math.sign((n.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}function Ue(e,n=0){var t;return e===((t=ve(e))==null?void 0:t.body)?!1:F(n,{[0](){return e.matches(pn)},[1](){let r=e;for(;r!==null;){if(r.matches(pn))return!0;r=r.parentElement}return!1}})}function dn(e){let n=ve(e);le().nextFrame(()=>{n&&!Ue(n.activeElement,0)&&Be(e)})}typeof window!="undefined"&&typeof document!="undefined"&&(document.addEventListener("keydown",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")},!0),document.addEventListener("click",e=>{e.detail===1?delete document.documentElement.dataset.headlessuiFocusVisible:e.detail===0&&(document.documentElement.dataset.headlessuiFocusVisible="")},!0));function Be(e){e==null||e.focus({preventScroll:!0})}var Do=["textarea","input"].join(",");function Mo(e){var n,t;return(t=(n=e==null?void 0:e.matches)==null?void 0:n.call(e,Do))!=null?t:!1}function Re(e,n=t=>t){return e.slice().sort((t,r)=>{let i=n(t),o=n(r);if(i===null||o===null)return 0;let a=i.compareDocumentPosition(o);return a&Node.DOCUMENT_POSITION_FOLLOWING?-1:a&Node.DOCUMENT_POSITION_PRECEDING?1:0})}function lr(e,n){return pe(it(),n,{relativeTo:e})}function pe(e,n,{sorted:t=!0,relativeTo:r=null,skipElements:i=[]}={}){let o=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,a=Array.isArray(e)?t?Re(e):e:it(e);i.length>0&&a.length>1&&(a=a.filter(T=>!i.includes(T))),r=r!=null?r:o.activeElement;let s=(()=>{if(n&5)return 1;if(n&10)return-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),l=(()=>{if(n&1)return 0;if(n&2)return Math.max(0,a.indexOf(r))-1;if(n&4)return Math.max(0,a.indexOf(r))+1;if(n&8)return a.length-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),u=n&32?{preventScroll:!0}:{},d=0,p=a.length,m;do{if(d>=p||d+p<=0)return 0;let T=l+d;if(n&16)T=(T+p)%p;else{if(T<0)return 3;if(T>=p)return 1}m=a[T],m==null||m.focus(u),d+=s}while(m!==o.activeElement);return n&6&&Mo(m)&&m.select(),2}function cn(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function Io(){return/Android/gi.test(window.navigator.userAgent)}function Nt(){return cn()||Io()}var sr=require("react");function Et(e,n,t){let r=ee(n);(0,sr.useEffect)(()=>{function i(o){r.current(o)}return document.addEventListener(e,i,t),()=>document.removeEventListener(e,i,t)},[e,t])}var ur=require("react");function Ut(e,n,t){let r=ee(n);(0,ur.useEffect)(()=>{function i(o){r.current(o)}return window.addEventListener(e,i,t),()=>window.removeEventListener(e,i,t)},[e,t])}function _e(e,n,t=!0){let r=(0,Pt.useRef)(!1);(0,Pt.useEffect)(()=>{requestAnimationFrame(()=>{r.current=t})},[t]);function i(a,s){if(!r.current||a.defaultPrevented)return;let l=s(a);if(l===null||!l.getRootNode().contains(l)||!l.isConnected)return;let u=function d(p){return typeof p=="function"?d(p()):Array.isArray(p)||p instanceof Set?p:[p]}(e);for(let d of u){if(d===null)continue;let p=d instanceof HTMLElement?d:d.current;if(p!=null&&p.contains(l)||a.composed&&a.composedPath().includes(p))return}return!Ue(l,1)&&l.tabIndex!==-1&&a.preventDefault(),n(a,l)}let o=(0,Pt.useRef)(null);Et("pointerdown",a=>{var s,l;r.current&&(o.current=((l=(s=a.composedPath)==null?void 0:s.call(a))==null?void 0:l[0])||a.target)},!0),Et("mousedown",a=>{var s,l;r.current&&(o.current=((l=(s=a.composedPath)==null?void 0:s.call(a))==null?void 0:l[0])||a.target)},!0),Et("click",a=>{Nt()||o.current&&(i(a,()=>o.current),o.current=null)},!0),Et("touchend",a=>i(a,()=>a.target instanceof HTMLElement?a.target:null),!0),Ut("blur",a=>i(a,()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null),!0)}var pr=require("react");function xe(...e){return(0,pr.useMemo)(()=>ve(...e),[...e])}var cr=require("react");function dr(e){var t;if(e.type)return e.type;let n=(t=e.as)!=null?t:"button";if(typeof n=="string"&&n.toLowerCase()==="button")return"button"}function Se(e,n){let[t,r]=(0,cr.useState)(()=>dr(e));return I(()=>{r(dr(e))},[e.type,e.as]),I(()=>{t||n.current&&n.current instanceof HTMLButtonElement&&!n.current.hasAttribute("type")&&r("button")},[t,n]),t}var Bt=require("react");var fr=Symbol();function at(e,n=!0){return Object.assign(e,{[fr]:n})}function _(...e){let n=(0,Bt.useRef)(e);(0,Bt.useEffect)(()=>{n.current=e},[e]);let t=y(r=>{for(let i of n.current)i!=null&&(typeof i=="function"?i(r):i.current=r)});return e.every(r=>r==null||(r==null?void 0:r[fr]))?void 0:t}var Tr=require("react");function mr(e){return[e.screenX,e.screenY]}function lt(){let e=(0,Tr.useRef)([-1,-1]);return{wasMoved(n){let t=mr(n);return e.current[0]===t[0]&&e.current[1]===t[1]?!1:(e.current=t,!0)},update(n){e.current=mr(n)}}}var Rt=require("react");function st({container:e,accept:n,walk:t,enabled:r=!0}){let i=(0,Rt.useRef)(n),o=(0,Rt.useRef)(t);(0,Rt.useEffect)(()=>{i.current=n,o.current=t},[n,t]),I(()=>{if(!e||!r)return;let a=ve(e);if(!a)return;let s=i.current,l=o.current,u=Object.assign(p=>s(p),{acceptNode:s}),d=a.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,u,!1);for(;d.nextNode();)l(d.currentNode)},[e,r,i,o])}var Gt=require("react");function Xe(e,n){let t=(0,Gt.useRef)([]),r=y(e);(0,Gt.useEffect)(()=>{let i=[...t.current];for(let[o,a]of n.entries())if(t.current[o]!==a){let s=r(n,i);return t.current=n,s}},[r,...n])}var Ee=require("react");function ut(...e){return Array.from(new Set(e.flatMap(n=>typeof n=="string"?n.split(" "):[]))).filter(Boolean).join(" ")}function D({ourProps:e,theirProps:n,slot:t,defaultTag:r,features:i,visible:o=!0,name:a,mergeRefs:s}){s=s!=null?s:Fo;let l=br(n,e);if(o)return Vt(l,t,r,a,s);let u=i!=null?i:0;if(u&2){let{static:d=!1,...p}=l;if(d)return Vt(p,t,r,a,s)}if(u&1){let{unmount:d=!0,...p}=l;return F(d?0:1,{[0](){return null},[1](){return Vt({...p,hidden:!0,style:{display:"none"}},t,r,a,s)}})}return Vt(l,t,r,a,s)}function Vt(e,n={},t,r,i){let{as:o=t,children:a,refName:s="ref",...l}=mn(e,["unmount","static"]),u=e.ref!==void 0?{[s]:e.ref}:{},d=typeof a=="function"?a(n):a;"className"in l&&l.className&&typeof l.className=="function"&&(l.className=l.className(n));let p={};if(n){let m=!1,T=[];for(let[b,f]of Object.entries(n))typeof f=="boolean"&&(m=!0),f===!0&&T.push(b);m&&(p["data-headlessui-state"]=T.join(" "))}if(o===Ee.Fragment&&Object.keys(we(l)).length>0){if(!(0,Ee.isValidElement)(d)||Array.isArray(d)&&d.length>1)throw new Error(['Passing props on "Fragment"!',"",`The current component <${r} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(l).map(f=>`  - ${f}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(f=>`  - ${f}`).join(`
`)].join(`
`));let m=d.props,T=typeof(m==null?void 0:m.className)=="function"?(...f)=>ut(m==null?void 0:m.className(...f),l.className):ut(m==null?void 0:m.className,l.className),b=T?{className:T}:{};return(0,Ee.cloneElement)(d,Object.assign({},br(d.props,we(mn(l,["ref"]))),p,u,{ref:i(d.ref,u.ref)},b))}return(0,Ee.createElement)(o,Object.assign({},mn(l,["ref"]),o!==Ee.Fragment&&u,o!==Ee.Fragment&&p),d)}function ht(){let e=(0,Ee.useRef)([]),n=(0,Ee.useCallback)(t=>{for(let r of e.current)r!=null&&(typeof r=="function"?r(t):r.current=t)},[]);return(...t)=>{if(!t.every(r=>r==null))return e.current=t,n}}function Fo(...e){return e.every(n=>n==null)?void 0:n=>{for(let t of e)t!=null&&(typeof t=="function"?t(n):t.current=n)}}function br(...e){var r;if(e.length===0)return{};if(e.length===1)return e[0];let n={},t={};for(let i of e)for(let o in i)o.startsWith("on")&&typeof i[o]=="function"?((r=t[o])!=null||(t[o]=[]),t[o].push(i[o])):n[o]=i[o];if(n.disabled||n["aria-disabled"])return Object.assign(n,Object.fromEntries(Object.keys(t).map(i=>[i,void 0])));for(let i in t)Object.assign(n,{[i](o,...a){let s=t[i];for(let l of s){if((o instanceof Event||(o==null?void 0:o.nativeEvent)instanceof Event)&&o.defaultPrevented)return;l(o,...a)}}});return n}function M(e){var n;return Object.assign((0,Ee.forwardRef)(e),{displayName:(n=e.displayName)!=null?n:e.name})}function we(e){let n=Object.assign({},e);for(let t in n)n[t]===void 0&&delete n[t];return n}function mn(e,n=[]){let t=Object.assign({},e);for(let r of n)r in t&&delete t[r];return t}var _o="div";function wo(e,n){var o;let{features:t=1,...r}=e,i={ref:n,"aria-hidden":(t&2)===2?!0:(o=r["aria-hidden"])!=null?o:void 0,hidden:(t&4)===4?!0:void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...(t&4)===4&&(t&2)!==2&&{display:"none"}}};return D({ourProps:i,theirProps:r,slot:{},defaultTag:_o,name:"Hidden"})}var fe=M(wo);var pt=ie(require("react"),1),Tn=(0,pt.createContext)(null);Tn.displayName="OpenClosedContext";function Pe(){return(0,pt.useContext)(Tn)}function Ce({value:e,children:n}){return pt.default.createElement(Tn.Provider,{value:e},n)}function yr(e){function n(){document.readyState!=="loading"&&(e(),document.removeEventListener("DOMContentLoaded",n))}typeof window!="undefined"&&typeof document!="undefined"&&(document.addEventListener("DOMContentLoaded",n),n())}var Ae=[];yr(()=>{function e(n){n.target instanceof HTMLElement&&n.target!==document.body&&Ae[0]!==n.target&&(Ae.unshift(n.target),Ae=Ae.filter(t=>t!=null&&t.isConnected),Ae.splice(10))}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})});function ge(e){let n=e.parentElement,t=null;for(;n&&!(n instanceof HTMLFieldSetElement);)n instanceof HTMLLegendElement&&(t=n),n=n.parentElement;let r=(n==null?void 0:n.getAttribute("disabled"))==="";return r&&Ho(t)?!1:r}function Ho(e){if(!e)return!1;let n=e.previousElementSibling;for(;n!==null;){if(n instanceof HTMLLegendElement)return!1;n=n.previousElementSibling}return!0}function ko(e){throw new Error("Unexpected object: "+e)}function Je(e,n){let t=n.resolveItems();if(t.length<=0)return null;let r=n.resolveActiveIndex(),i=r!=null?r:-1;switch(e.focus){case 0:{for(let o=0;o<t.length;++o)if(!n.resolveDisabled(t[o],o,t))return o;return r}case 1:{for(let o=i-1;o>=0;--o)if(!n.resolveDisabled(t[o],o,t))return o;return r}case 2:{for(let o=i+1;o<t.length;++o)if(!n.resolveDisabled(t[o],o,t))return o;return r}case 3:{for(let o=t.length-1;o>=0;--o)if(!n.resolveDisabled(t[o],o,t))return o;return r}case 4:{for(let o=0;o<t.length;++o)if(n.resolveId(t[o],o,t)===e.id)return o;return r}case 5:return null;default:ko(e)}}function qe(e={},n=null,t=[]){for(let[r,i]of Object.entries(e))vr(t,gr(n,r),i);return t}function gr(e,n){return e?e+"["+n+"]":n}function vr(e,n,t){if(Array.isArray(t))for(let[r,i]of t.entries())vr(e,gr(n,r.toString()),i);else t instanceof Date?e.push([n,t.toISOString()]):typeof t=="boolean"?e.push([n,t?"1":"0"]):typeof t=="string"?e.push([n,t]):typeof t=="number"?e.push([n,`${t}`]):t==null?e.push([n,""]):qe(t,n,e)}function jt(e){var t,r;let n=(t=e==null?void 0:e.form)!=null?t:e.closest("form");if(n){for(let i of n.elements)if(i!==e&&(i.tagName==="INPUT"&&i.type==="submit"||i.tagName==="BUTTON"&&i.type==="submit"||i.nodeName==="INPUT"&&i.type==="image")){i.click();return}(r=n.requestSubmit)==null||r.call(n)}}function bn(e,n=t=>t){let t=e.activeOptionIndex!==null?e.options[e.activeOptionIndex]:null,r=n(e.options.slice()),i=r.length>0&&r[0].dataRef.current.order!==null?r.sort((a,s)=>a.dataRef.current.order-s.dataRef.current.order):Re(r,a=>a.dataRef.current.domRef.current),o=t?i.indexOf(t):null;return o===-1&&(o=null),{options:i,activeOptionIndex:o}}var No={[1](e){var n;return(n=e.dataRef.current)!=null&&n.disabled||e.comboboxState===1?e:{...e,activeOptionIndex:null,comboboxState:1}},[0](e){var n,t;if((n=e.dataRef.current)!=null&&n.disabled||e.comboboxState===0)return e;if((t=e.dataRef.current)!=null&&t.value){let r=e.dataRef.current.calculateIndex(e.dataRef.current.value);if(r!==-1)return{...e,activeOptionIndex:r,comboboxState:0}}return{...e,comboboxState:0}},[2](e,n){var o,a,s,l,u;if((o=e.dataRef.current)!=null&&o.disabled||(a=e.dataRef.current)!=null&&a.optionsRef.current&&!((s=e.dataRef.current)!=null&&s.optionsPropsRef.current.static)&&e.comboboxState===1)return e;if(e.virtual){let d=n.focus===4?n.idx:Je(n,{resolveItems:()=>e.virtual.options,resolveActiveIndex:()=>{var m,T;return(T=(m=e.activeOptionIndex)!=null?m:e.virtual.options.findIndex(b=>!e.virtual.disabled(b)))!=null?T:null},resolveDisabled:e.virtual.disabled,resolveId(){throw new Error("Function not implemented.")}}),p=(l=n.trigger)!=null?l:2;return e.activeOptionIndex===d&&e.activationTrigger===p?e:{...e,activeOptionIndex:d,activationTrigger:p}}let t=bn(e);if(t.activeOptionIndex===null){let d=t.options.findIndex(p=>!p.dataRef.current.disabled);d!==-1&&(t.activeOptionIndex=d)}let r=n.focus===4?n.idx:Je(n,{resolveItems:()=>t.options,resolveActiveIndex:()=>t.activeOptionIndex,resolveId:d=>d.id,resolveDisabled:d=>d.dataRef.current.disabled}),i=(u=n.trigger)!=null?u:2;return e.activeOptionIndex===r&&e.activationTrigger===i?e:{...e,...t,activeOptionIndex:r,activationTrigger:i}},[3]:(e,n)=>{var o,a,s;if((o=e.dataRef.current)!=null&&o.virtual)return{...e,options:[...e.options,n.payload]};let t=n.payload,r=bn(e,l=>(l.push(t),l));e.activeOptionIndex===null&&(a=e.dataRef.current)!=null&&a.isSelected(n.payload.dataRef.current.value)&&(r.activeOptionIndex=r.options.indexOf(t));let i={...e,...r,activationTrigger:2};return(s=e.dataRef.current)!=null&&s.__demoMode&&e.dataRef.current.value===void 0&&(i.activeOptionIndex=0),i},[4]:(e,n)=>{var r;if((r=e.dataRef.current)!=null&&r.virtual)return{...e,options:e.options.filter(i=>i.id!==n.id)};let t=bn(e,i=>{let o=i.findIndex(a=>a.id===n.id);return o!==-1&&i.splice(o,1),i});return{...e,...t,activationTrigger:2}},[5]:(e,n)=>e.labelId===n.id?e:{...e,labelId:n.id},[6]:(e,n)=>e.activationTrigger===n.trigger?e:{...e,activationTrigger:n.trigger},[7]:(e,n)=>{var r;if(((r=e.virtual)==null?void 0:r.options)===n.options)return e;let t=e.activeOptionIndex;if(e.activeOptionIndex!==null){let i=n.options.indexOf(e.virtual.options[e.activeOptionIndex]);i!==-1?t=i:t=null}return{...e,activeOptionIndex:t,virtual:Object.assign({},e.virtual,{options:n.options})}}},yn=(0,N.createContext)(null);yn.displayName="ComboboxActionsContext";function St(e){let n=(0,N.useContext)(yn);if(n===null){let t=new Error(`<${e} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,St),t}return n}var Er=(0,N.createContext)(null);function Uo(e){var s;let n=Ze("VirtualProvider"),[t,r]=(0,N.useMemo)(()=>{let l=n.optionsRef.current;if(!l)return[0,0];let u=window.getComputedStyle(l);return[parseFloat(u.paddingBlockStart||u.paddingTop),parseFloat(u.paddingBlockEnd||u.paddingBottom)]},[n.optionsRef.current]),i=nr({scrollPaddingStart:t,scrollPaddingEnd:r,count:n.virtual.options.length,estimateSize(){return 40},getScrollElement(){var l;return(l=n.optionsRef.current)!=null?l:null},overscan:12}),[o,a]=(0,N.useState)(0);return I(()=>{a(l=>l+1)},[(s=n.virtual)==null?void 0:s.options]),N.default.createElement(Er.Provider,{value:i},N.default.createElement("div",{style:{position:"relative",width:"100%",height:`${i.getTotalSize()}px`},ref:l=>{if(l){if(typeof process!="undefined"&&process.env.JEST_WORKER_ID!==void 0||n.activationTrigger===0)return;n.activeOptionIndex!==null&&n.virtual.options.length>n.activeOptionIndex&&i.scrollToIndex(n.activeOptionIndex)}}},i.getVirtualItems().map(l=>{var u;return N.default.createElement(N.Fragment,{key:l.key},N.default.cloneElement((u=e.children)==null?void 0:u.call(e,{option:n.virtual.options[l.index],open:n.comboboxState===0}),{key:`${o}-${l.key}`,"data-index":l.index,"aria-setsize":n.virtual.options.length,"aria-posinset":l.index+1,style:{position:"absolute",top:0,left:0,transform:`translateY(${l.start}px)`,overflowAnchor:"none"}}))})))}var gn=(0,N.createContext)(null);gn.displayName="ComboboxDataContext";function Ze(e){let n=(0,N.useContext)(gn);if(n===null){let t=new Error(`<${e} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Ze),t}return n}function Bo(e,n){return F(n.type,No,e,n)}var Go=N.Fragment;function Vo(e,n){var It;let{value:t,defaultValue:r,onChange:i,form:o,name:a,by:s=null,disabled:l=!1,__demoMode:u=!1,nullable:d=!1,multiple:p=!1,immediate:m=!1,virtual:T=null,...b}=e,f=!1,c=null,[g=p?[]:void 0,v]=Ve(t,i,r),[E,C]=(0,N.useReducer)(Bo,{dataRef:(0,N.createRef)(),comboboxState:u?0:1,options:[],virtual:c?{options:c.options,disabled:(It=c.disabled)!=null?It:()=>!1}:null,activeOptionIndex:null,activationTrigger:2,labelId:null}),U=(0,N.useRef)(!1),L=(0,N.useRef)({static:!1,hold:!1}),w=(0,N.useRef)(null),P=(0,N.useRef)(null),O=(0,N.useRef)(null),R=(0,N.useRef)(null),h=y(typeof s=="string"?($,Q)=>{let X=s;return($==null?void 0:$[X])===(Q==null?void 0:Q[X])}:s!=null?s:($,Q)=>$===Q),S=y($=>c?s===null?c.options.indexOf($):c.options.findIndex(Q=>h(Q,$)):E.options.findIndex(Q=>h(Q.dataRef.current.value,$))),G=(0,N.useCallback)($=>F(x.mode,{[1]:()=>g.some(Q=>h(Q,$)),[0]:()=>h(g,$)}),[g]),Z=y($=>E.activeOptionIndex===S($)),x=(0,N.useMemo)(()=>({...E,immediate:f,optionsPropsRef:L,labelRef:w,inputRef:P,buttonRef:O,optionsRef:R,value:g,defaultValue:r,disabled:l,mode:p?1:0,virtual:E.virtual,get activeOptionIndex(){if(U.current&&E.activeOptionIndex===null&&(c?c.options.length>0:E.options.length>0)){if(c){let Q=c.options.findIndex(X=>{var be,ye;return!((ye=(be=c==null?void 0:c.disabled)==null?void 0:be.call(c,X))!=null&&ye)});if(Q!==-1)return Q}let $=E.options.findIndex(Q=>!Q.dataRef.current.disabled);if($!==-1)return $}return E.activeOptionIndex},calculateIndex:S,compare:h,isSelected:G,isActive:Z,nullable:d,__demoMode:u}),[g,r,l,p,d,u,E,c]);I(()=>{c&&C({type:7,options:c.options})},[c,c==null?void 0:c.options]),I(()=>{E.dataRef.current=x},[x]),_e([x.buttonRef,x.inputRef,x.optionsRef],()=>q.closeCombobox(),x.comboboxState===0);let H=(0,N.useMemo)(()=>{var $,Q,X;return{open:x.comboboxState===0,disabled:l,activeIndex:x.activeOptionIndex,activeOption:x.activeOptionIndex===null?null:x.virtual?x.virtual.options[($=x.activeOptionIndex)!=null?$:0]:(X=(Q=x.options[x.activeOptionIndex])==null?void 0:Q.dataRef.current.value)!=null?X:null,value:g}},[x,l,g]),V=y(()=>{if(x.activeOptionIndex!==null){if(x.virtual)de(x.virtual.options[x.activeOptionIndex]);else{let{dataRef:$}=x.options[x.activeOptionIndex];de($.current.value)}q.goToOption(4,x.activeOptionIndex)}}),re=y(()=>{C({type:0}),U.current=!0}),A=y(()=>{C({type:1}),U.current=!1}),j=y(($,Q,X)=>(U.current=!1,$===4?C({type:2,focus:4,idx:Q,trigger:X}):C({type:2,focus:$,trigger:X}))),Y=y(($,Q)=>(C({type:3,payload:{id:$,dataRef:Q}}),()=>{x.isActive(Q.current.value)&&(U.current=!0),C({type:4,id:$})})),oe=y($=>(C({type:5,id:$}),()=>C({type:5,id:null}))),de=y($=>F(x.mode,{[0](){return v==null?void 0:v($)},[1](){let Q=x.value.slice(),X=Q.findIndex(be=>h(be,$));return X===-1?Q.push($):Q.splice(X,1),v==null?void 0:v(Q)}})),B=y($=>{C({type:6,trigger:$})}),q=(0,N.useMemo)(()=>({onChange:de,registerOption:Y,registerLabel:oe,goToOption:j,closeCombobox:A,openCombobox:re,setActivationTrigger:B,selectActiveOption:V}),[]),Te=n===null?{}:{ref:n},Oe=(0,N.useRef)(null),rt=ce();return(0,N.useEffect)(()=>{Oe.current&&r!==void 0&&rt.addEventListener(Oe.current,"reset",()=>{v==null||v(r)})},[Oe,v]),N.default.createElement(yn.Provider,{value:q},N.default.createElement(gn.Provider,{value:x},N.default.createElement(Ce,{value:F(x.comboboxState,{[0]:1,[1]:2})},a!=null&&g!=null&&qe({[a]:g}).map(([$,Q],X)=>N.default.createElement(fe,{features:4,ref:X===0?be=>{var ye;Oe.current=(ye=be==null?void 0:be.closest("form"))!=null?ye:null}:void 0,...we({key:$,as:"input",type:"hidden",hidden:!0,readOnly:!0,form:o,disabled:l,name:$,value:Q})})),D({ourProps:Te,theirProps:b,slot:H,defaultTag:Go,name:"Combobox"}))))}var jo="input";function Wo(e,n){var R,h,S,G,Z;let t=W(),{id:r=`headlessui-combobox-input-${t}`,onChange:i,displayValue:o,type:a="text",...s}=e,l=Ze("Combobox.Input"),u=St("Combobox.Input"),d=_(l.inputRef,n),p=xe(l.inputRef),m=(0,N.useRef)(!1),T=ce(),b=y(()=>{u.onChange(null),l.optionsRef.current&&(l.optionsRef.current.scrollTop=0),u.goToOption(5)}),f=function(){var x;return typeof o=="function"&&l.value!==void 0?(x=o(l.value))!=null?x:"":typeof l.value=="string"?l.value:""}();Xe(([x,H],[V,re])=>{if(m.current)return;let A=l.inputRef.current;A&&((re===0&&H===1||x!==V)&&(A.value=x),requestAnimationFrame(()=>{if(m.current||!A||(p==null?void 0:p.activeElement)!==A)return;let{selectionStart:j,selectionEnd:Y}=A;Math.abs((Y!=null?Y:0)-(j!=null?j:0))===0&&j===0&&A.setSelectionRange(A.value.length,A.value.length)}))},[f,l.comboboxState,p]),Xe(([x],[H])=>{if(x===0&&H===1){if(m.current)return;let V=l.inputRef.current;if(!V)return;let re=V.value,{selectionStart:A,selectionEnd:j,selectionDirection:Y}=V;V.value="",V.value=re,Y!==null?V.setSelectionRange(A,j,Y):V.setSelectionRange(A,j)}},[l.comboboxState]);let c=(0,N.useRef)(!1),g=y(()=>{c.current=!0}),v=y(()=>{T.nextFrame(()=>{c.current=!1})}),E=y(x=>{switch(m.current=!0,x.key){case"Enter":if(m.current=!1,l.comboboxState!==0||c.current)return;if(x.preventDefault(),x.stopPropagation(),l.activeOptionIndex===null){u.closeCombobox();return}u.selectActiveOption(),l.mode===0&&u.closeCombobox();break;case"ArrowDown":return m.current=!1,x.preventDefault(),x.stopPropagation(),F(l.comboboxState,{[0]:()=>u.goToOption(2),[1]:()=>u.openCombobox()});case"ArrowUp":return m.current=!1,x.preventDefault(),x.stopPropagation(),F(l.comboboxState,{[0]:()=>u.goToOption(1),[1]:()=>{u.openCombobox(),T.nextFrame(()=>{l.value||u.goToOption(3)})}});case"Home":if(x.shiftKey)break;return m.current=!1,x.preventDefault(),x.stopPropagation(),u.goToOption(0);case"PageUp":return m.current=!1,x.preventDefault(),x.stopPropagation(),u.goToOption(0);case"End":if(x.shiftKey)break;return m.current=!1,x.preventDefault(),x.stopPropagation(),u.goToOption(3);case"PageDown":return m.current=!1,x.preventDefault(),x.stopPropagation(),u.goToOption(3);case"Escape":return m.current=!1,l.comboboxState!==0?void 0:(x.preventDefault(),l.optionsRef.current&&!l.optionsPropsRef.current.static&&x.stopPropagation(),l.nullable&&l.mode===0&&l.value===null&&b(),u.closeCombobox());case"Tab":if(m.current=!1,l.comboboxState!==0)return;l.mode===0&&l.activationTrigger!==1&&u.selectActiveOption(),u.closeCombobox();break}}),C=y(x=>{i==null||i(x),l.nullable&&l.mode===0&&x.target.value===""&&b(),u.openCombobox()}),U=y(x=>{var V,re,A;let H=(V=x.relatedTarget)!=null?V:Ae.find(j=>j!==x.currentTarget);if(m.current=!1,!((re=l.optionsRef.current)!=null&&re.contains(H))&&!((A=l.buttonRef.current)!=null&&A.contains(H))&&l.comboboxState===0)return x.preventDefault(),l.mode===0&&(l.nullable&&l.value===null?b():l.activationTrigger!==1&&u.selectActiveOption()),u.closeCombobox()}),L=y(x=>{var V,re,A;let H=(V=x.relatedTarget)!=null?V:Ae.find(j=>j!==x.currentTarget);(re=l.buttonRef.current)!=null&&re.contains(H)||(A=l.optionsRef.current)!=null&&A.contains(H)||l.disabled||l.immediate&&l.comboboxState!==0&&(u.openCombobox(),T.nextFrame(()=>{u.setActivationTrigger(1)}))}),w=$e(()=>{if(l.labelId)return[l.labelId].join(" ")},[l.labelId]),P=(0,N.useMemo)(()=>({open:l.comboboxState===0,disabled:l.disabled}),[l]),O={ref:d,id:r,role:"combobox",type:a,"aria-controls":(R=l.optionsRef.current)==null?void 0:R.id,"aria-expanded":l.comboboxState===0,"aria-activedescendant":l.activeOptionIndex===null?void 0:l.virtual?(h=l.options.find(x=>{var H;return!((H=l.virtual)!=null&&H.disabled(x.dataRef.current.value))&&l.compare(x.dataRef.current.value,l.virtual.options[l.activeOptionIndex])}))==null?void 0:h.id:(S=l.options[l.activeOptionIndex])==null?void 0:S.id,"aria-labelledby":w,"aria-autocomplete":"list",defaultValue:(Z=(G=e.defaultValue)!=null?G:l.defaultValue!==void 0?o==null?void 0:o(l.defaultValue):null)!=null?Z:l.defaultValue,disabled:l.disabled,onCompositionStart:g,onCompositionEnd:v,onKeyDown:E,onChange:C,onFocus:L,onBlur:U};return D({ourProps:O,theirProps:s,slot:P,defaultTag:jo,name:"Combobox.Input"})}var Ko="button";function $o(e,n){var b;let t=Ze("Combobox.Button"),r=St("Combobox.Button"),i=_(t.buttonRef,n),o=W(),{id:a=`headlessui-combobox-button-${o}`,...s}=e,l=ce(),u=y(f=>{switch(f.key){case"ArrowDown":return f.preventDefault(),f.stopPropagation(),t.comboboxState===1&&r.openCombobox(),l.nextFrame(()=>{var c;return(c=t.inputRef.current)==null?void 0:c.focus({preventScroll:!0})});case"ArrowUp":return f.preventDefault(),f.stopPropagation(),t.comboboxState===1&&(r.openCombobox(),l.nextFrame(()=>{t.value||r.goToOption(3)})),l.nextFrame(()=>{var c;return(c=t.inputRef.current)==null?void 0:c.focus({preventScroll:!0})});case"Escape":return t.comboboxState!==0?void 0:(f.preventDefault(),t.optionsRef.current&&!t.optionsPropsRef.current.static&&f.stopPropagation(),r.closeCombobox(),l.nextFrame(()=>{var c;return(c=t.inputRef.current)==null?void 0:c.focus({preventScroll:!0})}));default:return}}),d=y(f=>{if(ge(f.currentTarget))return f.preventDefault();t.comboboxState===0?r.closeCombobox():(f.preventDefault(),r.openCombobox()),l.nextFrame(()=>{var c;return(c=t.inputRef.current)==null?void 0:c.focus({preventScroll:!0})})}),p=$e(()=>{if(t.labelId)return[t.labelId,a].join(" ")},[t.labelId,a]),m=(0,N.useMemo)(()=>({open:t.comboboxState===0,disabled:t.disabled,value:t.value}),[t]),T={ref:i,id:a,type:Se(e,t.buttonRef),tabIndex:-1,"aria-haspopup":"listbox","aria-controls":(b=t.optionsRef.current)==null?void 0:b.id,"aria-expanded":t.comboboxState===0,"aria-labelledby":p,disabled:t.disabled,onClick:d,onKeyDown:u};return D({ourProps:T,theirProps:s,slot:m,defaultTag:Ko,name:"Combobox.Button"})}var zo="label";function Xo(e,n){let t=W(),{id:r=`headlessui-combobox-label-${t}`,...i}=e,o=Ze("Combobox.Label"),a=St("Combobox.Label"),s=_(o.labelRef,n);I(()=>a.registerLabel(r),[r]);let l=y(()=>{var p;return(p=o.inputRef.current)==null?void 0:p.focus({preventScroll:!0})}),u=(0,N.useMemo)(()=>({open:o.comboboxState===0,disabled:o.disabled}),[o]);return D({ourProps:{ref:s,id:r,onClick:l},theirProps:i,slot:u,defaultTag:zo,name:"Combobox.Label"})}var Jo="ul",qo=3;function Yo(e,n){let t=W(),{id:r=`headlessui-combobox-options-${t}`,hold:i=!1,...o}=e,a=Ze("Combobox.Options"),s=_(a.optionsRef,n),l=Pe(),u=(()=>l!==null?(l&1)===1:a.comboboxState===0)();I(()=>{var T;a.optionsPropsRef.current.static=(T=e.static)!=null?T:!1},[a.optionsPropsRef,e.static]),I(()=>{a.optionsPropsRef.current.hold=i},[a.optionsPropsRef,i]),st({container:a.optionsRef.current,enabled:a.comboboxState===0,accept(T){return T.getAttribute("role")==="option"?NodeFilter.FILTER_REJECT:T.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(T){T.setAttribute("role","none")}});let d=$e(()=>{var T,b;return(b=a.labelId)!=null?b:(T=a.buttonRef.current)==null?void 0:T.id},[a.labelId,a.buttonRef.current]),p=(0,N.useMemo)(()=>({open:a.comboboxState===0,option:void 0}),[a]),m={"aria-labelledby":d,role:"listbox","aria-multiselectable":a.mode===1?!0:void 0,id:r,ref:s};return a.virtual&&a.comboboxState===0&&Object.assign(o,{children:N.default.createElement(Uo,null,o.children)}),D({ourProps:m,theirProps:o,slot:p,defaultTag:Jo,features:qo,visible:u,name:"Combobox.Options"})}var Qo="li";function Zo(e,n){var R;let t=W(),{id:r=`headlessui-combobox-option-${t}`,disabled:i=!1,value:o,order:a=null,...s}=e,l=Ze("Combobox.Option"),u=St("Combobox.Option"),d=l.virtual?l.activeOptionIndex===l.calculateIndex(o):l.activeOptionIndex===null?!1:((R=l.options[l.activeOptionIndex])==null?void 0:R.id)===r,p=l.isSelected(o),m=(0,N.useRef)(null),T=ee({disabled:i,value:o,domRef:m,order:a}),b=(0,N.useContext)(Er),f=_(n,m,b?b.measureElement:null),c=y(()=>u.onChange(o));I(()=>u.registerOption(r,T),[T,r]);let g=(0,N.useRef)(!(l.virtual||l.__demoMode));I(()=>{if(!l.virtual||!l.__demoMode)return;let h=le();return h.requestAnimationFrame(()=>{g.current=!0}),h.dispose},[l.virtual,l.__demoMode]),I(()=>{if(!g.current||l.comboboxState!==0||!d||l.activationTrigger===0)return;let h=le();return h.requestAnimationFrame(()=>{var S,G;(G=(S=m.current)==null?void 0:S.scrollIntoView)==null||G.call(S,{block:"nearest"})}),h.dispose},[m,d,l.comboboxState,l.activationTrigger,l.activeOptionIndex]);let v=y(h=>{var S;if(i||(S=l.virtual)!=null&&S.disabled(o))return h.preventDefault();c(),Nt()||requestAnimationFrame(()=>{var G;return(G=l.inputRef.current)==null?void 0:G.focus({preventScroll:!0})}),l.mode===0&&requestAnimationFrame(()=>u.closeCombobox())}),E=y(()=>{var S;if(i||(S=l.virtual)!=null&&S.disabled(o))return u.goToOption(5);let h=l.calculateIndex(o);u.goToOption(4,h)}),C=lt(),U=y(h=>C.update(h)),L=y(h=>{var G;if(!C.wasMoved(h)||i||(G=l.virtual)!=null&&G.disabled(o)||d)return;let S=l.calculateIndex(o);u.goToOption(4,S,0)}),w=y(h=>{var S;C.wasMoved(h)&&(i||(S=l.virtual)!=null&&S.disabled(o)||d&&(l.optionsPropsRef.current.hold||u.goToOption(5)))}),P=(0,N.useMemo)(()=>({active:d,selected:p,disabled:i}),[d,p,i]);return D({ourProps:{id:r,ref:f,role:"option",tabIndex:i===!0?void 0:-1,"aria-disabled":i===!0?!0:void 0,"aria-selected":p,disabled:void 0,onClick:v,onFocus:E,onPointerEnter:U,onMouseEnter:U,onPointerMove:L,onMouseMove:L,onPointerLeave:w,onMouseLeave:w},theirProps:s,slot:P,defaultTag:Qo,name:"Combobox.Option"})}var ei=M(Vo),ti=M($o),ni=M(Wo),ri=M(Xo),oi=M(Yo),ii=M(Zo),ai=Object.assign(ei,{Input:ni,Button:ti,Label:ri,Options:oi,Option:ii});var z=ie(require("react"),1);var He=ie(require("react"),1);var Pr=require("react");function ct(e,n,t,r){let i=ee(t);(0,Pr.useEffect)(()=>{e=e!=null?e:window;function o(a){i.current(a)}return e.addEventListener(n,o,r),()=>e.removeEventListener(n,o,r)},[e,n,r])}var Rr=require("react");function Me(){let e=(0,Rr.useRef)(!1);return I(()=>(e.current=!0,()=>{e.current=!1}),[]),e}var Wt=require("react");function Kt(e){let n=y(e),t=(0,Wt.useRef)(!1);(0,Wt.useEffect)(()=>(t.current=!1,()=>{t.current=!0,Ne(()=>{t.current&&n()})}),[n])}var hr=require("react");function Ot(){let e=(0,hr.useRef)(0);return Ut("keydown",n=>{n.key==="Tab"&&(e.current=n.shiftKey?1:0)},!0),e}function Sr(e){if(!e)return new Set;if(typeof e=="function")return new Set(e());let n=new Set;for(let t of e.current)t.current instanceof HTMLElement&&n.add(t.current);return n}var si="div",Or=(a=>(a[a.None=1]="None",a[a.InitialFocus=2]="InitialFocus",a[a.TabLock=4]="TabLock",a[a.FocusLock=8]="FocusLock",a[a.RestoreFocus=16]="RestoreFocus",a[a.All=30]="All",a))(Or||{});function ui(e,n){let t=(0,He.useRef)(null),r=_(t,n),{initialFocus:i,containers:o,features:a=30,...s}=e;De()||(a=1);let l=xe(t);ci({ownerDocument:l},Boolean(a&16));let u=fi({ownerDocument:l,container:t,initialFocus:i},Boolean(a&2));mi({ownerDocument:l,container:t,containers:o,previousActiveElement:u},Boolean(a&8));let d=Ot(),p=y(f=>{let c=t.current;if(!c)return;(v=>v())(()=>{F(d.current,{[0]:()=>{pe(c,1,{skipElements:[f.relatedTarget]})},[1]:()=>{pe(c,8,{skipElements:[f.relatedTarget]})}})})}),m=ce(),T=(0,He.useRef)(!1),b={ref:r,onKeyDown(f){f.key=="Tab"&&(T.current=!0,m.requestAnimationFrame(()=>{T.current=!1}))},onBlur(f){let c=Sr(o);t.current instanceof HTMLElement&&c.add(t.current);let g=f.relatedTarget;g instanceof HTMLElement&&g.dataset.headlessuiFocusGuard!=="true"&&(Cr(c,g)||(T.current?pe(t.current,F(d.current,{[0]:()=>4,[1]:()=>2})|16,{relativeTo:f.target}):f.target instanceof HTMLElement&&Be(f.target)))}};return He.default.createElement(He.default.Fragment,null,Boolean(a&4)&&He.default.createElement(fe,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:p,features:2}),D({ourProps:b,theirProps:s,defaultTag:si,name:"FocusTrap"}),Boolean(a&4)&&He.default.createElement(fe,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:p,features:2}))}var pi=M(ui),et=Object.assign(pi,{features:Or});function di(e=!0){let n=(0,He.useRef)(Ae.slice());return Xe(([t],[r])=>{r===!0&&t===!1&&Ne(()=>{n.current.splice(0)}),r===!1&&t===!0&&(n.current=Ae.slice())},[e,Ae,n]),y(()=>{var t;return(t=n.current.find(r=>r!=null&&r.isConnected))!=null?t:null})}function ci({ownerDocument:e},n){let t=di(n);Xe(()=>{n||(e==null?void 0:e.activeElement)===(e==null?void 0:e.body)&&Be(t())},[n]),Kt(()=>{n&&Be(t())})}function fi({ownerDocument:e,container:n,initialFocus:t},r){let i=(0,He.useRef)(null),o=Me();return Xe(()=>{if(!r)return;let a=n.current;a&&Ne(()=>{if(!o.current)return;let s=e==null?void 0:e.activeElement;if(t!=null&&t.current){if((t==null?void 0:t.current)===s){i.current=s;return}}else if(a.contains(s)){i.current=s;return}t!=null&&t.current?Be(t.current):pe(a,1)===0&&console.warn("There are no focusable elements inside the <FocusTrap />"),i.current=e==null?void 0:e.activeElement})},[r]),i}function mi({ownerDocument:e,container:n,containers:t,previousActiveElement:r},i){let o=Me();ct(e==null?void 0:e.defaultView,"focus",a=>{if(!i||!o.current)return;let s=Sr(t);n.current instanceof HTMLElement&&s.add(n.current);let l=r.current;if(!l)return;let u=a.target;u&&u instanceof HTMLElement?Cr(s,u)?(r.current=u,Be(u)):(a.preventDefault(),a.stopPropagation(),Be(l)):Be(r.current)},!0)}function Cr(e,n){for(let t of e)if(t.contains(n))return!0;return!1}var se=ie(require("react"),1),Dr=require("react-dom");var ft=ie(require("react"),1),Ar=(0,ft.createContext)(!1);function Lr(){return(0,ft.useContext)(Ar)}function zt(e){return ft.default.createElement(Ar.Provider,{value:e.force},e.children)}function Ti(e){let n=Lr(),t=(0,se.useContext)(Mr),r=xe(e),[i,o]=(0,se.useState)(()=>{if(!n&&t!==null||he.isServer)return null;let a=r==null?void 0:r.getElementById("headlessui-portal-root");if(a)return a;if(r===null)return null;let s=r.createElement("div");return s.setAttribute("id","headlessui-portal-root"),r.body.appendChild(s)});return(0,se.useEffect)(()=>{i!==null&&(r!=null&&r.body.contains(i)||r==null||r.body.appendChild(i))},[i,r]),(0,se.useEffect)(()=>{n||t!==null&&o(t.current)},[t,o,n]),i}var bi=se.Fragment;function yi(e,n){let t=e,r=(0,se.useRef)(null),i=_(at(p=>{r.current=p}),n),o=xe(r),a=Ti(r),[s]=(0,se.useState)(()=>{var p;return he.isServer?null:(p=o==null?void 0:o.createElement("div"))!=null?p:null}),l=(0,se.useContext)(xn),u=De();return I(()=>{!a||!s||a.contains(s)||(s.setAttribute("data-headlessui-portal",""),a.appendChild(s))},[a,s]),I(()=>{if(s&&l)return l.register(s)},[l,s]),Kt(()=>{var p;!a||!s||(s instanceof Node&&a.contains(s)&&a.removeChild(s),a.childNodes.length<=0&&((p=a.parentElement)==null||p.removeChild(a)))}),u?!a||!s?null:(0,Dr.createPortal)(D({ourProps:{ref:i},theirProps:t,defaultTag:bi,name:"Portal"}),s):null}var gi=se.Fragment,Mr=(0,se.createContext)(null);function vi(e,n){let{target:t,...r}=e,o={ref:_(n)};return se.default.createElement(Mr.Provider,{value:t},D({ourProps:o,theirProps:r,defaultTag:gi,name:"Popover.Group"}))}var xn=(0,se.createContext)(null);function Xt(){let e=(0,se.useContext)(xn),n=(0,se.useRef)([]),t=y(o=>(n.current.push(o),e&&e.register(o),()=>r(o))),r=y(o=>{let a=n.current.indexOf(o);a!==-1&&n.current.splice(a,1),e&&e.unregister(o)}),i=(0,se.useMemo)(()=>({register:t,unregister:r,portals:n}),[t,r,n]);return[n,(0,se.useMemo)(()=>function({children:a}){return se.default.createElement(xn.Provider,{value:i},a)},[i])]}var xi=M(yi),Ei=M(vi),mt=Object.assign(xi,{Group:Ei});var _r=ie(require("react"),1);var Pi=ie(require("react"),1);function Ri(e,n){return e===n&&(e!==0||1/e===1/n)||e!==e&&n!==n}var hi=typeof Object.is=="function"?Object.is:Ri,{useState:Si,useEffect:Oi,useLayoutEffect:Ci,useDebugValue:Ai}=Pi;function Ir(e,n,t){let r=n(),[{inst:i},o]=Si({inst:{value:r,getSnapshot:n}});return Ci(()=>{i.value=r,i.getSnapshot=n,En(i)&&o({inst:i})},[e,r,n]),Oi(()=>(En(i)&&o({inst:i}),e(()=>{En(i)&&o({inst:i})})),[e]),Ai(r),r}function En(e){let n=e.getSnapshot,t=e.value;try{let r=n();return!hi(t,r)}catch{return!0}}function Fr(e,n,t){return n()}var Li=typeof window!="undefined"&&typeof window.document!="undefined"&&typeof window.document.createElement!="undefined",Di=!Li,Mi=Di?Fr:Ir,wr="useSyncExternalStore"in _r?(e=>e.useSyncExternalStore)(_r):Mi;function Hr(e){return wr(e.subscribe,e.getSnapshot,e.getSnapshot)}function kr(e,n){let t=e(),r=new Set;return{getSnapshot(){return t},subscribe(i){return r.add(i),()=>r.delete(i)},dispatch(i,...o){let a=n[i].call(t,...o);a&&(t=a,r.forEach(s=>s()))}}}function Nr(){let e;return{before({doc:n}){var i;let t=n.documentElement;e=((i=n.defaultView)!=null?i:window).innerWidth-t.clientWidth},after({doc:n,d:t}){let r=n.documentElement,i=r.clientWidth-r.offsetWidth,o=e-i;t.style(r,"paddingRight",`${o}px`)}}}function Ur(){return cn()?{before({doc:e,d:n,meta:t}){function r(i){return t.containers.flatMap(o=>o()).some(o=>o.contains(i))}n.microTask(()=>{var a;if(window.getComputedStyle(e.documentElement).scrollBehavior!=="auto"){let s=le();s.style(e.documentElement,"scrollBehavior","auto"),n.add(()=>n.microTask(()=>s.dispose()))}let i=(a=window.scrollY)!=null?a:window.pageYOffset,o=null;n.addEventListener(e,"click",s=>{if(s.target instanceof HTMLElement)try{let l=s.target.closest("a");if(!l)return;let{hash:u}=new URL(l.href),d=e.querySelector(u);d&&!r(d)&&(o=d)}catch{}},!0),n.addEventListener(e,"touchstart",s=>{if(s.target instanceof HTMLElement)if(r(s.target)){let l=s.target;for(;l.parentElement&&r(l.parentElement);)l=l.parentElement;n.style(l,"overscrollBehavior","contain")}else n.style(s.target,"touchAction","none")}),n.addEventListener(e,"touchmove",s=>{if(s.target instanceof HTMLElement)if(r(s.target)){let l=s.target;for(;l.parentElement&&l.dataset.headlessuiPortal!==""&&!(l.scrollHeight>l.clientHeight||l.scrollWidth>l.clientWidth);)l=l.parentElement;l.dataset.headlessuiPortal===""&&s.preventDefault()}else s.preventDefault()},{passive:!1}),n.add(()=>{var l;let s=(l=window.scrollY)!=null?l:window.pageYOffset;i!==s&&window.scrollTo(0,i),o&&o.isConnected&&(o.scrollIntoView({block:"nearest"}),o=null)})})}}:{}}function Br(){return{before({doc:e,d:n}){n.style(e.documentElement,"overflow","hidden")}}}function Ii(e){let n={};for(let t of e)Object.assign(n,t(n));return n}var je=kr(()=>new Map,{PUSH(e,n){var r;let t=(r=this.get(e))!=null?r:{doc:e,count:0,d:le(),meta:new Set};return t.count++,t.meta.add(n),this.set(e,t),this},POP(e,n){let t=this.get(e);return t&&(t.count--,t.meta.delete(n)),this},SCROLL_PREVENT({doc:e,d:n,meta:t}){let r={doc:e,d:n,meta:Ii(t)},i=[Ur(),Nr(),Br()];i.forEach(({before:o})=>o==null?void 0:o(r)),i.forEach(({after:o})=>o==null?void 0:o(r))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});je.subscribe(()=>{let e=je.getSnapshot(),n=new Map;for(let[t]of e)n.set(t,t.documentElement.style.overflow);for(let t of e.values()){let r=n.get(t.doc)==="hidden",i=t.count!==0;(i&&!r||!i&&r)&&je.dispatch(t.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",t),t.count===0&&je.dispatch("TEARDOWN",t)}});function Gr(e,n,t){let r=Hr(je),i=e?r.get(e):void 0,o=i?i.count>0:!1;return I(()=>{if(!(!e||!n))return je.dispatch("PUSH",e,t),()=>je.dispatch("POP",e,t)},[n,e]),o}var Pn=new Map,Ct=new Map;function Rn(e,n=!0){I(()=>{var o;if(!n)return;let t=typeof e=="function"?e():e.current;if(!t)return;function r(){var l;if(!t)return;let a=(l=Ct.get(t))!=null?l:1;if(a===1?Ct.delete(t):Ct.set(t,a-1),a!==1)return;let s=Pn.get(t);s&&(s["aria-hidden"]===null?t.removeAttribute("aria-hidden"):t.setAttribute("aria-hidden",s["aria-hidden"]),t.inert=s.inert,Pn.delete(t))}let i=(o=Ct.get(t))!=null?o:0;return Ct.set(t,i+1),i!==0||(Pn.set(t,{"aria-hidden":t.getAttribute("aria-hidden"),inert:t.inert}),t.setAttribute("aria-hidden","true"),t.inert=!0),r},[e,n])}var Ge=ie(require("react"),1);function Jt({defaultContainers:e=[],portals:n,mainTreeNodeRef:t}={}){var a;let r=(0,Ge.useRef)((a=t==null?void 0:t.current)!=null?a:null),i=xe(r),o=y(()=>{var l,u,d;let s=[];for(let p of e)p!==null&&(p instanceof HTMLElement?s.push(p):"current"in p&&p.current instanceof HTMLElement&&s.push(p.current));if(n!=null&&n.current)for(let p of n.current)s.push(p);for(let p of(l=i==null?void 0:i.querySelectorAll("html > *, body > *"))!=null?l:[])p!==document.body&&p!==document.head&&p instanceof HTMLElement&&p.id!=="headlessui-portal-root"&&(p.contains(r.current)||p.contains((d=(u=r.current)==null?void 0:u.getRootNode())==null?void 0:d.host)||s.some(m=>p.contains(m))||s.push(p));return s});return{resolveContainers:o,contains:y(s=>o().some(l=>l.contains(s))),mainTreeNodeRef:r,MainTreeNode:(0,Ge.useMemo)(()=>function(){return t!=null?null:Ge.default.createElement(fe,{features:4,ref:r})},[r,t])}}function Vr(){let e=(0,Ge.useRef)(null);return{mainTreeNodeRef:e,MainTreeNode:(0,Ge.useMemo)(()=>function(){return Ge.default.createElement(fe,{features:4,ref:e})},[e])}}var Tt=ie(require("react"),1);var hn=(0,Tt.createContext)(()=>{});hn.displayName="StackContext";function Fi(){return(0,Tt.useContext)(hn)}function jr({children:e,onUpdate:n,type:t,element:r,enabled:i}){let o=Fi(),a=y((...s)=>{n==null||n(...s),o(...s)});return I(()=>{let s=i===void 0||i===!0;return s&&a(0,t,r),()=>{s&&a(1,t,r)}},[a,t,r,i]),Tt.default.createElement(hn.Provider,{value:a},e)}var Ie=ie(require("react"),1);var Wr=(0,Ie.createContext)(null);function Kr(){let e=(0,Ie.useContext)(Wr);if(e===null){let n=new Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(n,Kr),n}return e}function tt(){let[e,n]=(0,Ie.useState)([]);return[e.length>0?e.join(" "):void 0,(0,Ie.useMemo)(()=>function(r){let i=y(a=>(n(s=>[...s,a]),()=>n(s=>{let l=s.slice(),u=l.indexOf(a);return u!==-1&&l.splice(u,1),l}))),o=(0,Ie.useMemo)(()=>({register:i,slot:r.slot,name:r.name,props:r.props}),[i,r.slot,r.name,r.props]);return Ie.default.createElement(Wr.Provider,{value:o},r.children)},[n])]}var _i="p";function wi(e,n){let t=W(),{id:r=`headlessui-description-${t}`,...i}=e,o=Kr(),a=_(n);I(()=>o.register(r),[r,o.register]);let s={ref:a,...o.props,id:r};return D({ourProps:s,theirProps:i,slot:o.slot||{},defaultTag:_i,name:o.name||"Description"})}var Hi=M(wi),bt=Object.assign(Hi,{});var ki={[0](e,n){return e.titleId===n.id?e:{...e,titleId:n.id}}},qt=(0,z.createContext)(null);qt.displayName="DialogContext";function At(e){let n=(0,z.useContext)(qt);if(n===null){let t=new Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,At),t}return n}function Ni(e,n,t=()=>[document.body]){Gr(e,n,r=>{var i;return{containers:[...(i=r.containers)!=null?i:[],t]}})}function Ui(e,n){return F(n.type,ki,e,n)}var Bi="div",Gi=3;function Vi(e,n){let t=W(),{id:r=`headlessui-dialog-${t}`,open:i,onClose:o,initialFocus:a,role:s="dialog",__demoMode:l=!1,...u}=e,[d,p]=(0,z.useState)(0),m=(0,z.useRef)(!1);s=function(){return s==="dialog"||s==="alertdialog"?s:(m.current||(m.current=!0,console.warn(`Invalid role [${s}] passed to <Dialog />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)),"dialog")}();let T=Pe();i===void 0&&T!==null&&(i=(T&1)===1);let b=(0,z.useRef)(null),f=_(b,n),c=xe(b),g=e.hasOwnProperty("open")||T!==null,v=e.hasOwnProperty("onClose");if(!g&&!v)throw new Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!g)throw new Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!v)throw new Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if(typeof i!="boolean")throw new Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${i}`);if(typeof o!="function")throw new Error(`You provided an \`onClose\` prop to the \`Dialog\`, but the value is not a function. Received: ${o}`);let E=i?0:1,[C,U]=(0,z.useReducer)(Ui,{titleId:null,descriptionId:null,panelRef:(0,z.createRef)()}),L=y(()=>o(!1)),w=y(X=>U({type:0,id:X})),O=De()?l?!1:E===0:!1,R=d>1,h=(0,z.useContext)(qt)!==null,[S,G]=Xt(),Z={get current(){var X;return(X=C.panelRef.current)!=null?X:b.current}},{resolveContainers:x,mainTreeNodeRef:H,MainTreeNode:V}=Jt({portals:S,defaultContainers:[Z]}),re=R?"parent":"leaf",A=T!==null?(T&4)===4:!1,j=(()=>h||A?!1:O)(),Y=(0,z.useCallback)(()=>{var X,be;return(be=Array.from((X=c==null?void 0:c.querySelectorAll("body > *"))!=null?X:[]).find(ye=>ye.id==="headlessui-portal-root"?!1:ye.contains(H.current)&&ye instanceof HTMLElement))!=null?be:null},[H]);Rn(Y,j);let oe=(()=>R?!0:O)(),de=(0,z.useCallback)(()=>{var X,be;return(be=Array.from((X=c==null?void 0:c.querySelectorAll("[data-headlessui-portal]"))!=null?X:[]).find(ye=>ye.contains(H.current)&&ye instanceof HTMLElement))!=null?be:null},[H]);Rn(de,oe);let B=(()=>!(!O||R))();_e(x,X=>{X.preventDefault(),L()},B);let q=(()=>!(R||E!==0))();ct(c==null?void 0:c.defaultView,"keydown",X=>{q&&(X.defaultPrevented||X.key==="Escape"&&(X.preventDefault(),X.stopPropagation(),L()))});let Te=(()=>!(A||E!==0||h))();Ni(c,Te,x),(0,z.useEffect)(()=>{if(E!==0||!b.current)return;let X=new ResizeObserver(be=>{for(let ye of be){let Ft=ye.target.getBoundingClientRect();Ft.x===0&&Ft.y===0&&Ft.width===0&&Ft.height===0&&L()}});return X.observe(b.current),()=>X.disconnect()},[E,b,L]);let[Oe,rt]=tt(),It=(0,z.useMemo)(()=>[{dialogState:E,close:L,setTitleId:w},C],[E,C,L,w]),$=(0,z.useMemo)(()=>({open:E===0}),[E]),Q={ref:f,id:r,role:s,"aria-modal":E===0?!0:void 0,"aria-labelledby":C.titleId,"aria-describedby":Oe};return z.default.createElement(jr,{type:"Dialog",enabled:E===0,element:b,onUpdate:y((X,be)=>{be==="Dialog"&&F(X,{[0]:()=>p(ye=>ye+1),[1]:()=>p(ye=>ye-1)})})},z.default.createElement(zt,{force:!0},z.default.createElement(mt,null,z.default.createElement(qt.Provider,{value:It},z.default.createElement(mt.Group,{target:b},z.default.createElement(zt,{force:!1},z.default.createElement(rt,{slot:$,name:"Dialog.Description"},z.default.createElement(et,{initialFocus:a,containers:x,features:O?F(re,{parent:et.features.RestoreFocus,leaf:et.features.All&~et.features.FocusLock}):et.features.None},z.default.createElement(G,null,D({ourProps:Q,theirProps:u,slot:$,defaultTag:Bi,features:Gi,visible:E===0,name:"Dialog"}))))))))),z.default.createElement(V,null))}var ji="div";function Wi(e,n){let t=W(),{id:r=`headlessui-dialog-overlay-${t}`,...i}=e,[{dialogState:o,close:a}]=At("Dialog.Overlay"),s=_(n),l=y(p=>{if(p.target===p.currentTarget){if(ge(p.currentTarget))return p.preventDefault();p.preventDefault(),p.stopPropagation(),a()}}),u=(0,z.useMemo)(()=>({open:o===0}),[o]);return D({ourProps:{ref:s,id:r,"aria-hidden":!0,onClick:l},theirProps:i,slot:u,defaultTag:ji,name:"Dialog.Overlay"})}var Ki="div";function $i(e,n){let t=W(),{id:r=`headlessui-dialog-backdrop-${t}`,...i}=e,[{dialogState:o},a]=At("Dialog.Backdrop"),s=_(n);(0,z.useEffect)(()=>{if(a.panelRef.current===null)throw new Error("A <Dialog.Backdrop /> component is being used, but a <Dialog.Panel /> component is missing.")},[a.panelRef]);let l=(0,z.useMemo)(()=>({open:o===0}),[o]);return z.default.createElement(zt,{force:!0},z.default.createElement(mt,null,D({ourProps:{ref:s,id:r,"aria-hidden":!0},theirProps:i,slot:l,defaultTag:Ki,name:"Dialog.Backdrop"})))}var zi="div";function Xi(e,n){let t=W(),{id:r=`headlessui-dialog-panel-${t}`,...i}=e,[{dialogState:o},a]=At("Dialog.Panel"),s=_(n,a.panelRef),l=(0,z.useMemo)(()=>({open:o===0}),[o]),u=y(p=>{p.stopPropagation()});return D({ourProps:{ref:s,id:r,onClick:u},theirProps:i,slot:l,defaultTag:zi,name:"Dialog.Panel"})}var Ji="h2";function qi(e,n){let t=W(),{id:r=`headlessui-dialog-title-${t}`,...i}=e,[{dialogState:o,setTitleId:a}]=At("Dialog.Title"),s=_(n);(0,z.useEffect)(()=>(a(r),()=>a(null)),[r,a]);let l=(0,z.useMemo)(()=>({open:o===0}),[o]);return D({ourProps:{ref:s,id:r},theirProps:i,slot:l,defaultTag:Ji,name:"Dialog.Title"})}var Yi=M(Vi),Qi=M($i),Zi=M(Xi),ea=M(Wi),ta=M(qi),na=Object.assign(Yi,{Backdrop:Qi,Panel:Zi,Overlay:ea,Title:ta,Description:bt});var te=ie(require("react"),1);var zr=ie(require("react"),1),$r,Xr=($r=zr.default.startTransition)!=null?$r:function(n){n()};var ra={[0]:e=>({...e,disclosureState:F(e.disclosureState,{[0]:1,[1]:0})}),[1]:e=>e.disclosureState===1?e:{...e,disclosureState:1},[4](e){return e.linkedPanel===!0?e:{...e,linkedPanel:!0}},[5](e){return e.linkedPanel===!1?e:{...e,linkedPanel:!1}},[2](e,n){return e.buttonId===n.buttonId?e:{...e,buttonId:n.buttonId}},[3](e,n){return e.panelId===n.panelId?e:{...e,panelId:n.panelId}}},Sn=(0,te.createContext)(null);Sn.displayName="DisclosureContext";function On(e){let n=(0,te.useContext)(Sn);if(n===null){let t=new Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,On),t}return n}var Cn=(0,te.createContext)(null);Cn.displayName="DisclosureAPIContext";function Jr(e){let n=(0,te.useContext)(Cn);if(n===null){let t=new Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Jr),t}return n}var An=(0,te.createContext)(null);An.displayName="DisclosurePanelContext";function oa(){return(0,te.useContext)(An)}function ia(e,n){return F(n.type,ra,e,n)}var aa=te.Fragment;function la(e,n){let{defaultOpen:t=!1,...r}=e,i=(0,te.useRef)(null),o=_(n,at(c=>{i.current=c},e.as===void 0||e.as===te.Fragment)),a=(0,te.useRef)(null),s=(0,te.useRef)(null),l=(0,te.useReducer)(ia,{disclosureState:t?0:1,linkedPanel:!1,buttonRef:s,panelRef:a,buttonId:null,panelId:null}),[{disclosureState:u,buttonId:d},p]=l,m=y(c=>{p({type:1});let g=ve(i);if(!g||!d)return;let v=(()=>c?c instanceof HTMLElement?c:c.current instanceof HTMLElement?c.current:g.getElementById(d):g.getElementById(d))();v==null||v.focus()}),T=(0,te.useMemo)(()=>({close:m}),[m]),b=(0,te.useMemo)(()=>({open:u===0,close:m}),[u,m]),f={ref:o};return te.default.createElement(Sn.Provider,{value:l},te.default.createElement(Cn.Provider,{value:T},te.default.createElement(Ce,{value:F(u,{[0]:1,[1]:2})},D({ourProps:f,theirProps:r,slot:b,defaultTag:aa,name:"Disclosure"}))))}var sa="button";function ua(e,n){let t=W(),{id:r=`headlessui-disclosure-button-${t}`,...i}=e,[o,a]=On("Disclosure.Button"),s=oa(),l=s===null?!1:s===o.panelId,u=(0,te.useRef)(null),d=_(u,n,l?null:o.buttonRef),p=ht();(0,te.useEffect)(()=>{if(!l)return a({type:2,buttonId:r}),()=>{a({type:2,buttonId:null})}},[r,a,l]);let m=y(v=>{var E;if(l){if(o.disclosureState===1)return;switch(v.key){case" ":case"Enter":v.preventDefault(),v.stopPropagation(),a({type:0}),(E=o.buttonRef.current)==null||E.focus();break}}else switch(v.key){case" ":case"Enter":v.preventDefault(),v.stopPropagation(),a({type:0});break}}),T=y(v=>{switch(v.key){case" ":v.preventDefault();break}}),b=y(v=>{var E;ge(v.currentTarget)||e.disabled||(l?(a({type:0}),(E=o.buttonRef.current)==null||E.focus()):a({type:0}))}),f=(0,te.useMemo)(()=>({open:o.disclosureState===0}),[o]),c=Se(e,u),g=l?{ref:d,type:c,onKeyDown:m,onClick:b}:{ref:d,id:r,type:c,"aria-expanded":o.disclosureState===0,"aria-controls":o.linkedPanel?o.panelId:void 0,onKeyDown:m,onKeyUp:T,onClick:b};return D({mergeRefs:p,ourProps:g,theirProps:i,slot:f,defaultTag:sa,name:"Disclosure.Button"})}var pa="div",da=3;function ca(e,n){let t=W(),{id:r=`headlessui-disclosure-panel-${t}`,...i}=e,[o,a]=On("Disclosure.Panel"),{close:s}=Jr("Disclosure.Panel"),l=ht(),u=_(n,o.panelRef,b=>{Xr(()=>a({type:b?4:5}))});(0,te.useEffect)(()=>(a({type:3,panelId:r}),()=>{a({type:3,panelId:null})}),[r,a]);let d=Pe(),p=(()=>d!==null?(d&1)===1:o.disclosureState===0)(),m=(0,te.useMemo)(()=>({open:o.disclosureState===0,close:s}),[o,s]),T={ref:u,id:r};return te.default.createElement(An.Provider,{value:o.panelId},D({mergeRefs:l,ourProps:T,theirProps:i,slot:m,defaultTag:pa,features:da,visible:p,name:"Disclosure.Panel"}))}var fa=M(la),ma=M(ua),Ta=M(ca),ba=Object.assign(fa,{Button:ma,Panel:Ta});var J=ie(require("react"),1);var Ln=require("react");var qr=/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g;function Yr(e){var o,a;let n=(o=e.innerText)!=null?o:"",t=e.cloneNode(!0);if(!(t instanceof HTMLElement))return n;let r=!1;for(let s of t.querySelectorAll('[hidden],[aria-hidden],[role="img"]'))s.remove(),r=!0;let i=r?(a=t.innerText)!=null?a:"":n;return qr.test(i)&&(i=i.replace(qr,"")),i}function Qr(e){let n=e.getAttribute("aria-label");if(typeof n=="string")return n.trim();let t=e.getAttribute("aria-labelledby");if(t){let r=t.split(" ").map(i=>{let o=document.getElementById(i);if(o){let a=o.getAttribute("aria-label");return typeof a=="string"?a.trim():Yr(o).trim()}return null}).filter(Boolean);if(r.length>0)return r.join(", ")}return Yr(e).trim()}function Yt(e){let n=(0,Ln.useRef)(""),t=(0,Ln.useRef)("");return y(()=>{let r=e.current;if(!r)return"";let i=r.innerText;if(n.current===i)return t.current;let o=Qr(r).trim().toLowerCase();return n.current=i,t.current=o,o})}function Dn(e,n=t=>t){let t=e.activeOptionIndex!==null?e.options[e.activeOptionIndex]:null,r=Re(n(e.options.slice()),o=>o.dataRef.current.domRef.current),i=t?r.indexOf(t):null;return i===-1&&(i=null),{options:r,activeOptionIndex:i}}var ya={[1](e){return e.dataRef.current.disabled||e.listboxState===1?e:{...e,activeOptionIndex:null,listboxState:1}},[0](e){if(e.dataRef.current.disabled||e.listboxState===0)return e;let n=e.activeOptionIndex,{isSelected:t}=e.dataRef.current,r=e.options.findIndex(i=>t(i.dataRef.current.value));return r!==-1&&(n=r),{...e,listboxState:0,activeOptionIndex:n}},[2](e,n){var i;if(e.dataRef.current.disabled||e.listboxState===1)return e;let t=Dn(e),r=Je(n,{resolveItems:()=>t.options,resolveActiveIndex:()=>t.activeOptionIndex,resolveId:o=>o.id,resolveDisabled:o=>o.dataRef.current.disabled});return{...e,...t,searchQuery:"",activeOptionIndex:r,activationTrigger:(i=n.trigger)!=null?i:1}},[3]:(e,n)=>{if(e.dataRef.current.disabled||e.listboxState===1)return e;let r=e.searchQuery!==""?0:1,i=e.searchQuery+n.value.toLowerCase(),a=(e.activeOptionIndex!==null?e.options.slice(e.activeOptionIndex+r).concat(e.options.slice(0,e.activeOptionIndex+r)):e.options).find(l=>{var u;return!l.dataRef.current.disabled&&((u=l.dataRef.current.textValue)==null?void 0:u.startsWith(i))}),s=a?e.options.indexOf(a):-1;return s===-1||s===e.activeOptionIndex?{...e,searchQuery:i}:{...e,searchQuery:i,activeOptionIndex:s,activationTrigger:1}},[4](e){return e.dataRef.current.disabled||e.listboxState===1||e.searchQuery===""?e:{...e,searchQuery:""}},[5]:(e,n)=>{let t={id:n.id,dataRef:n.dataRef},r=Dn(e,i=>[...i,t]);return e.activeOptionIndex===null&&e.dataRef.current.isSelected(n.dataRef.current.value)&&(r.activeOptionIndex=r.options.indexOf(t)),{...e,...r}},[6]:(e,n)=>{let t=Dn(e,r=>{let i=r.findIndex(o=>o.id===n.id);return i!==-1&&r.splice(i,1),r});return{...e,...t,activationTrigger:1}},[7]:(e,n)=>({...e,labelId:n.id})},Mn=(0,J.createContext)(null);Mn.displayName="ListboxActionsContext";function Lt(e){let n=(0,J.useContext)(Mn);if(n===null){let t=new Error(`<${e} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Lt),t}return n}var In=(0,J.createContext)(null);In.displayName="ListboxDataContext";function Dt(e){let n=(0,J.useContext)(In);if(n===null){let t=new Error(`<${e} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Dt),t}return n}function ga(e,n){return F(n.type,ya,e,n)}var va=J.Fragment;function xa(e,n){let{value:t,defaultValue:r,form:i,name:o,onChange:a,by:s=(B,q)=>B===q,disabled:l=!1,horizontal:u=!1,multiple:d=!1,...p}=e,m=u?"horizontal":"vertical",T=_(n),[b=d?[]:void 0,f]=Ve(t,a,r),[c,g]=(0,J.useReducer)(ga,{dataRef:(0,J.createRef)(),listboxState:1,options:[],searchQuery:"",labelId:null,activeOptionIndex:null,activationTrigger:1}),v=(0,J.useRef)({static:!1,hold:!1}),E=(0,J.useRef)(null),C=(0,J.useRef)(null),U=(0,J.useRef)(null),L=y(typeof s=="string"?(B,q)=>{let Te=s;return(B==null?void 0:B[Te])===(q==null?void 0:q[Te])}:s),w=(0,J.useCallback)(B=>F(P.mode,{[1]:()=>b.some(q=>L(q,B)),[0]:()=>L(b,B)}),[b]),P=(0,J.useMemo)(()=>({...c,value:b,disabled:l,mode:d?1:0,orientation:m,compare:L,isSelected:w,optionsPropsRef:v,labelRef:E,buttonRef:C,optionsRef:U}),[b,l,d,c]);I(()=>{c.dataRef.current=P},[P]),_e([P.buttonRef,P.optionsRef],(B,q)=>{var Te;g({type:1}),Ue(q,1)||(B.preventDefault(),(Te=P.buttonRef.current)==null||Te.focus())},P.listboxState===0);let O=(0,J.useMemo)(()=>({open:P.listboxState===0,disabled:l,value:b}),[P,l,b]),R=y(B=>{let q=P.options.find(Te=>Te.id===B);q&&V(q.dataRef.current.value)}),h=y(()=>{if(P.activeOptionIndex!==null){let{dataRef:B,id:q}=P.options[P.activeOptionIndex];V(B.current.value),g({type:2,focus:4,id:q})}}),S=y(()=>g({type:0})),G=y(()=>g({type:1})),Z=y((B,q,Te)=>B===4?g({type:2,focus:4,id:q,trigger:Te}):g({type:2,focus:B,trigger:Te})),x=y((B,q)=>(g({type:5,id:B,dataRef:q}),()=>g({type:6,id:B}))),H=y(B=>(g({type:7,id:B}),()=>g({type:7,id:null}))),V=y(B=>F(P.mode,{[0](){return f==null?void 0:f(B)},[1](){let q=P.value.slice(),Te=q.findIndex(Oe=>L(Oe,B));return Te===-1?q.push(B):q.splice(Te,1),f==null?void 0:f(q)}})),re=y(B=>g({type:3,value:B})),A=y(()=>g({type:4})),j=(0,J.useMemo)(()=>({onChange:V,registerOption:x,registerLabel:H,goToOption:Z,closeListbox:G,openListbox:S,selectActiveOption:h,selectOption:R,search:re,clearSearch:A}),[]),Y={ref:T},oe=(0,J.useRef)(null),de=ce();return(0,J.useEffect)(()=>{oe.current&&r!==void 0&&de.addEventListener(oe.current,"reset",()=>{f==null||f(r)})},[oe,f]),J.default.createElement(Mn.Provider,{value:j},J.default.createElement(In.Provider,{value:P},J.default.createElement(Ce,{value:F(P.listboxState,{[0]:1,[1]:2})},o!=null&&b!=null&&qe({[o]:b}).map(([B,q],Te)=>J.default.createElement(fe,{features:4,ref:Te===0?Oe=>{var rt;oe.current=(rt=Oe==null?void 0:Oe.closest("form"))!=null?rt:null}:void 0,...we({key:B,as:"input",type:"hidden",hidden:!0,readOnly:!0,form:i,disabled:l,name:B,value:q})})),D({ourProps:Y,theirProps:p,slot:O,defaultTag:va,name:"Listbox"}))))}var Ea="button";function Pa(e,n){var f;let t=W(),{id:r=`headlessui-listbox-button-${t}`,...i}=e,o=Dt("Listbox.Button"),a=Lt("Listbox.Button"),s=_(o.buttonRef,n),l=ce(),u=y(c=>{switch(c.key){case" ":case"Enter":case"ArrowDown":c.preventDefault(),a.openListbox(),l.nextFrame(()=>{o.value||a.goToOption(0)});break;case"ArrowUp":c.preventDefault(),a.openListbox(),l.nextFrame(()=>{o.value||a.goToOption(3)});break}}),d=y(c=>{switch(c.key){case" ":c.preventDefault();break}}),p=y(c=>{if(ge(c.currentTarget))return c.preventDefault();o.listboxState===0?(a.closeListbox(),l.nextFrame(()=>{var g;return(g=o.buttonRef.current)==null?void 0:g.focus({preventScroll:!0})})):(c.preventDefault(),a.openListbox())}),m=$e(()=>{if(o.labelId)return[o.labelId,r].join(" ")},[o.labelId,r]),T=(0,J.useMemo)(()=>({open:o.listboxState===0,disabled:o.disabled,value:o.value}),[o]),b={ref:s,id:r,type:Se(e,o.buttonRef),"aria-haspopup":"listbox","aria-controls":(f=o.optionsRef.current)==null?void 0:f.id,"aria-expanded":o.listboxState===0,"aria-labelledby":m,disabled:o.disabled,onKeyDown:u,onKeyUp:d,onClick:p};return D({ourProps:b,theirProps:i,slot:T,defaultTag:Ea,name:"Listbox.Button"})}var Ra="label";function ha(e,n){let t=W(),{id:r=`headlessui-listbox-label-${t}`,...i}=e,o=Dt("Listbox.Label"),a=Lt("Listbox.Label"),s=_(o.labelRef,n);I(()=>a.registerLabel(r),[r]);let l=y(()=>{var p;return(p=o.buttonRef.current)==null?void 0:p.focus({preventScroll:!0})}),u=(0,J.useMemo)(()=>({open:o.listboxState===0,disabled:o.disabled}),[o]);return D({ourProps:{ref:s,id:r,onClick:l},theirProps:i,slot:u,defaultTag:Ra,name:"Listbox.Label"})}var Sa="ul",Oa=3;function Ca(e,n){var c;let t=W(),{id:r=`headlessui-listbox-options-${t}`,...i}=e,o=Dt("Listbox.Options"),a=Lt("Listbox.Options"),s=_(o.optionsRef,n),l=ce(),u=ce(),d=Pe(),p=(()=>d!==null?(d&1)===1:o.listboxState===0)();(0,J.useEffect)(()=>{var v;let g=o.optionsRef.current;g&&o.listboxState===0&&g!==((v=ve(g))==null?void 0:v.activeElement)&&g.focus({preventScroll:!0})},[o.listboxState,o.optionsRef]);let m=y(g=>{switch(u.dispose(),g.key){case" ":if(o.searchQuery!=="")return g.preventDefault(),g.stopPropagation(),a.search(g.key);case"Enter":if(g.preventDefault(),g.stopPropagation(),o.activeOptionIndex!==null){let{dataRef:v}=o.options[o.activeOptionIndex];a.onChange(v.current.value)}o.mode===0&&(a.closeListbox(),le().nextFrame(()=>{var v;return(v=o.buttonRef.current)==null?void 0:v.focus({preventScroll:!0})}));break;case F(o.orientation,{vertical:"ArrowDown",horizontal:"ArrowRight"}):return g.preventDefault(),g.stopPropagation(),a.goToOption(2);case F(o.orientation,{vertical:"ArrowUp",horizontal:"ArrowLeft"}):return g.preventDefault(),g.stopPropagation(),a.goToOption(1);case"Home":case"PageUp":return g.preventDefault(),g.stopPropagation(),a.goToOption(0);case"End":case"PageDown":return g.preventDefault(),g.stopPropagation(),a.goToOption(3);case"Escape":return g.preventDefault(),g.stopPropagation(),a.closeListbox(),l.nextFrame(()=>{var v;return(v=o.buttonRef.current)==null?void 0:v.focus({preventScroll:!0})});case"Tab":g.preventDefault(),g.stopPropagation();break;default:g.key.length===1&&(a.search(g.key),u.setTimeout(()=>a.clearSearch(),350));break}}),T=$e(()=>{var g;return(g=o.buttonRef.current)==null?void 0:g.id},[o.buttonRef.current]),b=(0,J.useMemo)(()=>({open:o.listboxState===0}),[o]),f={"aria-activedescendant":o.activeOptionIndex===null||(c=o.options[o.activeOptionIndex])==null?void 0:c.id,"aria-multiselectable":o.mode===1?!0:void 0,"aria-labelledby":T,"aria-orientation":o.orientation,id:r,onKeyDown:m,role:"listbox",tabIndex:0,ref:s};return D({ourProps:f,theirProps:i,slot:b,defaultTag:Sa,features:Oa,visible:p,name:"Listbox.Options"})}var Aa="li";function La(e,n){let t=W(),{id:r=`headlessui-listbox-option-${t}`,disabled:i=!1,value:o,...a}=e,s=Dt("Listbox.Option"),l=Lt("Listbox.Option"),u=s.activeOptionIndex!==null?s.options[s.activeOptionIndex].id===r:!1,d=s.isSelected(o),p=(0,J.useRef)(null),m=Yt(p),T=ee({disabled:i,value:o,domRef:p,get textValue(){return m()}}),b=_(n,p);I(()=>{if(s.listboxState!==0||!u||s.activationTrigger===0)return;let w=le();return w.requestAnimationFrame(()=>{var P,O;(O=(P=p.current)==null?void 0:P.scrollIntoView)==null||O.call(P,{block:"nearest"})}),w.dispose},[p,u,s.listboxState,s.activationTrigger,s.activeOptionIndex]),I(()=>l.registerOption(r,T),[T,r]);let f=y(w=>{if(i)return w.preventDefault();l.onChange(o),s.mode===0&&(l.closeListbox(),le().nextFrame(()=>{var P;return(P=s.buttonRef.current)==null?void 0:P.focus({preventScroll:!0})}))}),c=y(()=>{if(i)return l.goToOption(5);l.goToOption(4,r)}),g=lt(),v=y(w=>g.update(w)),E=y(w=>{g.wasMoved(w)&&(i||u||l.goToOption(4,r,0))}),C=y(w=>{g.wasMoved(w)&&(i||u&&l.goToOption(5))}),U=(0,J.useMemo)(()=>({active:u,selected:d,disabled:i}),[u,d,i]);return D({ourProps:{id:r,ref:b,role:"option",tabIndex:i===!0?void 0:-1,"aria-disabled":i===!0?!0:void 0,"aria-selected":d,disabled:void 0,onClick:f,onFocus:c,onPointerEnter:v,onMouseEnter:v,onPointerMove:E,onMouseMove:E,onPointerLeave:C,onMouseLeave:C},theirProps:a,slot:U,defaultTag:Aa,name:"Listbox.Option"})}var Da=M(xa),Ma=M(Pa),Ia=M(ha),Fa=M(Ca),_a=M(La),wa=Object.assign(Da,{Button:Ma,Label:Ia,Options:Fa,Option:_a});var ue=ie(require("react"),1);function Fn(e,n=t=>t){let t=e.activeItemIndex!==null?e.items[e.activeItemIndex]:null,r=Re(n(e.items.slice()),o=>o.dataRef.current.domRef.current),i=t?r.indexOf(t):null;return i===-1&&(i=null),{items:r,activeItemIndex:i}}var Ha={[1](e){return e.menuState===1?e:{...e,activeItemIndex:null,menuState:1}},[0](e){return e.menuState===0?e:{...e,__demoMode:!1,menuState:0}},[2]:(e,n)=>{var i;let t=Fn(e),r=Je(n,{resolveItems:()=>t.items,resolveActiveIndex:()=>t.activeItemIndex,resolveId:o=>o.id,resolveDisabled:o=>o.dataRef.current.disabled});return{...e,...t,searchQuery:"",activeItemIndex:r,activationTrigger:(i=n.trigger)!=null?i:1}},[3]:(e,n)=>{let r=e.searchQuery!==""?0:1,i=e.searchQuery+n.value.toLowerCase(),a=(e.activeItemIndex!==null?e.items.slice(e.activeItemIndex+r).concat(e.items.slice(0,e.activeItemIndex+r)):e.items).find(l=>{var u;return((u=l.dataRef.current.textValue)==null?void 0:u.startsWith(i))&&!l.dataRef.current.disabled}),s=a?e.items.indexOf(a):-1;return s===-1||s===e.activeItemIndex?{...e,searchQuery:i}:{...e,searchQuery:i,activeItemIndex:s,activationTrigger:1}},[4](e){return e.searchQuery===""?e:{...e,searchQuery:"",searchActiveItemIndex:null}},[5]:(e,n)=>{let t=Fn(e,r=>[...r,{id:n.id,dataRef:n.dataRef}]);return{...e,...t}},[6]:(e,n)=>{let t=Fn(e,r=>{let i=r.findIndex(o=>o.id===n.id);return i!==-1&&r.splice(i,1),r});return{...e,...t,activationTrigger:1}}},_n=(0,ue.createContext)(null);_n.displayName="MenuContext";function Qt(e){let n=(0,ue.useContext)(_n);if(n===null){let t=new Error(`<${e} /> is missing a parent <Menu /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Qt),t}return n}function ka(e,n){return F(n.type,Ha,e,n)}var Na=ue.Fragment;function Ua(e,n){let{__demoMode:t=!1,...r}=e,i=(0,ue.useReducer)(ka,{__demoMode:t,menuState:t?0:1,buttonRef:(0,ue.createRef)(),itemsRef:(0,ue.createRef)(),items:[],searchQuery:"",activeItemIndex:null,activationTrigger:1}),[{menuState:o,itemsRef:a,buttonRef:s},l]=i,u=_(n);_e([s,a],(T,b)=>{var f;l({type:1}),Ue(b,1)||(T.preventDefault(),(f=s.current)==null||f.focus())},o===0);let d=y(()=>{l({type:1})}),p=(0,ue.useMemo)(()=>({open:o===0,close:d}),[o,d]),m={ref:u};return ue.default.createElement(_n.Provider,{value:i},ue.default.createElement(Ce,{value:F(o,{[0]:1,[1]:2})},D({ourProps:m,theirProps:r,slot:p,defaultTag:Na,name:"Menu"})))}var Ba="button";function Ga(e,n){var b;let t=W(),{id:r=`headlessui-menu-button-${t}`,...i}=e,[o,a]=Qt("Menu.Button"),s=_(o.buttonRef,n),l=ce(),u=y(f=>{switch(f.key){case" ":case"Enter":case"ArrowDown":f.preventDefault(),f.stopPropagation(),a({type:0}),l.nextFrame(()=>a({type:2,focus:0}));break;case"ArrowUp":f.preventDefault(),f.stopPropagation(),a({type:0}),l.nextFrame(()=>a({type:2,focus:3}));break}}),d=y(f=>{switch(f.key){case" ":f.preventDefault();break}}),p=y(f=>{if(ge(f.currentTarget))return f.preventDefault();e.disabled||(o.menuState===0?(a({type:1}),l.nextFrame(()=>{var c;return(c=o.buttonRef.current)==null?void 0:c.focus({preventScroll:!0})})):(f.preventDefault(),a({type:0})))}),m=(0,ue.useMemo)(()=>({open:o.menuState===0}),[o]),T={ref:s,id:r,type:Se(e,o.buttonRef),"aria-haspopup":"menu","aria-controls":(b=o.itemsRef.current)==null?void 0:b.id,"aria-expanded":o.menuState===0,onKeyDown:u,onKeyUp:d,onClick:p};return D({ourProps:T,theirProps:i,slot:m,defaultTag:Ba,name:"Menu.Button"})}var Va="div",ja=3;function Wa(e,n){var c,g;let t=W(),{id:r=`headlessui-menu-items-${t}`,...i}=e,[o,a]=Qt("Menu.Items"),s=_(o.itemsRef,n),l=xe(o.itemsRef),u=ce(),d=Pe(),p=(()=>d!==null?(d&1)===1:o.menuState===0)();(0,ue.useEffect)(()=>{let v=o.itemsRef.current;v&&o.menuState===0&&v!==(l==null?void 0:l.activeElement)&&v.focus({preventScroll:!0})},[o.menuState,o.itemsRef,l]),st({container:o.itemsRef.current,enabled:o.menuState===0,accept(v){return v.getAttribute("role")==="menuitem"?NodeFilter.FILTER_REJECT:v.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(v){v.setAttribute("role","none")}});let m=y(v=>{var E,C;switch(u.dispose(),v.key){case" ":if(o.searchQuery!=="")return v.preventDefault(),v.stopPropagation(),a({type:3,value:v.key});case"Enter":if(v.preventDefault(),v.stopPropagation(),a({type:1}),o.activeItemIndex!==null){let{dataRef:U}=o.items[o.activeItemIndex];(C=(E=U.current)==null?void 0:E.domRef.current)==null||C.click()}dn(o.buttonRef.current);break;case"ArrowDown":return v.preventDefault(),v.stopPropagation(),a({type:2,focus:2});case"ArrowUp":return v.preventDefault(),v.stopPropagation(),a({type:2,focus:1});case"Home":case"PageUp":return v.preventDefault(),v.stopPropagation(),a({type:2,focus:0});case"End":case"PageDown":return v.preventDefault(),v.stopPropagation(),a({type:2,focus:3});case"Escape":v.preventDefault(),v.stopPropagation(),a({type:1}),le().nextFrame(()=>{var U;return(U=o.buttonRef.current)==null?void 0:U.focus({preventScroll:!0})});break;case"Tab":v.preventDefault(),v.stopPropagation(),a({type:1}),le().nextFrame(()=>{lr(o.buttonRef.current,v.shiftKey?2:4)});break;default:v.key.length===1&&(a({type:3,value:v.key}),u.setTimeout(()=>a({type:4}),350));break}}),T=y(v=>{switch(v.key){case" ":v.preventDefault();break}}),b=(0,ue.useMemo)(()=>({open:o.menuState===0}),[o]),f={"aria-activedescendant":o.activeItemIndex===null||(c=o.items[o.activeItemIndex])==null?void 0:c.id,"aria-labelledby":(g=o.buttonRef.current)==null?void 0:g.id,id:r,onKeyDown:m,onKeyUp:T,role:"menu",tabIndex:0,ref:s};return D({ourProps:f,theirProps:i,slot:b,defaultTag:Va,features:ja,visible:p,name:"Menu.Items"})}var Ka=ue.Fragment;function $a(e,n){let t=W(),{id:r=`headlessui-menu-item-${t}`,disabled:i=!1,...o}=e,[a,s]=Qt("Menu.Item"),l=a.activeItemIndex!==null?a.items[a.activeItemIndex].id===r:!1,u=(0,ue.useRef)(null),d=_(n,u);I(()=>{if(a.__demoMode||a.menuState!==0||!l||a.activationTrigger===0)return;let L=le();return L.requestAnimationFrame(()=>{var w,P;(P=(w=u.current)==null?void 0:w.scrollIntoView)==null||P.call(w,{block:"nearest"})}),L.dispose},[a.__demoMode,u,l,a.menuState,a.activationTrigger,a.activeItemIndex]);let p=Yt(u),m=(0,ue.useRef)({disabled:i,domRef:u,get textValue(){return p()}});I(()=>{m.current.disabled=i},[m,i]),I(()=>(s({type:5,id:r,dataRef:m}),()=>s({type:6,id:r})),[m,r]);let T=y(()=>{s({type:1})}),b=y(L=>{if(i)return L.preventDefault();s({type:1}),dn(a.buttonRef.current)}),f=y(()=>{if(i)return s({type:2,focus:5});s({type:2,focus:4,id:r})}),c=lt(),g=y(L=>c.update(L)),v=y(L=>{c.wasMoved(L)&&(i||l||s({type:2,focus:4,id:r,trigger:0}))}),E=y(L=>{c.wasMoved(L)&&(i||l&&s({type:2,focus:5}))}),C=(0,ue.useMemo)(()=>({active:l,disabled:i,close:T}),[l,i,T]);return D({ourProps:{id:r,ref:d,role:"menuitem",tabIndex:i===!0?void 0:-1,"aria-disabled":i===!0?!0:void 0,disabled:void 0,onClick:b,onFocus:f,onPointerEnter:g,onMouseEnter:g,onPointerMove:v,onMouseMove:v,onPointerLeave:E,onMouseLeave:E},theirProps:o,slot:C,defaultTag:Ka,name:"Menu.Item"})}var za=M(Ua),Xa=M(Ga),Ja=M(Wa),qa=M($a),Ya=Object.assign(za,{Button:Xa,Items:Ja,Item:qa});var k=ie(require("react"),1);var Qa={[0]:e=>{let n={...e,popoverState:F(e.popoverState,{[0]:1,[1]:0})};return n.popoverState===0&&(n.__demoMode=!1),n},[1](e){return e.popoverState===1?e:{...e,popoverState:1}},[2](e,n){return e.button===n.button?e:{...e,button:n.button}},[3](e,n){return e.buttonId===n.buttonId?e:{...e,buttonId:n.buttonId}},[4](e,n){return e.panel===n.panel?e:{...e,panel:n.panel}},[5](e,n){return e.panelId===n.panelId?e:{...e,panelId:n.panelId}}},wn=(0,k.createContext)(null);wn.displayName="PopoverContext";function Zt(e){let n=(0,k.useContext)(wn);if(n===null){let t=new Error(`<${e} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Zt),t}return n}var Hn=(0,k.createContext)(null);Hn.displayName="PopoverAPIContext";function kn(e){let n=(0,k.useContext)(Hn);if(n===null){let t=new Error(`<${e} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,kn),t}return n}var Nn=(0,k.createContext)(null);Nn.displayName="PopoverGroupContext";function Zr(){return(0,k.useContext)(Nn)}var en=(0,k.createContext)(null);en.displayName="PopoverPanelContext";function Za(){return(0,k.useContext)(en)}function el(e,n){return F(n.type,Qa,e,n)}var tl="div";function nl(e,n){var x;let{__demoMode:t=!1,...r}=e,i=(0,k.useRef)(null),o=_(n,at(H=>{i.current=H})),a=(0,k.useRef)([]),s=(0,k.useReducer)(el,{__demoMode:t,popoverState:t?0:1,buttons:a,button:null,buttonId:null,panel:null,panelId:null,beforePanelSentinel:(0,k.createRef)(),afterPanelSentinel:(0,k.createRef)()}),[{popoverState:l,button:u,buttonId:d,panel:p,panelId:m,beforePanelSentinel:T,afterPanelSentinel:b},f]=s,c=xe((x=i.current)!=null?x:u),g=(0,k.useMemo)(()=>{if(!u||!p)return!1;for(let oe of document.querySelectorAll("body > *"))if(Number(oe==null?void 0:oe.contains(u))^Number(oe==null?void 0:oe.contains(p)))return!0;let H=it(),V=H.indexOf(u),re=(V+H.length-1)%H.length,A=(V+1)%H.length,j=H[re],Y=H[A];return!p.contains(j)&&!p.contains(Y)},[u,p]),v=ee(d),E=ee(m),C=(0,k.useMemo)(()=>({buttonId:v,panelId:E,close:()=>f({type:1})}),[v,E,f]),U=Zr(),L=U==null?void 0:U.registerPopover,w=y(()=>{var H;return(H=U==null?void 0:U.isFocusWithinPopoverGroup())!=null?H:(c==null?void 0:c.activeElement)&&((u==null?void 0:u.contains(c.activeElement))||(p==null?void 0:p.contains(c.activeElement)))});(0,k.useEffect)(()=>L==null?void 0:L(C),[L,C]);let[P,O]=Xt(),R=Jt({mainTreeNodeRef:U==null?void 0:U.mainTreeNodeRef,portals:P,defaultContainers:[u,p]});ct(c==null?void 0:c.defaultView,"focus",H=>{var V,re,A,j;H.target!==window&&H.target instanceof HTMLElement&&l===0&&(w()||u&&p&&(R.contains(H.target)||(re=(V=T.current)==null?void 0:V.contains)!=null&&re.call(V,H.target)||(j=(A=b.current)==null?void 0:A.contains)!=null&&j.call(A,H.target)||f({type:1})))},!0),_e(R.resolveContainers,(H,V)=>{f({type:1}),Ue(V,1)||(H.preventDefault(),u==null||u.focus())},l===0);let h=y(H=>{f({type:1});let V=(()=>H?H instanceof HTMLElement?H:"current"in H&&H.current instanceof HTMLElement?H.current:u:u)();V==null||V.focus()}),S=(0,k.useMemo)(()=>({close:h,isPortalled:g}),[h,g]),G=(0,k.useMemo)(()=>({open:l===0,close:h}),[l,h]),Z={ref:o};return k.default.createElement(en.Provider,{value:null},k.default.createElement(wn.Provider,{value:s},k.default.createElement(Hn.Provider,{value:S},k.default.createElement(Ce,{value:F(l,{[0]:1,[1]:2})},k.default.createElement(O,null,D({ourProps:Z,theirProps:r,slot:G,defaultTag:tl,name:"Popover"}),k.default.createElement(R.MainTreeNode,null))))))}var rl="button";function ol(e,n){let t=W(),{id:r=`headlessui-popover-button-${t}`,...i}=e,[o,a]=Zt("Popover.Button"),{isPortalled:s}=kn("Popover.Button"),l=(0,k.useRef)(null),u=`headlessui-focus-sentinel-${W()}`,d=Zr(),p=d==null?void 0:d.closeOthers,T=Za()!==null;(0,k.useEffect)(()=>{if(!T)return a({type:3,buttonId:r}),()=>{a({type:3,buttonId:null})}},[T,r,a]);let[b]=(0,k.useState)(()=>Symbol()),f=_(l,n,T?null:S=>{if(S)o.buttons.current.push(b);else{let G=o.buttons.current.indexOf(b);G!==-1&&o.buttons.current.splice(G,1)}o.buttons.current.length>1&&console.warn("You are already using a <Popover.Button /> but only 1 <Popover.Button /> is supported."),S&&a({type:2,button:S})}),c=_(l,n),g=xe(l),v=y(S=>{var G,Z,x;if(T){if(o.popoverState===1)return;switch(S.key){case" ":case"Enter":S.preventDefault(),(Z=(G=S.target).click)==null||Z.call(G),a({type:1}),(x=o.button)==null||x.focus();break}}else switch(S.key){case" ":case"Enter":S.preventDefault(),S.stopPropagation(),o.popoverState===1&&(p==null||p(o.buttonId)),a({type:0});break;case"Escape":if(o.popoverState!==0)return p==null?void 0:p(o.buttonId);if(!l.current||g!=null&&g.activeElement&&!l.current.contains(g.activeElement))return;S.preventDefault(),S.stopPropagation(),a({type:1});break}}),E=y(S=>{T||S.key===" "&&S.preventDefault()}),C=y(S=>{var G,Z;ge(S.currentTarget)||e.disabled||(T?(a({type:1}),(G=o.button)==null||G.focus()):(S.preventDefault(),S.stopPropagation(),o.popoverState===1&&(p==null||p(o.buttonId)),a({type:0}),(Z=o.button)==null||Z.focus()))}),U=y(S=>{S.preventDefault(),S.stopPropagation()}),L=o.popoverState===0,w=(0,k.useMemo)(()=>({open:L}),[L]),P=Se(e,l),O=T?{ref:c,type:P,onKeyDown:v,onClick:C}:{ref:f,id:o.buttonId,type:P,"aria-expanded":o.popoverState===0,"aria-controls":o.panel?o.panelId:void 0,onKeyDown:v,onKeyUp:E,onClick:C,onMouseDown:U},R=Ot(),h=y(()=>{let S=o.panel;if(!S)return;function G(){F(R.current,{[0]:()=>pe(S,1),[1]:()=>pe(S,8)})===0&&pe(it().filter(x=>x.dataset.headlessuiFocusGuard!=="true"),F(R.current,{[0]:4,[1]:2}),{relativeTo:o.button})}G()});return k.default.createElement(k.default.Fragment,null,D({ourProps:O,theirProps:i,slot:w,defaultTag:rl,name:"Popover.Button"}),L&&!T&&s&&k.default.createElement(fe,{id:u,features:2,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:h}))}var il="div",al=3;function ll(e,n){let t=W(),{id:r=`headlessui-popover-overlay-${t}`,...i}=e,[{popoverState:o},a]=Zt("Popover.Overlay"),s=_(n),l=Pe(),u=(()=>l!==null?(l&1)===1:o===0)(),d=y(T=>{if(ge(T.currentTarget))return T.preventDefault();a({type:1})}),p=(0,k.useMemo)(()=>({open:o===0}),[o]);return D({ourProps:{ref:s,id:r,"aria-hidden":!0,onClick:d},theirProps:i,slot:p,defaultTag:il,features:al,visible:u,name:"Popover.Overlay"})}var sl="div",ul=3;function pl(e,n){let t=W(),{id:r=`headlessui-popover-panel-${t}`,focus:i=!1,...o}=e,[a,s]=Zt("Popover.Panel"),{close:l,isPortalled:u}=kn("Popover.Panel"),d=`headlessui-focus-sentinel-before-${W()}`,p=`headlessui-focus-sentinel-after-${W()}`,m=(0,k.useRef)(null),T=_(m,n,P=>{s({type:4,panel:P})}),b=xe(m),f=ht();I(()=>(s({type:5,panelId:r}),()=>{s({type:5,panelId:null})}),[r,s]);let c=Pe(),g=(()=>c!==null?(c&1)===1:a.popoverState===0)(),v=y(P=>{var O;switch(P.key){case"Escape":if(a.popoverState!==0||!m.current||b!=null&&b.activeElement&&!m.current.contains(b.activeElement))return;P.preventDefault(),P.stopPropagation(),s({type:1}),(O=a.button)==null||O.focus();break}});(0,k.useEffect)(()=>{var P;e.static||a.popoverState===1&&((P=e.unmount)==null||P)&&s({type:4,panel:null})},[a.popoverState,e.unmount,e.static,s]),(0,k.useEffect)(()=>{if(a.__demoMode||!i||a.popoverState!==0||!m.current)return;let P=b==null?void 0:b.activeElement;m.current.contains(P)||pe(m.current,1)},[a.__demoMode,i,m,a.popoverState]);let E=(0,k.useMemo)(()=>({open:a.popoverState===0,close:l}),[a,l]),C={ref:T,id:r,onKeyDown:v,onBlur:i&&a.popoverState===0?P=>{var R,h,S,G,Z;let O=P.relatedTarget;O&&m.current&&((R=m.current)!=null&&R.contains(O)||(s({type:1}),((S=(h=a.beforePanelSentinel.current)==null?void 0:h.contains)!=null&&S.call(h,O)||(Z=(G=a.afterPanelSentinel.current)==null?void 0:G.contains)!=null&&Z.call(G,O))&&O.focus({preventScroll:!0})))}:void 0,tabIndex:-1},U=Ot(),L=y(()=>{let P=m.current;if(!P)return;function O(){F(U.current,{[0]:()=>{var h;pe(P,1)===0&&((h=a.afterPanelSentinel.current)==null||h.focus())},[1]:()=>{var R;(R=a.button)==null||R.focus({preventScroll:!0})}})}O()}),w=y(()=>{let P=m.current;if(!P)return;function O(){F(U.current,{[0]:()=>{var x;if(!a.button)return;let R=it(),h=R.indexOf(a.button),S=R.slice(0,h+1),Z=[...R.slice(h+1),...S];for(let H of Z.slice())if(H.dataset.headlessuiFocusGuard==="true"||(x=a.panel)!=null&&x.contains(H)){let V=Z.indexOf(H);V!==-1&&Z.splice(V,1)}pe(Z,1,{sorted:!1})},[1]:()=>{var h;pe(P,2)===0&&((h=a.button)==null||h.focus())}})}O()});return k.default.createElement(en.Provider,{value:r},g&&u&&k.default.createElement(fe,{id:d,ref:a.beforePanelSentinel,features:2,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:L}),D({mergeRefs:f,ourProps:C,theirProps:o,slot:E,defaultTag:sl,features:ul,visible:g,name:"Popover.Panel"}),g&&u&&k.default.createElement(fe,{id:p,ref:a.afterPanelSentinel,features:2,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:w}))}var dl="div";function cl(e,n){let t=(0,k.useRef)(null),r=_(t,n),[i,o]=(0,k.useState)([]),a=Vr(),s=y(f=>{o(c=>{let g=c.indexOf(f);if(g!==-1){let v=c.slice();return v.splice(g,1),v}return c})}),l=y(f=>(o(c=>[...c,f]),()=>s(f))),u=y(()=>{var g;let f=ve(t);if(!f)return!1;let c=f.activeElement;return(g=t.current)!=null&&g.contains(c)?!0:i.some(v=>{var E,C;return((E=f.getElementById(v.buttonId.current))==null?void 0:E.contains(c))||((C=f.getElementById(v.panelId.current))==null?void 0:C.contains(c))})}),d=y(f=>{for(let c of i)c.buttonId.current!==f&&c.close()}),p=(0,k.useMemo)(()=>({registerPopover:l,unregisterPopover:s,isFocusWithinPopoverGroup:u,closeOthers:d,mainTreeNodeRef:a.mainTreeNodeRef}),[l,s,u,d,a.mainTreeNodeRef]),m=(0,k.useMemo)(()=>({}),[]),T=e,b={ref:r};return k.default.createElement(Nn.Provider,{value:p},D({ourProps:b,theirProps:T,slot:m,defaultTag:dl,name:"Popover.Group"}),k.default.createElement(a.MainTreeNode,null))}var fl=M(nl),ml=M(ol),Tl=M(ll),bl=M(pl),yl=M(cl),gl=Object.assign(fl,{Button:ml,Overlay:Tl,Panel:bl,Group:yl});var ne=ie(require("react"),1);var Fe=ie(require("react"),1);var eo=(0,Fe.createContext)(null);function to(){let e=(0,Fe.useContext)(eo);if(e===null){let n=new Error("You used a <Label /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(n,to),n}return e}function Mt(){let[e,n]=(0,Fe.useState)([]);return[e.length>0?e.join(" "):void 0,(0,Fe.useMemo)(()=>function(r){let i=y(a=>(n(s=>[...s,a]),()=>n(s=>{let l=s.slice(),u=l.indexOf(a);return u!==-1&&l.splice(u,1),l}))),o=(0,Fe.useMemo)(()=>({register:i,slot:r.slot,name:r.name,props:r.props}),[i,r.slot,r.name,r.props]);return Fe.default.createElement(eo.Provider,{value:o},r.children)},[n])]}var vl="label";function xl(e,n){let t=W(),{id:r=`headlessui-label-${t}`,passive:i=!1,...o}=e,a=to(),s=_(n);I(()=>a.register(r),[r,a.register]);let l={ref:s,...a.props,id:r};return i&&("onClick"in l&&(delete l.htmlFor,delete l.onClick),"onClick"in o&&delete o.onClick),D({ourProps:l,theirProps:o,slot:a.slot||{},defaultTag:vl,name:a.name||"Label"})}var El=M(xl),tn=Object.assign(El,{});var nt=require("react");function nn(e=0){let[n,t]=(0,nt.useState)(e),r=Me(),i=(0,nt.useCallback)(l=>{r.current&&t(u=>u|l)},[n,r]),o=(0,nt.useCallback)(l=>Boolean(n&l),[n]),a=(0,nt.useCallback)(l=>{r.current&&t(u=>u&~l)},[t,r]),s=(0,nt.useCallback)(l=>{r.current&&t(u=>u^l)},[t]);return{flags:n,addFlag:i,hasFlag:o,removeFlag:a,toggleFlag:s}}var Pl={[0](e,n){let t=[...e.options,{id:n.id,element:n.element,propsRef:n.propsRef}];return{...e,options:Re(t,r=>r.element.current)}},[1](e,n){let t=e.options.slice(),r=e.options.findIndex(i=>i.id===n.id);return r===-1?e:(t.splice(r,1),{...e,options:t})}},Un=(0,ne.createContext)(null);Un.displayName="RadioGroupDataContext";function no(e){let n=(0,ne.useContext)(Un);if(n===null){let t=new Error(`<${e} /> is missing a parent <RadioGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,no),t}return n}var Bn=(0,ne.createContext)(null);Bn.displayName="RadioGroupActionsContext";function ro(e){let n=(0,ne.useContext)(Bn);if(n===null){let t=new Error(`<${e} /> is missing a parent <RadioGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,ro),t}return n}function Rl(e,n){return F(n.type,Pl,e,n)}var hl="div";function Sl(e,n){let t=W(),{id:r=`headlessui-radiogroup-${t}`,value:i,defaultValue:o,form:a,name:s,onChange:l,by:u=(A,j)=>A===j,disabled:d=!1,...p}=e,m=y(typeof u=="string"?(A,j)=>{let Y=u;return(A==null?void 0:A[Y])===(j==null?void 0:j[Y])}:u),[T,b]=(0,ne.useReducer)(Rl,{options:[]}),f=T.options,[c,g]=Mt(),[v,E]=tt(),C=(0,ne.useRef)(null),U=_(C,n),[L,w]=Ve(i,l,o),P=(0,ne.useMemo)(()=>f.find(A=>!A.propsRef.current.disabled),[f]),O=(0,ne.useMemo)(()=>f.some(A=>m(A.propsRef.current.value,L)),[f,L]),R=y(A=>{var Y;if(d||m(A,L))return!1;let j=(Y=f.find(oe=>m(oe.propsRef.current.value,A)))==null?void 0:Y.propsRef.current;return j!=null&&j.disabled?!1:(w==null||w(A),!0)});st({container:C.current,accept(A){return A.getAttribute("role")==="radio"?NodeFilter.FILTER_REJECT:A.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(A){A.setAttribute("role","none")}});let h=y(A=>{let j=C.current;if(!j)return;let Y=ve(j),oe=f.filter(de=>de.propsRef.current.disabled===!1).map(de=>de.element.current);switch(A.key){case"Enter":jt(A.currentTarget);break;case"ArrowLeft":case"ArrowUp":if(A.preventDefault(),A.stopPropagation(),pe(oe,18)===2){let B=f.find(q=>q.element.current===(Y==null?void 0:Y.activeElement));B&&R(B.propsRef.current.value)}break;case"ArrowRight":case"ArrowDown":if(A.preventDefault(),A.stopPropagation(),pe(oe,20)===2){let B=f.find(q=>q.element.current===(Y==null?void 0:Y.activeElement));B&&R(B.propsRef.current.value)}break;case" ":{A.preventDefault(),A.stopPropagation();let de=f.find(B=>B.element.current===(Y==null?void 0:Y.activeElement));de&&R(de.propsRef.current.value)}break}}),S=y(A=>(b({type:0,...A}),()=>b({type:1,id:A.id}))),G=(0,ne.useMemo)(()=>({value:L,firstOption:P,containsCheckedOption:O,disabled:d,compare:m,...T}),[L,P,O,d,m,T]),Z=(0,ne.useMemo)(()=>({registerOption:S,change:R}),[S,R]),x={ref:U,id:r,role:"radiogroup","aria-labelledby":c,"aria-describedby":v,onKeyDown:h},H=(0,ne.useMemo)(()=>({value:L}),[L]),V=(0,ne.useRef)(null),re=ce();return(0,ne.useEffect)(()=>{V.current&&o!==void 0&&re.addEventListener(V.current,"reset",()=>{R(o)})},[V,R]),ne.default.createElement(E,{name:"RadioGroup.Description"},ne.default.createElement(g,{name:"RadioGroup.Label"},ne.default.createElement(Bn.Provider,{value:Z},ne.default.createElement(Un.Provider,{value:G},s!=null&&L!=null&&qe({[s]:L}).map(([A,j],Y)=>ne.default.createElement(fe,{features:4,ref:Y===0?oe=>{var de;V.current=(de=oe==null?void 0:oe.closest("form"))!=null?de:null}:void 0,...we({key:A,as:"input",type:"radio",checked:j!=null,hidden:!0,readOnly:!0,form:a,disabled:d,name:A,value:j})})),D({ourProps:x,theirProps:p,slot:H,defaultTag:hl,name:"RadioGroup"})))))}var Ol="div";function Cl(e,n){var h;let t=W(),{id:r=`headlessui-radiogroup-option-${t}`,value:i,disabled:o=!1,...a}=e,s=(0,ne.useRef)(null),l=_(s,n),[u,d]=Mt(),[p,m]=tt(),{addFlag:T,removeFlag:b,hasFlag:f}=nn(1),c=ee({value:i,disabled:o}),g=no("RadioGroup.Option"),v=ro("RadioGroup.Option");I(()=>v.registerOption({id:r,element:s,propsRef:c}),[r,v,s,c]);let E=y(S=>{var G;if(ge(S.currentTarget))return S.preventDefault();v.change(i)&&(T(2),(G=s.current)==null||G.focus())}),C=y(S=>{if(ge(S.currentTarget))return S.preventDefault();T(2)}),U=y(()=>b(2)),L=((h=g.firstOption)==null?void 0:h.id)===r,w=g.disabled||o,P=g.compare(g.value,i),O={ref:l,id:r,role:"radio","aria-checked":P?"true":"false","aria-labelledby":u,"aria-describedby":p,"aria-disabled":w?!0:void 0,tabIndex:(()=>w?-1:P||!g.containsCheckedOption&&L?0:-1)(),onClick:w?void 0:E,onFocus:w?void 0:C,onBlur:w?void 0:U},R=(0,ne.useMemo)(()=>({checked:P,disabled:w,active:f(2)}),[P,w,f]);return ne.default.createElement(m,{name:"RadioGroup.Description"},ne.default.createElement(d,{name:"RadioGroup.Label"},D({ourProps:O,theirProps:a,slot:R,defaultTag:Ol,name:"RadioGroup.Option"})))}var Al=M(Sl),Ll=M(Cl),Dl=Object.assign(Al,{Option:Ll,Label:tn,Description:bt});var me=ie(require("react"),1);var Gn=(0,me.createContext)(null);Gn.displayName="GroupContext";var Ml=me.Fragment;function Il(e){var d;let[n,t]=(0,me.useState)(null),[r,i]=Mt(),[o,a]=tt(),s=(0,me.useMemo)(()=>({switch:n,setSwitch:t,labelledby:r,describedby:o}),[n,t,r,o]),l={},u=e;return me.default.createElement(a,{name:"Switch.Description"},me.default.createElement(i,{name:"Switch.Label",props:{htmlFor:(d=s.switch)==null?void 0:d.id,onClick(p){n&&(p.currentTarget.tagName==="LABEL"&&p.preventDefault(),n.click(),n.focus({preventScroll:!0}))}}},me.default.createElement(Gn.Provider,{value:s},D({ourProps:l,theirProps:u,defaultTag:Ml,name:"Switch.Group"}))))}var Fl="button";function _l(e,n){var P;let t=W(),{id:r=`headlessui-switch-${t}`,checked:i,defaultChecked:o=!1,onChange:a,disabled:s=!1,name:l,value:u,form:d,...p}=e,m=(0,me.useContext)(Gn),T=(0,me.useRef)(null),b=_(T,n,m===null?null:m.setSwitch),[f,c]=Ve(i,a,o),g=y(()=>c==null?void 0:c(!f)),v=y(O=>{if(ge(O.currentTarget))return O.preventDefault();O.preventDefault(),g()}),E=y(O=>{O.key===" "?(O.preventDefault(),g()):O.key==="Enter"&&jt(O.currentTarget)}),C=y(O=>O.preventDefault()),U=(0,me.useMemo)(()=>({checked:f}),[f]),L={id:r,ref:b,role:"switch",type:Se(e,T),tabIndex:e.tabIndex===-1?0:(P=e.tabIndex)!=null?P:0,"aria-checked":f,"aria-labelledby":m==null?void 0:m.labelledby,"aria-describedby":m==null?void 0:m.describedby,disabled:s,onClick:v,onKeyUp:E,onKeyPress:C},w=ce();return(0,me.useEffect)(()=>{var R;let O=(R=T.current)==null?void 0:R.closest("form");O&&o!==void 0&&w.addEventListener(O,"reset",()=>{c(o)})},[T,c]),me.default.createElement(me.default.Fragment,null,l!=null&&f&&me.default.createElement(fe,{features:4,...we({as:"input",type:"checkbox",hidden:!0,readOnly:!0,disabled:s,form:d,checked:f,name:l,value:u})}),D({ourProps:L,theirProps:p,slot:U,defaultTag:Fl,name:"Switch"}))}var wl=M(_l),Hl=Il,kl=Object.assign(wl,{Group:Hl,Label:tn,Description:bt});var ae=ie(require("react"),1);var rn=ie(require("react"),1);function oo({onFocus:e}){let[n,t]=(0,rn.useState)(!0),r=Me();return n?rn.default.createElement(fe,{as:"button",type:"button",features:2,onFocus:i=>{i.preventDefault();let o,a=50;function s(){if(a--<=0){o&&cancelAnimationFrame(o);return}if(e()){if(cancelAnimationFrame(o),!r.current)return;t(!1);return}o=requestAnimationFrame(s)}o=requestAnimationFrame(s)}}):null}var Le=ie(require("react"),1),io=Le.createContext(null);function Nl(){return{groups:new Map,get(e,n){var a;let t=this.groups.get(e);t||(t=new Map,this.groups.set(e,t));let r=(a=t.get(n))!=null?a:0;t.set(n,r+1);let i=Array.from(t.keys()).indexOf(n);function o(){let s=t.get(n);s>1?t.set(n,s-1):t.delete(n)}return[i,o]}}}function ao({children:e}){let n=Le.useRef(Nl());return Le.createElement(io.Provider,{value:n},e)}function Vn(e){let n=Le.useContext(io);if(!n)throw new Error("You must wrap your component in a <StableCollection>");let t=Ul(),[r,i]=n.current.get(e,t);return Le.useEffect(()=>i,[]),r}function Ul(){var r,i,o;let e=(o=(i=(r=Le.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED)==null?void 0:r.ReactCurrentOwner)==null?void 0:i.current)!=null?o:null;if(!e)return Symbol();let n=[],t=e;for(;t;)n.push(t.index),t=t.return;return"$."+n.join(".")}var Bl={[0](e,n){var d;let t=Re(e.tabs,p=>p.current),r=Re(e.panels,p=>p.current),i=t.filter(p=>{var m;return!((m=p.current)!=null&&m.hasAttribute("disabled"))}),o={...e,tabs:t,panels:r};if(n.index<0||n.index>t.length-1){let p=F(Math.sign(n.index-e.selectedIndex),{[-1]:()=>1,[0]:()=>F(Math.sign(n.index),{[-1]:()=>0,[0]:()=>0,[1]:()=>1}),[1]:()=>0});if(i.length===0)return o;let m=F(p,{[0]:()=>t.indexOf(i[0]),[1]:()=>t.indexOf(i[i.length-1])});return{...o,selectedIndex:m===-1?e.selectedIndex:m}}let a=t.slice(0,n.index),l=[...t.slice(n.index),...a].find(p=>i.includes(p));if(!l)return o;let u=(d=t.indexOf(l))!=null?d:e.selectedIndex;return u===-1&&(u=e.selectedIndex),{...o,selectedIndex:u}},[1](e,n){if(e.tabs.includes(n.tab))return e;let t=e.tabs[e.selectedIndex],r=Re([...e.tabs,n.tab],o=>o.current),i=e.selectedIndex;return e.info.current.isControlled||(i=r.indexOf(t),i===-1&&(i=e.selectedIndex)),{...e,tabs:r,selectedIndex:i}},[2](e,n){return{...e,tabs:e.tabs.filter(t=>t!==n.tab)}},[3](e,n){return e.panels.includes(n.panel)?e:{...e,panels:Re([...e.panels,n.panel],t=>t.current)}},[4](e,n){return{...e,panels:e.panels.filter(t=>t!==n.panel)}}},jn=(0,ae.createContext)(null);jn.displayName="TabsDataContext";function yt(e){let n=(0,ae.useContext)(jn);if(n===null){let t=new Error(`<${e} /> is missing a parent <Tab.Group /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,yt),t}return n}var Wn=(0,ae.createContext)(null);Wn.displayName="TabsActionsContext";function Kn(e){let n=(0,ae.useContext)(Wn);if(n===null){let t=new Error(`<${e} /> is missing a parent <Tab.Group /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Kn),t}return n}function Gl(e,n){return F(n.type,Bl,e,n)}var Vl=ae.Fragment;function jl(e,n){let{defaultIndex:t=0,vertical:r=!1,manual:i=!1,onChange:o,selectedIndex:a=null,...s}=e,l=r?"vertical":"horizontal",u=i?"manual":"auto",d=a!==null,p=ee({isControlled:d}),m=_(n),[T,b]=(0,ae.useReducer)(Gl,{info:p,selectedIndex:a!=null?a:t,tabs:[],panels:[]}),f=(0,ae.useMemo)(()=>({selectedIndex:T.selectedIndex}),[T.selectedIndex]),c=ee(o||(()=>{})),g=ee(T.tabs),v=(0,ae.useMemo)(()=>({orientation:l,activation:u,...T}),[l,u,T]),E=y(O=>(b({type:1,tab:O}),()=>b({type:2,tab:O}))),C=y(O=>(b({type:3,panel:O}),()=>b({type:4,panel:O}))),U=y(O=>{L.current!==O&&c.current(O),d||b({type:0,index:O})}),L=ee(d?e.selectedIndex:T.selectedIndex),w=(0,ae.useMemo)(()=>({registerTab:E,registerPanel:C,change:U}),[]);I(()=>{b({type:0,index:a!=null?a:t})},[a]),I(()=>{if(L.current===void 0||T.tabs.length<=0)return;let O=Re(T.tabs,h=>h.current);O.some((h,S)=>T.tabs[S]!==h)&&U(O.indexOf(T.tabs[L.current]))});let P={ref:m};return ae.default.createElement(ao,null,ae.default.createElement(Wn.Provider,{value:w},ae.default.createElement(jn.Provider,{value:v},v.tabs.length<=0&&ae.default.createElement(oo,{onFocus:()=>{var O,R;for(let h of g.current)if(((O=h.current)==null?void 0:O.tabIndex)===0)return(R=h.current)==null||R.focus(),!0;return!1}}),D({ourProps:P,theirProps:s,slot:f,defaultTag:Vl,name:"Tabs"}))))}var Wl="div";function Kl(e,n){let{orientation:t,selectedIndex:r}=yt("Tab.List"),i=_(n);return D({ourProps:{ref:i,role:"tablist","aria-orientation":t},theirProps:e,slot:{selectedIndex:r},defaultTag:Wl,name:"Tabs.List"})}var $l="button";function zl(e,n){var P,O;let t=W(),{id:r=`headlessui-tabs-tab-${t}`,...i}=e,{orientation:o,activation:a,selectedIndex:s,tabs:l,panels:u}=yt("Tab"),d=Kn("Tab"),p=yt("Tab"),m=(0,ae.useRef)(null),T=_(m,n);I(()=>d.registerTab(m),[d,m]);let b=Vn("tabs"),f=l.indexOf(m);f===-1&&(f=b);let c=f===s,g=y(R=>{var S;let h=R();if(h===2&&a==="auto"){let G=(S=ve(m))==null?void 0:S.activeElement,Z=p.tabs.findIndex(x=>x.current===G);Z!==-1&&d.change(Z)}return h}),v=y(R=>{let h=l.map(G=>G.current).filter(Boolean);if(R.key===" "||R.key==="Enter"){R.preventDefault(),R.stopPropagation(),d.change(f);return}switch(R.key){case"Home":case"PageUp":return R.preventDefault(),R.stopPropagation(),g(()=>pe(h,1));case"End":case"PageDown":return R.preventDefault(),R.stopPropagation(),g(()=>pe(h,8))}if(g(()=>F(o,{vertical(){return R.key==="ArrowUp"?pe(h,18):R.key==="ArrowDown"?pe(h,20):0},horizontal(){return R.key==="ArrowLeft"?pe(h,18):R.key==="ArrowRight"?pe(h,20):0}}))===2)return R.preventDefault()}),E=(0,ae.useRef)(!1),C=y(()=>{var R;E.current||(E.current=!0,(R=m.current)==null||R.focus({preventScroll:!0}),d.change(f),Ne(()=>{E.current=!1}))}),U=y(R=>{R.preventDefault()}),L=(0,ae.useMemo)(()=>{var R;return{selected:c,disabled:(R=e.disabled)!=null?R:!1}},[c,e.disabled]),w={ref:T,onKeyDown:v,onMouseDown:U,onClick:C,id:r,role:"tab",type:Se(e,m),"aria-controls":(O=(P=u[f])==null?void 0:P.current)==null?void 0:O.id,"aria-selected":c,tabIndex:c?0:-1};return D({ourProps:w,theirProps:i,slot:L,defaultTag:$l,name:"Tabs.Tab"})}var Xl="div";function Jl(e,n){let{selectedIndex:t}=yt("Tab.Panels"),r=_(n),i=(0,ae.useMemo)(()=>({selectedIndex:t}),[t]);return D({ourProps:{ref:r},theirProps:e,slot:i,defaultTag:Xl,name:"Tabs.Panels"})}var ql="div",Yl=3;function Ql(e,n){var g,v,E,C;let t=W(),{id:r=`headlessui-tabs-panel-${t}`,tabIndex:i=0,...o}=e,{selectedIndex:a,tabs:s,panels:l}=yt("Tab.Panel"),u=Kn("Tab.Panel"),d=(0,ae.useRef)(null),p=_(d,n);I(()=>u.registerPanel(d),[u,d,r]);let m=Vn("panels"),T=l.indexOf(d);T===-1&&(T=m);let b=T===a,f=(0,ae.useMemo)(()=>({selected:b}),[b]),c={ref:p,id:r,role:"tabpanel","aria-labelledby":(v=(g=s[T])==null?void 0:g.current)==null?void 0:v.id,tabIndex:b?i:-1};return!b&&((E=o.unmount)==null||E)&&!((C=o.static)!=null&&C)?ae.default.createElement(fe,{as:"span","aria-hidden":"true",...c}):D({ourProps:c,theirProps:o,slot:f,defaultTag:ql,features:Yl,visible:b,name:"Tabs.Panel"})}var Zl=M(zl),es=M(jl),ts=M(Kl),ns=M(Jl),rs=M(Ql),os=Object.assign(Zl,{Group:es,List:ts,Panels:ns,Panel:rs});var K=ie(require("react"),1);function lo(e){let n={called:!1};return(...t)=>{if(!n.called)return n.called=!0,e(...t)}}function $n(e,...n){e&&n.length>0&&e.classList.add(...n)}function zn(e,...n){e&&n.length>0&&e.classList.remove(...n)}function is(e,n){let t=le();if(!e)return t.dispose;let{transitionDuration:r,transitionDelay:i}=getComputedStyle(e),[o,a]=[r,i].map(l=>{let[u=0]=l.split(",").filter(Boolean).map(d=>d.includes("ms")?parseFloat(d):parseFloat(d)*1e3).sort((d,p)=>p-d);return u}),s=o+a;if(s!==0){t.group(u=>{u.setTimeout(()=>{n(),u.dispose()},s),u.addEventListener(e,"transitionrun",d=>{d.target===d.currentTarget&&u.dispose()})});let l=t.addEventListener(e,"transitionend",u=>{u.target===u.currentTarget&&(n(),l())})}else n();return t.add(()=>n()),t.dispose}function so(e,n,t,r){let i=t?"enter":"leave",o=le(),a=r!==void 0?lo(r):()=>{};i==="enter"&&(e.removeAttribute("hidden"),e.style.display="");let s=F(i,{enter:()=>n.enter,leave:()=>n.leave}),l=F(i,{enter:()=>n.enterTo,leave:()=>n.leaveTo}),u=F(i,{enter:()=>n.enterFrom,leave:()=>n.leaveFrom});return zn(e,...n.base,...n.enter,...n.enterTo,...n.enterFrom,...n.leave,...n.leaveFrom,...n.leaveTo,...n.entered),$n(e,...n.base,...s,...u),o.nextFrame(()=>{zn(e,...n.base,...s,...u),$n(e,...n.base,...s,...l),is(e,()=>(zn(e,...n.base,...s),$n(e,...n.base,...n.entered),a()))}),o.dispose}function uo({immediate:e,container:n,direction:t,classes:r,onStart:i,onStop:o}){let a=Me(),s=ce(),l=ee(t);I(()=>{e&&(l.current="enter")},[e]),I(()=>{let u=le();s.add(u.dispose);let d=n.current;if(d&&l.current!=="idle"&&a.current)return u.dispose(),i.current(l.current),u.add(so(d,r.current,l.current==="enter",()=>{u.dispose(),o.current(l.current)})),u.dispose},[t])}function We(e=""){return e.split(/\s+/).filter(n=>n.length>1)}var on=(0,K.createContext)(null);on.displayName="TransitionContext";function as(){let e=(0,K.useContext)(on);if(e===null)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}function ls(){let e=(0,K.useContext)(an);if(e===null)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}var an=(0,K.createContext)(null);an.displayName="NestingContext";function ln(e){return"children"in e?ln(e.children):e.current.filter(({el:n})=>n.current!==null).filter(({state:n})=>n==="visible").length>0}function co(e,n){let t=ee(e),r=(0,K.useRef)([]),i=Me(),o=ce(),a=y((T,b=1)=>{let f=r.current.findIndex(({el:c})=>c===T);f!==-1&&(F(b,{[0](){r.current.splice(f,1)},[1](){r.current[f].state="hidden"}}),o.microTask(()=>{var c;!ln(r)&&i.current&&((c=t.current)==null||c.call(t))}))}),s=y(T=>{let b=r.current.find(({el:f})=>f===T);return b?b.state!=="visible"&&(b.state="visible"):r.current.push({el:T,state:"visible"}),()=>a(T,0)}),l=(0,K.useRef)([]),u=(0,K.useRef)(Promise.resolve()),d=(0,K.useRef)({enter:[],leave:[],idle:[]}),p=y((T,b,f)=>{l.current.splice(0),n&&(n.chains.current[b]=n.chains.current[b].filter(([c])=>c!==T)),n==null||n.chains.current[b].push([T,new Promise(c=>{l.current.push(c)})]),n==null||n.chains.current[b].push([T,new Promise(c=>{Promise.all(d.current[b].map(([g,v])=>v)).then(()=>c())})]),b==="enter"?u.current=u.current.then(()=>n==null?void 0:n.wait.current).then(()=>f(b)):f(b)}),m=y((T,b,f)=>{Promise.all(d.current[b].splice(0).map(([c,g])=>g)).then(()=>{var c;(c=l.current.shift())==null||c()}).then(()=>f(b))});return(0,K.useMemo)(()=>({children:r,register:s,unregister:a,onStart:p,onStop:m,wait:u,chains:d}),[s,a,r,p,m,d,u])}function ss(){}var us=["beforeEnter","afterEnter","beforeLeave","afterLeave"];function po(e){var t;let n={};for(let r of us)n[r]=(t=e[r])!=null?t:ss;return n}function ps(e){let n=(0,K.useRef)(po(e));return(0,K.useEffect)(()=>{n.current=po(e)},[e]),n}var ds="div",fo=1;function cs(e,n){var oe,de;let{beforeEnter:t,afterEnter:r,beforeLeave:i,afterLeave:o,enter:a,enterFrom:s,enterTo:l,entered:u,leave:d,leaveFrom:p,leaveTo:m,...T}=e,b=(0,K.useRef)(null),f=_(b,n),c=(oe=T.unmount)==null||oe?0:1,{show:g,appear:v,initial:E}=as(),[C,U]=(0,K.useState)(g?"visible":"hidden"),L=ls(),{register:w,unregister:P}=L;(0,K.useEffect)(()=>w(b),[w,b]),(0,K.useEffect)(()=>{if(c===1&&b.current){if(g&&C!=="visible"){U("visible");return}return F(C,{["hidden"]:()=>P(b),["visible"]:()=>w(b)})}},[C,b,w,P,g,c]);let O=ee({base:We(T.className),enter:We(a),enterFrom:We(s),enterTo:We(l),entered:We(u),leave:We(d),leaveFrom:We(p),leaveTo:We(m)}),R=ps({beforeEnter:t,afterEnter:r,beforeLeave:i,afterLeave:o}),h=De();(0,K.useEffect)(()=>{if(h&&C==="visible"&&b.current===null)throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?")},[b,C,h]);let S=E&&!v,G=v&&g&&E,Z=(()=>!h||S?"idle":g?"enter":"leave")(),x=nn(0),H=y(B=>F(B,{enter:()=>{x.addFlag(8),R.current.beforeEnter()},leave:()=>{x.addFlag(4),R.current.beforeLeave()},idle:()=>{}})),V=y(B=>F(B,{enter:()=>{x.removeFlag(8),R.current.afterEnter()},leave:()=>{x.removeFlag(4),R.current.afterLeave()},idle:()=>{}})),re=co(()=>{U("hidden"),P(b)},L),A=(0,K.useRef)(!1);uo({immediate:G,container:b,classes:O,direction:Z,onStart:ee(B=>{A.current=!0,re.onStart(b,B,H)}),onStop:ee(B=>{A.current=!1,re.onStop(b,B,V),B==="leave"&&!ln(re)&&(U("hidden"),P(b))})});let j=T,Y={ref:f};return G?j={...j,className:ut(T.className,...O.current.enter,...O.current.enterFrom)}:A.current&&(j.className=ut(T.className,(de=b.current)==null?void 0:de.className),j.className===""&&delete j.className),K.default.createElement(an.Provider,{value:re},K.default.createElement(Ce,{value:F(C,{["visible"]:1,["hidden"]:2})|x.flags},D({ourProps:Y,theirProps:j,defaultTag:ds,features:fo,visible:C==="visible",name:"Transition.Child"})))}function fs(e,n){let{show:t,appear:r=!1,unmount:i=!0,...o}=e,a=(0,K.useRef)(null),s=_(a,n);De();let l=Pe();if(t===void 0&&l!==null&&(t=(l&1)===1),![!0,!1].includes(t))throw new Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[u,d]=(0,K.useState)(t?"visible":"hidden"),p=co(()=>{d("hidden")}),[m,T]=(0,K.useState)(!0),b=(0,K.useRef)([t]);I(()=>{m!==!1&&b.current[b.current.length-1]!==t&&(b.current.push(t),T(!1))},[b,t]);let f=(0,K.useMemo)(()=>({show:t,appear:r,initial:m}),[t,r,m]);(0,K.useEffect)(()=>{if(t)d("visible");else if(!ln(p))d("hidden");else{let E=a.current;if(!E)return;let C=E.getBoundingClientRect();C.x===0&&C.y===0&&C.width===0&&C.height===0&&d("hidden")}},[t,p]);let c={unmount:i},g=y(()=>{var E;m&&T(!1),(E=e.beforeEnter)==null||E.call(e)}),v=y(()=>{var E;m&&T(!1),(E=e.beforeLeave)==null||E.call(e)});return K.default.createElement(an.Provider,{value:p},K.default.createElement(on.Provider,{value:f},D({ourProps:{...c,as:K.Fragment,children:K.default.createElement(mo,{ref:s,...c,...o,beforeEnter:g,beforeLeave:v})},theirProps:{},defaultTag:K.Fragment,features:fo,visible:u==="visible",name:"Transition"})))}function ms(e,n){let t=(0,K.useContext)(on)!==null,r=Pe()!==null;return K.default.createElement(K.default.Fragment,null,!t&&r?K.default.createElement(Xn,{ref:n,...e}):K.default.createElement(mo,{ref:n,...e}))}var Xn=M(fs),mo=M(cs),Ts=M(ms),bs=Object.assign(Xn,{Child:Ts,Root:Xn});
/*! Bundled license information:

@tanstack/react-virtual/build/lib/_virtual/_rollupPluginBabelHelpers.mjs:
  (**
   * react-virtual
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   *)

@tanstack/virtual-core/build/lib/_virtual/_rollupPluginBabelHelpers.mjs:
  (**
   * virtual-core
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   *)

@tanstack/virtual-core/build/lib/utils.mjs:
  (**
   * virtual-core
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   *)

@tanstack/virtual-core/build/lib/index.mjs:
  (**
   * virtual-core
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   *)

@tanstack/react-virtual/build/lib/index.mjs:
  (**
   * react-virtual
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   *)
*/
