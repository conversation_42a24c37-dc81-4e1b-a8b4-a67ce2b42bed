import m,{useMemo as d,useRef as M}from"react";import{Features as H,Hidden as T}from'../internal/hidden.js';import{useEvent as E}from'./use-event.js';import{useOwnerDocument as b}from'./use-owner.js';function N({defaultContainers:o=[],portals:r,mainTreeNodeRef:u}={}){var f;let t=M((f=u==null?void 0:u.current)!=null?f:null),l=b(t),c=E(()=>{var i,s,a;let n=[];for(let e of o)e!==null&&(e instanceof HTMLElement?n.push(e):"current"in e&&e.current instanceof HTMLElement&&n.push(e.current));if(r!=null&&r.current)for(let e of r.current)n.push(e);for(let e of(i=l==null?void 0:l.querySelectorAll("html > *, body > *"))!=null?i:[])e!==document.body&&e!==document.head&&e instanceof HTMLElement&&e.id!=="headlessui-portal-root"&&(e.contains(t.current)||e.contains((a=(s=t.current)==null?void 0:s.getRootNode())==null?void 0:a.host)||n.some(L=>e.contains(L))||n.push(e));return n});return{resolveContainers:c,contains:E(n=>c().some(i=>i.contains(n))),mainTreeNodeRef:t,MainTreeNode:d(()=>function(){return u!=null?null:m.createElement(T,{features:H.Hidden,ref:t})},[t,u])}}function y(){let o=M(null);return{mainTreeNodeRef:o,MainTreeNode:d(()=>function(){return m.createElement(T,{features:H.Hidden,ref:o})},[o])}}export{y as useMainTreeNode,N as useRootContainers};
