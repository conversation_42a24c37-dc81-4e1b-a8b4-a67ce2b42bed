!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react"),require("react-dom/test-utils"),require("react-dom"),require("react-dom/client")):"function"==typeof define&&define.amd?define(["exports","react","react-dom/test-utils","react-dom","react-dom/client"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).TestingLibraryReact={},e.<PERSON>act,e.ReactTestUtils,e.ReactDOM,e.ReactDOMClient)}(this,(function(e,t,r,n,o){"use strict";function a(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function i(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}function l(e,t){return t.forEach((function(t){t&&"string"!=typeof t&&!Array.isArray(t)&&Object.keys(t).forEach((function(r){if("default"!==r&&!(r in e)){var n=Object.getOwnPropertyDescriptor(t,r);Object.defineProperty(e,r,n.get?n:{enumerable:!0,get:function(){return t[r]}})}}))})),Object.freeze(e)}var u=i(t),s=i(r),c=a(n),d=i(o);const p="function"==typeof u.act?u.act:s.act;function f(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("unable to locate global object")}function b(e){f().IS_REACT_ACT_ENVIRONMENT=e}function m(){return f().IS_REACT_ACT_ENVIRONMENT}const y=(v=p,e=>{const t=m();b(!0);try{let r=!1;const n=v((()=>{const t=e();return null!==t&&"object"==typeof t&&"function"==typeof t.then&&(r=!0),t}));return r?{then:(e,r)=>{n.then((r=>{b(t),e(r)}),(e=>{b(t),r(e)}))}}:(b(t),n)}catch(e){throw b(t),e}});var v,h="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function g(e){var t=e.default;if("function"==typeof t){var r=function(){return t.apply(this,arguments)};r.prototype=t.prototype}else r={};return Object.defineProperty(r,"__esModule",{value:!0}),Object.keys(e).forEach((function(t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})})),r}var P={},C={exports:{}};!function(e){const t=function(e){return void 0===e&&(e=0),t=>`[${38+e};5;${t}m`},r=function(e){return void 0===e&&(e=0),(t,r,n)=>`[${38+e};2;${t};${r};${n}m`};Object.defineProperty(e,"exports",{enumerable:!0,get:function(){const e=new Map,n={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}};n.color.gray=n.color.blackBright,n.bgColor.bgGray=n.bgColor.bgBlackBright,n.color.grey=n.color.blackBright,n.bgColor.bgGrey=n.bgColor.bgBlackBright;for(const[t,r]of Object.entries(n)){for(const[t,o]of Object.entries(r))n[t]={open:`[${o[0]}m`,close:`[${o[1]}m`},r[t]=n[t],e.set(o[0],o[1]);Object.defineProperty(n,t,{value:r,enumerable:!1})}return Object.defineProperty(n,"codes",{value:e,enumerable:!1}),n.color.close="[39m",n.bgColor.close="[49m",n.color.ansi256=t(),n.color.ansi16m=r(),n.bgColor.ansi256=t(10),n.bgColor.ansi16m=r(10),Object.defineProperties(n,{rgbToAnsi256:{value:(e,t,r)=>e===t&&t===r?e<8?16:e>248?231:Math.round((e-8)/247*24)+232:16+36*Math.round(e/255*5)+6*Math.round(t/255*5)+Math.round(r/255*5),enumerable:!1},hexToRgb:{value:e=>{const t=/(?<colorString>[a-f\d]{6}|[a-f\d]{3})/i.exec(e.toString(16));if(!t)return[0,0,0];let{colorString:r}=t.groups;3===r.length&&(r=r.split("").map((e=>e+e)).join(""));const n=Number.parseInt(r,16);return[n>>16&255,n>>8&255,255&n]},enumerable:!1},hexToAnsi256:{value:e=>n.rgbToAnsi256(...n.hexToRgb(e)),enumerable:!1}}),n}})}(C);var w={};Object.defineProperty(w,"__esModule",{value:!0}),w.printIteratorEntries=function(e,t,r,n,o,a,i){void 0===i&&(i=": ");let l="",u=e.next();if(!u.done){l+=t.spacingOuter;const s=r+t.indent;for(;!u.done;){l+=s+a(u.value[0],t,s,n,o)+i+a(u.value[1],t,s,n,o),u=e.next(),u.done?t.min||(l+=","):l+=","+t.spacingInner}l+=t.spacingOuter+r}return l},w.printIteratorValues=function(e,t,r,n,o,a){let i="",l=e.next();if(!l.done){i+=t.spacingOuter;const u=r+t.indent;for(;!l.done;)i+=u+a(l.value,t,u,n,o),l=e.next(),l.done?t.min||(i+=","):i+=","+t.spacingInner;i+=t.spacingOuter+r}return i},w.printListItems=function(e,t,r,n,o,a){let i="";if(e.length){i+=t.spacingOuter;const l=r+t.indent;for(let r=0;r<e.length;r++)i+=l,r in e&&(i+=a(e[r],t,l,n,o)),r<e.length-1?i+=","+t.spacingInner:t.min||(i+=",");i+=t.spacingOuter+r}return i},w.printObjectProperties=function(e,t,r,n,o,a){let i="";const l=q(e,t.compareKeys);if(l.length){i+=t.spacingOuter;const u=r+t.indent;for(let r=0;r<l.length;r++){const s=l[r];i+=u+a(s,t,u,n,o)+": "+a(e[s],t,u,n,o),r<l.length-1?i+=","+t.spacingInner:t.min||(i+=",")}i+=t.spacingOuter+r}return i};const q=(e,t)=>{const r=Object.keys(e).sort(t);return Object.getOwnPropertySymbols&&Object.getOwnPropertySymbols(e).forEach((t=>{Object.getOwnPropertyDescriptor(e,t).enumerable&&r.push(t)})),r};var E={};Object.defineProperty(E,"__esModule",{value:!0}),E.test=E.serialize=E.default=void 0;var x=w,O="undefined"!=typeof globalThis?globalThis:void 0!==O?O:"undefined"!=typeof self?self:"undefined"!=typeof window?window:Function("return this")(),R=O["jest-symbol-do-not-touch"]||O.Symbol;const j="function"==typeof R&&R.for?R.for("jest.asymmetricMatcher"):1267621,S=" ",A=(e,t,r,n,o,a)=>{const i=e.toString();return"ArrayContaining"===i||"ArrayNotContaining"===i?++n>t.maxDepth?"["+i+"]":i+S+"["+(0,x.printListItems)(e.sample,t,r,n,o,a)+"]":"ObjectContaining"===i||"ObjectNotContaining"===i?++n>t.maxDepth?"["+i+"]":i+S+"{"+(0,x.printObjectProperties)(e.sample,t,r,n,o,a)+"}":"StringMatching"===i||"StringNotMatching"===i||"StringContaining"===i||"StringNotContaining"===i?i+S+a(e.sample,t,r,n,o):e.toAsymmetricMatcher()};E.serialize=A;const _=e=>e&&e.$$typeof===j;E.test=_;var T={serialize:A,test:_};E.default=T;var M={};Object.defineProperty(M,"__esModule",{value:!0}),M.test=M.serialize=M.default=void 0;var I=k((function(e){let{onlyFirst:t=!1}=void 0===e?{}:e;const r=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(r,t?void 0:"g")})),B=k(C.exports);function k(e){return e&&e.__esModule?e:{default:e}}const F=e=>"string"==typeof e&&!!e.match((0,I.default)());M.test=F;const N=(e,t,r,n,o,a)=>a(e.replace((0,I.default)(),(e=>{switch(e){case B.default.red.close:case B.default.green.close:case B.default.cyan.close:case B.default.gray.close:case B.default.white.close:case B.default.yellow.close:case B.default.bgRed.close:case B.default.bgGreen.close:case B.default.bgYellow.close:case B.default.inverse.close:case B.default.dim.close:case B.default.bold.close:case B.default.reset.open:case B.default.reset.close:return"</>";case B.default.red.open:return"<red>";case B.default.green.open:return"<green>";case B.default.cyan.open:return"<cyan>";case B.default.gray.open:return"<gray>";case B.default.white.open:return"<white>";case B.default.yellow.open:return"<yellow>";case B.default.bgRed.open:return"<bgRed>";case B.default.bgGreen.open:return"<bgGreen>";case B.default.bgYellow.open:return"<bgYellow>";case B.default.inverse.open:return"<inverse>";case B.default.dim.open:return"<dim>";case B.default.bold.open:return"<bold>";default:return""}})),t,r,n,o);M.serialize=N;var L={serialize:N,test:F};M.default=L;var U={};Object.defineProperty(U,"__esModule",{value:!0}),U.test=U.serialize=U.default=void 0;var D=w;const H=["DOMStringMap","NamedNodeMap"],$=/^(HTML\w*Collection|NodeList)$/,W=e=>{return e&&e.constructor&&!!e.constructor.name&&(t=e.constructor.name,-1!==H.indexOf(t)||$.test(t));var t};U.test=W;const z=(e,t,r,n,o,a)=>{const i=e.constructor.name;return++n>t.maxDepth?"["+i+"]":(t.min?"":i+" ")+(-1!==H.indexOf(i)?"{"+(0,D.printObjectProperties)((e=>"NamedNodeMap"===e.constructor.name)(e)?Array.from(e).reduce(((e,t)=>(e[t.name]=t.value,e)),{}):{...e},t,r,n,o,a)+"}":"["+(0,D.printListItems)(Array.from(e),t,r,n,o,a)+"]")};U.serialize=z;var V={serialize:z,test:W};U.default=V;var G={},J={},Q={};Object.defineProperty(Q,"__esModule",{value:!0}),Q.default=function(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;")},Object.defineProperty(J,"__esModule",{value:!0}),J.printText=J.printProps=J.printElementAsLeaf=J.printElement=J.printComment=J.printChildren=void 0;var X,K=(X=Q)&&X.__esModule?X:{default:X};J.printProps=(e,t,r,n,o,a,i)=>{const l=n+r.indent,u=r.colors;return e.map((e=>{const s=t[e];let c=i(s,r,l,o,a);return"string"!=typeof s&&(-1!==c.indexOf("\n")&&(c=r.spacingOuter+l+c+r.spacingOuter+n),c="{"+c+"}"),r.spacingInner+n+u.prop.open+e+u.prop.close+"="+u.value.open+c+u.value.close})).join("")};J.printChildren=(e,t,r,n,o,a)=>e.map((e=>t.spacingOuter+r+("string"==typeof e?Y(e,t):a(e,t,r,n,o)))).join("");const Y=(e,t)=>{const r=t.colors.content;return r.open+(0,K.default)(e)+r.close};J.printText=Y;J.printComment=(e,t)=>{const r=t.colors.comment;return r.open+"\x3c!--"+(0,K.default)(e)+"--\x3e"+r.close};J.printElement=(e,t,r,n,o)=>{const a=n.colors.tag;return a.open+"<"+e+(t&&a.close+t+n.spacingOuter+o+a.open)+(r?">"+a.close+r+n.spacingOuter+o+a.open+"</"+e:(t&&!n.min?"":" ")+"/")+">"+a.close};J.printElementAsLeaf=(e,t)=>{const r=t.colors.tag;return r.open+"<"+e+r.close+" …"+r.open+" />"+r.close},Object.defineProperty(G,"__esModule",{value:!0}),G.test=G.serialize=G.default=void 0;var Z=J;const ee=/^((HTML|SVG)\w*)?Element$/,te=e=>{var t;return(null==e||null===(t=e.constructor)||void 0===t?void 0:t.name)&&(e=>{const t=e.constructor.name,{nodeType:r,tagName:n}=e,o="string"==typeof n&&n.includes("-")||(e=>{try{return"function"==typeof e.hasAttribute&&e.hasAttribute("is")}catch{return!1}})(e);return 1===r&&(ee.test(t)||o)||3===r&&"Text"===t||8===r&&"Comment"===t||11===r&&"DocumentFragment"===t})(e)};function re(e){return 11===e.nodeType}G.test=te;const ne=(e,t,r,n,o,a)=>{if(function(e){return 3===e.nodeType}(e))return(0,Z.printText)(e.data,t);if(function(e){return 8===e.nodeType}(e))return(0,Z.printComment)(e.data,t);const i=re(e)?"DocumentFragment":e.tagName.toLowerCase();return++n>t.maxDepth?(0,Z.printElementAsLeaf)(i,t):(0,Z.printElement)(i,(0,Z.printProps)(re(e)?[]:Array.from(e.attributes).map((e=>e.name)).sort(),re(e)?{}:Array.from(e.attributes).reduce(((e,t)=>(e[t.name]=t.value,e)),{}),t,r+t.indent,n,o,a),(0,Z.printChildren)(Array.prototype.slice.call(e.childNodes||e.children),t,r+t.indent,n,o,a),t,r)};G.serialize=ne;var oe={serialize:ne,test:te};G.default=oe;var ae={};Object.defineProperty(ae,"__esModule",{value:!0}),ae.test=ae.serialize=ae.default=void 0;var ie=w;const le="@@__IMMUTABLE_ORDERED__@@",ue=e=>"Immutable."+e,se=e=>"["+e+"]",ce=" ";const de=(e,t,r,n,o,a,i)=>++n>t.maxDepth?se(ue(i)):ue(i)+ce+"["+(0,ie.printIteratorValues)(e.values(),t,r,n,o,a)+"]",pe=(e,t,r,n,o,a)=>e["@@__IMMUTABLE_MAP__@@"]?((e,t,r,n,o,a,i)=>++n>t.maxDepth?se(ue(i)):ue(i)+ce+"{"+(0,ie.printIteratorEntries)(e.entries(),t,r,n,o,a)+"}")(e,t,r,n,o,a,e[le]?"OrderedMap":"Map"):e["@@__IMMUTABLE_LIST__@@"]?de(e,t,r,n,o,a,"List"):e["@@__IMMUTABLE_SET__@@"]?de(e,t,r,n,o,a,e[le]?"OrderedSet":"Set"):e["@@__IMMUTABLE_STACK__@@"]?de(e,t,r,n,o,a,"Stack"):e["@@__IMMUTABLE_SEQ__@@"]?((e,t,r,n,o,a)=>{const i=ue("Seq");return++n>t.maxDepth?se(i):e["@@__IMMUTABLE_KEYED__@@"]?i+ce+"{"+(e._iter||e._object?(0,ie.printIteratorEntries)(e.entries(),t,r,n,o,a):"…")+"}":i+ce+"["+(e._iter||e._array||e._collection||e._iterable?(0,ie.printIteratorValues)(e.values(),t,r,n,o,a):"…")+"]"})(e,t,r,n,o,a):((e,t,r,n,o,a)=>{const i=ue(e._name||"Record");return++n>t.maxDepth?se(i):i+ce+"{"+(0,ie.printIteratorEntries)(function(e){let t=0;return{next(){if(t<e._keys.length){const r=e._keys[t++];return{done:!1,value:[r,e.get(r)]}}return{done:!0,value:void 0}}}}(e),t,r,n,o,a)+"}"})(e,t,r,n,o,a);ae.serialize=pe;const fe=e=>e&&(!0===e["@@__IMMUTABLE_ITERABLE__@@"]||!0===e["@@__IMMUTABLE_RECORD__@@"]);ae.test=fe;var be={serialize:pe,test:fe};ae.default=be;var me,ye={},ve={exports:{}},he={};!function(e){e.exports=function(){if(me)return he;me=1;var e=60103,t=60106,r=60107,n=60108,o=60114,a=60109,i=60110,l=60112,u=60113,s=60120,c=60115,d=60116,p=60121,f=60122,b=60117,m=60129,y=60131;if("function"==typeof Symbol&&Symbol.for){var v=Symbol.for;e=v("react.element"),t=v("react.portal"),r=v("react.fragment"),n=v("react.strict_mode"),o=v("react.profiler"),a=v("react.provider"),i=v("react.context"),l=v("react.forward_ref"),u=v("react.suspense"),s=v("react.suspense_list"),c=v("react.memo"),d=v("react.lazy"),p=v("react.block"),f=v("react.server.block"),b=v("react.fundamental"),m=v("react.debug_trace_mode"),y=v("react.legacy_hidden")}function h(p){if("object"==typeof p&&null!==p){var f=p.$$typeof;switch(f){case e:switch(p=p.type){case r:case o:case n:case u:case s:return p;default:switch(p=p&&p.$$typeof){case i:case l:case d:case c:case a:return p;default:return f}}case t:return f}}}var g=a,P=e,C=l,w=r,q=d,E=c,x=t,O=o,R=n,j=u;return he.ContextConsumer=i,he.ContextProvider=g,he.Element=P,he.ForwardRef=C,he.Fragment=w,he.Lazy=q,he.Memo=E,he.Portal=x,he.Profiler=O,he.StrictMode=R,he.Suspense=j,he.isAsyncMode=function(){return!1},he.isConcurrentMode=function(){return!1},he.isContextConsumer=function(e){return h(e)===i},he.isContextProvider=function(e){return h(e)===a},he.isElement=function(t){return"object"==typeof t&&null!==t&&t.$$typeof===e},he.isForwardRef=function(e){return h(e)===l},he.isFragment=function(e){return h(e)===r},he.isLazy=function(e){return h(e)===d},he.isMemo=function(e){return h(e)===c},he.isPortal=function(e){return h(e)===t},he.isProfiler=function(e){return h(e)===o},he.isStrictMode=function(e){return h(e)===n},he.isSuspense=function(e){return h(e)===u},he.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===r||e===o||e===m||e===n||e===u||e===s||e===y||"object"==typeof e&&null!==e&&(e.$$typeof===d||e.$$typeof===c||e.$$typeof===a||e.$$typeof===i||e.$$typeof===l||e.$$typeof===b||e.$$typeof===p||e[0]===f)},he.typeOf=h,he}()}(ve),Object.defineProperty(ye,"__esModule",{value:!0}),ye.test=ye.serialize=ye.default=void 0;var ge=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=Ce(t);if(r&&r.has(e))return r.get(e);var n={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var i=o?Object.getOwnPropertyDescriptor(e,a):null;i&&(i.get||i.set)?Object.defineProperty(n,a,i):n[a]=e[a]}n.default=e,r&&r.set(e,n);return n}(ve.exports),Pe=J;function Ce(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(Ce=function(e){return e?r:t})(e)}const we=function(e,t){return void 0===t&&(t=[]),Array.isArray(e)?e.forEach((e=>{we(e,t)})):null!=e&&!1!==e&&t.push(e),t},qe=e=>{const t=e.type;if("string"==typeof t)return t;if("function"==typeof t)return t.displayName||t.name||"Unknown";if(ge.isFragment(e))return"React.Fragment";if(ge.isSuspense(e))return"React.Suspense";if("object"==typeof t&&null!==t){if(ge.isContextProvider(e))return"Context.Provider";if(ge.isContextConsumer(e))return"Context.Consumer";if(ge.isForwardRef(e)){if(t.displayName)return t.displayName;const e=t.render.displayName||t.render.name||"";return""!==e?"ForwardRef("+e+")":"ForwardRef"}if(ge.isMemo(e)){const e=t.displayName||t.type.displayName||t.type.name||"";return""!==e?"Memo("+e+")":"Memo"}}return"UNDEFINED"},Ee=(e,t,r,n,o,a)=>++n>t.maxDepth?(0,Pe.printElementAsLeaf)(qe(e),t):(0,Pe.printElement)(qe(e),(0,Pe.printProps)((e=>{const{props:t}=e;return Object.keys(t).filter((e=>"children"!==e&&void 0!==t[e])).sort()})(e),e.props,t,r+t.indent,n,o,a),(0,Pe.printChildren)(we(e.props.children),t,r+t.indent,n,o,a),t,r);ye.serialize=Ee;const xe=e=>null!=e&&ge.isElement(e);ye.test=xe;var Oe={serialize:Ee,test:xe};ye.default=Oe;var Re={};Object.defineProperty(Re,"__esModule",{value:!0}),Re.test=Re.serialize=Re.default=void 0;var je=J,Se="undefined"!=typeof globalThis?globalThis:void 0!==Se?Se:"undefined"!=typeof self?self:"undefined"!=typeof window?window:Function("return this")(),Ae=Se["jest-symbol-do-not-touch"]||Se.Symbol;const _e="function"==typeof Ae&&Ae.for?Ae.for("react.test.json"):245830487,Te=(e,t,r,n,o,a)=>++n>t.maxDepth?(0,je.printElementAsLeaf)(e.type,t):(0,je.printElement)(e.type,e.props?(0,je.printProps)((e=>{const{props:t}=e;return t?Object.keys(t).filter((e=>void 0!==t[e])).sort():[]})(e),e.props,t,r+t.indent,n,o,a):"",e.children?(0,je.printChildren)(e.children,t,r+t.indent,n,o,a):"",t,r);Re.serialize=Te;const Me=e=>e&&e.$$typeof===_e;Re.test=Me;var Ie={serialize:Te,test:Me};Re.default=Ie,Object.defineProperty(P,"__esModule",{value:!0});var Be=P.default=P.DEFAULT_OPTIONS=void 0,ke=P.format=Pt,Fe=P.plugins=void 0,Ne=Ge(C.exports),Le=w,Ue=Ge(E),De=Ge(M),He=Ge(U),$e=Ge(G),We=Ge(ae),ze=Ge(ye),Ve=Ge(Re);function Ge(e){return e&&e.__esModule?e:{default:e}}const Je=Object.prototype.toString,Qe=Date.prototype.toISOString,Xe=Error.prototype.toString,Ke=RegExp.prototype.toString,Ye=e=>"function"==typeof e.constructor&&e.constructor.name||"Object",Ze=e=>"undefined"!=typeof window&&e===window,et=/^Symbol\((.*)\)(.*)$/,tt=/\n/gi;class rt extends Error{constructor(e,t){super(e),this.stack=t,this.name=this.constructor.name}}function nt(e,t){return t?"[Function "+(e.name||"anonymous")+"]":"[Function]"}function ot(e){return String(e).replace(et,"Symbol($1)")}function at(e){return"["+Xe.call(e)+"]"}function it(e,t,r,n){if(!0===e||!1===e)return""+e;if(void 0===e)return"undefined";if(null===e)return"null";const o=typeof e;if("number"===o)return function(e){return Object.is(e,-0)?"-0":String(e)}(e);if("bigint"===o)return function(e){return String(`${e}n`)}(e);if("string"===o)return n?'"'+e.replace(/"|\\/g,"\\$&")+'"':'"'+e+'"';if("function"===o)return nt(e,t);if("symbol"===o)return ot(e);const a=Je.call(e);return"[object WeakMap]"===a?"WeakMap {}":"[object WeakSet]"===a?"WeakSet {}":"[object Function]"===a||"[object GeneratorFunction]"===a?nt(e,t):"[object Symbol]"===a?ot(e):"[object Date]"===a?isNaN(+e)?"Date { NaN }":Qe.call(e):"[object Error]"===a?at(e):"[object RegExp]"===a?r?Ke.call(e).replace(/[\\^$*+?.()|[\]{}]/g,"\\$&"):Ke.call(e):e instanceof Error?at(e):null}function lt(e,t,r,n,o,a){if(-1!==o.indexOf(e))return"[Circular]";(o=o.slice()).push(e);const i=++n>t.maxDepth,l=t.min;if(t.callToJSON&&!i&&e.toJSON&&"function"==typeof e.toJSON&&!a)return ct(e.toJSON(),t,r,n,o,!0);const u=Je.call(e);return"[object Arguments]"===u?i?"[Arguments]":(l?"":"Arguments ")+"["+(0,Le.printListItems)(e,t,r,n,o,ct)+"]":function(e){return"[object Array]"===e||"[object ArrayBuffer]"===e||"[object DataView]"===e||"[object Float32Array]"===e||"[object Float64Array]"===e||"[object Int8Array]"===e||"[object Int16Array]"===e||"[object Int32Array]"===e||"[object Uint8Array]"===e||"[object Uint8ClampedArray]"===e||"[object Uint16Array]"===e||"[object Uint32Array]"===e}(u)?i?"["+e.constructor.name+"]":(l?"":t.printBasicPrototype||"Array"!==e.constructor.name?e.constructor.name+" ":"")+"["+(0,Le.printListItems)(e,t,r,n,o,ct)+"]":"[object Map]"===u?i?"[Map]":"Map {"+(0,Le.printIteratorEntries)(e.entries(),t,r,n,o,ct," => ")+"}":"[object Set]"===u?i?"[Set]":"Set {"+(0,Le.printIteratorValues)(e.values(),t,r,n,o,ct)+"}":i||Ze(e)?"["+Ye(e)+"]":(l?"":t.printBasicPrototype||"Object"!==Ye(e)?Ye(e)+" ":"")+"{"+(0,Le.printObjectProperties)(e,t,r,n,o,ct)+"}"}function ut(e,t,r,n,o,a){let i;try{i=function(e){return null!=e.serialize}(e)?e.serialize(t,r,n,o,a,ct):e.print(t,(e=>ct(e,r,n,o,a)),(e=>{const t=n+r.indent;return t+e.replace(tt,"\n"+t)}),{edgeSpacing:r.spacingOuter,min:r.min,spacing:r.spacingInner},r.colors)}catch(e){throw new rt(e.message,e.stack)}if("string"!=typeof i)throw new Error(`pretty-format: Plugin must return type "string" but instead returned "${typeof i}".`);return i}function st(e,t){for(let r=0;r<e.length;r++)try{if(e[r].test(t))return e[r]}catch(e){throw new rt(e.message,e.stack)}return null}function ct(e,t,r,n,o,a){const i=st(t.plugins,e);if(null!==i)return ut(i,e,t,r,n,o);const l=it(e,t.printFunctionName,t.escapeRegex,t.escapeString);return null!==l?l:lt(e,t,r,n,o,a)}const dt={comment:"gray",content:"reset",prop:"yellow",tag:"cyan",value:"green"},pt=Object.keys(dt),ft={callToJSON:!0,compareKeys:void 0,escapeRegex:!1,escapeString:!0,highlight:!1,indent:2,maxDepth:1/0,min:!1,plugins:[],printBasicPrototype:!0,printFunctionName:!0,theme:dt};var bt=P.DEFAULT_OPTIONS=ft;const mt=e=>pt.reduce(((t,r)=>{const n=e.theme&&void 0!==e.theme[r]?e.theme[r]:dt[r],o=n&&Ne.default[n];if(!o||"string"!=typeof o.close||"string"!=typeof o.open)throw new Error(`pretty-format: Option "theme" has a key "${r}" whose value "${n}" is undefined in ansi-styles.`);return t[r]=o,t}),Object.create(null)),yt=e=>e&&void 0!==e.printFunctionName?e.printFunctionName:ft.printFunctionName,vt=e=>e&&void 0!==e.escapeRegex?e.escapeRegex:ft.escapeRegex,ht=e=>e&&void 0!==e.escapeString?e.escapeString:ft.escapeString,gt=e=>{var t,r;return{callToJSON:e&&void 0!==e.callToJSON?e.callToJSON:ft.callToJSON,colors:e&&e.highlight?mt(e):pt.reduce(((e,t)=>(e[t]={close:"",open:""},e)),Object.create(null)),compareKeys:e&&"function"==typeof e.compareKeys?e.compareKeys:ft.compareKeys,escapeRegex:vt(e),escapeString:ht(e),indent:e&&e.min?"":(r=e&&void 0!==e.indent?e.indent:ft.indent,new Array(r+1).join(" ")),maxDepth:e&&void 0!==e.maxDepth?e.maxDepth:ft.maxDepth,min:e&&void 0!==e.min?e.min:ft.min,plugins:e&&void 0!==e.plugins?e.plugins:ft.plugins,printBasicPrototype:null===(t=null==e?void 0:e.printBasicPrototype)||void 0===t||t,printFunctionName:yt(e),spacingInner:e&&e.min?" ":"\n",spacingOuter:e&&e.min?"":"\n"}};function Pt(e,t){if(t&&(function(e){if(Object.keys(e).forEach((e=>{if(!ft.hasOwnProperty(e))throw new Error(`pretty-format: Unknown option "${e}".`)})),e.min&&void 0!==e.indent&&0!==e.indent)throw new Error('pretty-format: Options "min" and "indent" cannot be used together.');if(void 0!==e.theme){if(null===e.theme)throw new Error('pretty-format: Option "theme" must not be null.');if("object"!=typeof e.theme)throw new Error(`pretty-format: Option "theme" must be of type "object" but instead received "${typeof e.theme}".`)}}(t),t.plugins)){const r=st(t.plugins,e);if(null!==r)return ut(r,e,gt(t),"",0,[])}const r=it(e,yt(t),vt(t),ht(t));return null!==r?r:lt(e,gt(t),"",0,[])}const Ct={AsymmetricMatcher:Ue.default,ConvertAnsi:De.default,DOMCollection:He.default,DOMElement:$e.default,Immutable:We.default,ReactElement:ze.default,ReactTestComponent:Ve.default};Fe=P.plugins=Ct;var wt=Pt;Be=P.default=wt;var qt=l({__proto__:null,get DEFAULT_OPTIONS(){return bt},format:ke,get plugins(){return Fe},get default(){return Be}},[P]),Et=Object.prototype.toString;function xt(e){return"function"==typeof e||"[object Function]"===Et.call(e)}var Ot=Math.pow(2,53)-1;function Rt(e){var t=function(e){var t=Number(e);return isNaN(t)?0:0!==t&&isFinite(t)?(t>0?1:-1)*Math.floor(Math.abs(t)):t}(e);return Math.min(Math.max(t,0),Ot)}function jt(e,t){var r=Array,n=Object(e);if(null==e)throw new TypeError("Array.from requires an array-like object - not null or undefined");if(void 0!==t&&!xt(t))throw new TypeError("Array.from: when provided, the second argument must be a function");for(var o,a=Rt(n.length),i=xt(r)?Object(new r(a)):new Array(a),l=0;l<a;)o=n[l],i[l]=t?t(o,l):o,l+=1;return i.length=a,i}function St(e){return St="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},St(e)}function At(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,_t(n.key),n)}}function _t(e){var t=function(e,t){if("object"!==St(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==St(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===St(t)?t:String(t)}var Tt=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),function(e,t,r){(t=_t(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(this,"items",void 0),this.items=t}var t,r,n;return t=e,(r=[{key:"add",value:function(e){return!1===this.has(e)&&this.items.push(e),this}},{key:"clear",value:function(){this.items=[]}},{key:"delete",value:function(e){var t=this.items.length;return this.items=this.items.filter((function(t){return t!==e})),t!==this.items.length}},{key:"forEach",value:function(e){var t=this;this.items.forEach((function(r){e(r,r,t)}))}},{key:"has",value:function(e){return-1!==this.items.indexOf(e)}},{key:"size",get:function(){return this.items.length}}])&&At(t.prototype,r),n&&At(t,n),Object.defineProperty(t,"prototype",{writable:!1}),e}(),Mt="undefined"==typeof Set?Set:Tt;function It(e){var t;return null!==(t=e.localName)&&void 0!==t?t:e.tagName.toLowerCase()}var Bt={article:"article",aside:"complementary",button:"button",datalist:"listbox",dd:"definition",details:"group",dialog:"dialog",dt:"term",fieldset:"group",figure:"figure",form:"form",footer:"contentinfo",h1:"heading",h2:"heading",h3:"heading",h4:"heading",h5:"heading",h6:"heading",header:"banner",hr:"separator",html:"document",legend:"legend",li:"listitem",math:"math",main:"main",menu:"list",nav:"navigation",ol:"list",optgroup:"group",option:"option",output:"status",progress:"progressbar",section:"region",summary:"button",table:"table",tbody:"rowgroup",textarea:"textbox",tfoot:"rowgroup",td:"cell",th:"columnheader",thead:"rowgroup",tr:"row",ul:"list"},kt={caption:new Set(["aria-label","aria-labelledby"]),code:new Set(["aria-label","aria-labelledby"]),deletion:new Set(["aria-label","aria-labelledby"]),emphasis:new Set(["aria-label","aria-labelledby"]),generic:new Set(["aria-label","aria-labelledby","aria-roledescription"]),insertion:new Set(["aria-label","aria-labelledby"]),paragraph:new Set(["aria-label","aria-labelledby"]),presentation:new Set(["aria-label","aria-labelledby"]),strong:new Set(["aria-label","aria-labelledby"]),subscript:new Set(["aria-label","aria-labelledby"]),superscript:new Set(["aria-label","aria-labelledby"])};function Ft(e,t){return function(e,t){return["aria-atomic","aria-busy","aria-controls","aria-current","aria-describedby","aria-details","aria-dropeffect","aria-flowto","aria-grabbed","aria-hidden","aria-keyshortcuts","aria-label","aria-labelledby","aria-live","aria-owns","aria-relevant","aria-roledescription"].some((function(r){var n;return e.hasAttribute(r)&&!(null!==(n=kt[t])&&void 0!==n&&n.has(r))}))}(e,t)}function Nt(e){var t=function(e){var t=e.getAttribute("role");if(null!==t){var r=t.trim().split(" ")[0];if(r.length>0)return r}return null}(e);if(null===t||"presentation"===t){var r=function(e){var t=Bt[It(e)];if(void 0!==t)return t;switch(It(e)){case"a":case"area":case"link":if(e.hasAttribute("href"))return"link";break;case"img":return""!==e.getAttribute("alt")||Ft(e,"img")?"img":"presentation";case"input":var r=e.type;switch(r){case"button":case"image":case"reset":case"submit":return"button";case"checkbox":case"radio":return r;case"range":return"slider";case"email":case"tel":case"text":case"url":return e.hasAttribute("list")?"combobox":"textbox";case"search":return e.hasAttribute("list")?"combobox":"searchbox";case"number":return"spinbutton";default:return null}case"select":return e.hasAttribute("multiple")||e.size>1?"listbox":"combobox"}return null}(e);if("presentation"!==t||Ft(e,r||""))return r}return t}function Lt(e){return null!==e&&e.nodeType===e.ELEMENT_NODE}function Ut(e){return Lt(e)&&"caption"===It(e)}function Dt(e){return Lt(e)&&"input"===It(e)}function Ht(e){return Lt(e)&&"legend"===It(e)}function $t(e){return function(e){return Lt(e)&&void 0!==e.ownerSVGElement}(e)&&"title"===It(e)}function Wt(e,t){if(Lt(e)&&e.hasAttribute(t)){var r=e.getAttribute(t).split(" "),n=e.getRootNode?e.getRootNode():e.ownerDocument;return r.map((function(e){return n.getElementById(e)})).filter((function(e){return null!==e}))}return[]}function zt(e,t){return!!Lt(e)&&-1!==t.indexOf(Nt(e))}function Vt(e,t){if(!Lt(e))return!1;if("range"===t)return zt(e,["meter","progressbar","scrollbar","slider","spinbutton"]);throw new TypeError("No knowledge about abstract role '".concat(t,"'. This is likely a bug :("))}function Gt(e,t){var r=jt(e.querySelectorAll(t));return Wt(e,"aria-owns").forEach((function(e){r.push.apply(r,jt(e.querySelectorAll(t)))})),r}function Jt(e){return Lt(t=e)&&"select"===It(t)?e.selectedOptions||Gt(e,"[selected]"):Gt(e,'[aria-selected="true"]');var t}function Qt(e){return Dt(e)||Lt(t=e)&&"textarea"===It(t)?e.value:e.textContent||"";var t}function Xt(e){var t=e.getPropertyValue("content");return/^["'].*["']$/.test(t)?t.slice(1,-1):""}function Kt(e){var t=It(e);return"button"===t||"input"===t&&"hidden"!==e.getAttribute("type")||"meter"===t||"output"===t||"progress"===t||"select"===t||"textarea"===t}function Yt(e){if(Kt(e))return e;var t=null;return e.childNodes.forEach((function(e){if(null===t&&Lt(e)){var r=Yt(e);null!==r&&(t=r)}})),t}function Zt(e){if(void 0!==e.control)return e.control;var t=e.getAttribute("for");return null!==t?e.ownerDocument.getElementById(t):Yt(e)}function er(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=new Mt,n=function(e){var t=(null===e.ownerDocument?e:e.ownerDocument).defaultView;if(null===t)throw new TypeError("no window available");return t}(e),o=t.compute,a=void 0===o?"name":o,i=t.computedStyleSupportsPseudoElements,l=void 0===i?void 0!==t.getComputedStyle:i,u=t.getComputedStyle,s=void 0===u?n.getComputedStyle.bind(n):u,c=t.hidden,d=void 0!==c&&c;function p(e,t){var r,n,o="";if(Lt(e)&&l){var a=Xt(s(e,"::before"));o="".concat(a," ").concat(o)}if((function(e){return Lt(e)&&"slot"===It(e)}(e)?0===(n=(r=e).assignedNodes()).length?jt(r.childNodes):n:jt(e.childNodes).concat(Wt(e,"aria-owns"))).forEach((function(e){var r=m(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!1,recursion:!0}),n="inline"!==(Lt(e)?s(e).getPropertyValue("display"):"inline")?" ":"";o+="".concat(n).concat(r).concat(n)})),Lt(e)&&l){var i=Xt(s(e,"::after"));o="".concat(o," ").concat(i)}return o.trim()}function f(e,t){var n=e.getAttributeNode(t);return null===n||r.has(n)||""===n.value.trim()?null:(r.add(n),n.value)}function b(e){if(!Lt(e))return null;if(function(e){return Lt(e)&&"fieldset"===It(e)}(e)){r.add(e);for(var t=jt(e.childNodes),n=0;n<t.length;n+=1){var o=t[n];if(Ht(o))return m(o,{isEmbeddedInLabel:!1,isReferenced:!1,recursion:!1})}}else if(function(e){return Lt(e)&&"table"===It(e)}(e)){r.add(e);for(var a=jt(e.childNodes),i=0;i<a.length;i+=1){var l=a[i];if(Ut(l))return m(l,{isEmbeddedInLabel:!1,isReferenced:!1,recursion:!1})}}else{if(function(e){return Lt(e)&&"svg"===It(e)}(e)){r.add(e);for(var u=jt(e.childNodes),s=0;s<u.length;s+=1){var c=u[s];if($t(c))return c.textContent}return null}if("img"===It(e)||"area"===It(e)){var d=f(e,"alt");if(null!==d)return d}else if(function(e){return Lt(e)&&"optgroup"===It(e)}(e)){var b=f(e,"label");if(null!==b)return b}}if(Dt(e)&&("button"===e.type||"submit"===e.type||"reset"===e.type)){var y=f(e,"value");if(null!==y)return y;if("submit"===e.type)return"Submit";if("reset"===e.type)return"Reset"}var v,h,g=null===(h=(v=e).labels)?h:void 0!==h?jt(h):Kt(v)?jt(v.ownerDocument.querySelectorAll("label")).filter((function(e){return Zt(e)===v})):null;if(null!==g&&0!==g.length)return r.add(e),jt(g).map((function(e){return m(e,{isEmbeddedInLabel:!0,isReferenced:!1,recursion:!0})})).filter((function(e){return e.length>0})).join(" ");if(Dt(e)&&"image"===e.type){var P=f(e,"alt");if(null!==P)return P;var C=f(e,"title");return null!==C?C:"Submit Query"}if(zt(e,["button"])){var w=p(e,{isEmbeddedInLabel:!1,isReferenced:!1});if(""!==w)return w}return null}function m(e,t){if(r.has(e))return"";if(!d&&function(e,t){if(!Lt(e))return!1;if(e.hasAttribute("hidden")||"true"===e.getAttribute("aria-hidden"))return!0;var r=t(e);return"none"===r.getPropertyValue("display")||"hidden"===r.getPropertyValue("visibility")}(e,s)&&!t.isReferenced)return r.add(e),"";var n=Lt(e)?e.getAttributeNode("aria-labelledby"):null,o=null===n||r.has(n)?[]:Wt(e,"aria-labelledby");if("name"===a&&!t.isReferenced&&o.length>0)return r.add(n),o.map((function(e){return m(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!0,recursion:!1})})).join(" ");var i,l=t.recursion&&(zt(i=e,["button","combobox","listbox","textbox"])||Vt(i,"range"))&&"name"===a;if(!l){var u=(Lt(e)&&e.getAttribute("aria-label")||"").trim();if(""!==u&&"name"===a)return r.add(e),u;if(!function(e){return zt(e,["none","presentation"])}(e)){var c=b(e);if(null!==c)return r.add(e),c}}if(zt(e,["menu"]))return r.add(e),"";if(l||t.isEmbeddedInLabel||t.isReferenced){if(zt(e,["combobox","listbox"])){r.add(e);var y=Jt(e);return 0===y.length?Dt(e)?e.value:"":jt(y).map((function(e){return m(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!1,recursion:!0})})).join(" ")}if(Vt(e,"range"))return r.add(e),e.hasAttribute("aria-valuetext")?e.getAttribute("aria-valuetext"):e.hasAttribute("aria-valuenow")?e.getAttribute("aria-valuenow"):e.getAttribute("value")||"";if(zt(e,["textbox"]))return r.add(e),Qt(e)}if(function(e){return zt(e,["button","cell","checkbox","columnheader","gridcell","heading","label","legend","link","menuitem","menuitemcheckbox","menuitemradio","option","radio","row","rowheader","switch","tab","tooltip","treeitem"])}(e)||Lt(e)&&t.isReferenced||function(e){return Ut(e)}(e)){var v=p(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!1});if(""!==v)return r.add(e),v}if(e.nodeType===e.TEXT_NODE)return r.add(e),e.textContent||"";if(t.recursion)return r.add(e),p(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!1});var h=function(e){return Lt(e)?f(e,"title"):null}(e);return null!==h?(r.add(e),h):(r.add(e),"")}return m(e,{isEmbeddedInLabel:!1,isReferenced:"description"===a,recursion:!1}).trim().replace(/\s\s+/g," ")}function tr(e){return tr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},tr(e)}function rr(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function nr(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?rr(Object(r),!0).forEach((function(t){or(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):rr(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function or(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==tr(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==tr(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===tr(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ar(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=Wt(e,"aria-describedby").map((function(e){return er(e,nr(nr({},t),{},{compute:"description"}))})).join(" ");if(""===r){var n=e.getAttribute("title");r=null===n?"":n}return r}function ir(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return zt(e,["caption","code","deletion","emphasis","generic","insertion","paragraph","presentation","strong","subscript","superscript"])?"":er(e,t)}var lr={},ur={},sr={},cr={};Object.defineProperty(cr,"__esModule",{value:!0}),cr.default=void 0;var dr=function(){var e=this,t=0,r={"@@iterator":function(){return r},next:function(){if(t<e.length){var r=e[t];return t+=1,{done:!1,value:r}}return{done:!0}}};return r};cr.default=dr,Object.defineProperty(sr,"__esModule",{value:!0}),sr.default=function(e,t){"function"==typeof Symbol&&"symbol"===fr(Symbol.iterator)&&Object.defineProperty(e,Symbol.iterator,{value:pr.default.bind(t)});return e};var pr=function(e){return e&&e.__esModule?e:{default:e}}(cr);function fr(e){return fr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},fr(e)}Object.defineProperty(ur,"__esModule",{value:!0}),ur.default=void 0;var br=function(e){return e&&e.__esModule?e:{default:e}}(sr);function mr(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,o,a=[],i=!0,l=!1;try{for(r=r.call(e);!(i=(n=r.next()).done)&&(a.push(n.value),!t||a.length!==t);i=!0);}catch(e){l=!0,o=e}finally{try{i||null==r.return||r.return()}finally{if(l)throw o}}return a}(e,t)||yr(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function yr(e,t){if(e){if("string"==typeof e)return vr(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?vr(e,t):void 0}}function vr(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var hr=[["aria-activedescendant",{type:"id"}],["aria-atomic",{type:"boolean"}],["aria-autocomplete",{type:"token",values:["inline","list","both","none"]}],["aria-busy",{type:"boolean"}],["aria-checked",{type:"tristate"}],["aria-colcount",{type:"integer"}],["aria-colindex",{type:"integer"}],["aria-colspan",{type:"integer"}],["aria-controls",{type:"idlist"}],["aria-current",{type:"token",values:["page","step","location","date","time",!0,!1]}],["aria-describedby",{type:"idlist"}],["aria-details",{type:"id"}],["aria-disabled",{type:"boolean"}],["aria-dropeffect",{type:"tokenlist",values:["copy","execute","link","move","none","popup"]}],["aria-errormessage",{type:"id"}],["aria-expanded",{type:"boolean",allowundefined:!0}],["aria-flowto",{type:"idlist"}],["aria-grabbed",{type:"boolean",allowundefined:!0}],["aria-haspopup",{type:"token",values:[!1,!0,"menu","listbox","tree","grid","dialog"]}],["aria-hidden",{type:"boolean",allowundefined:!0}],["aria-invalid",{type:"token",values:["grammar",!1,"spelling",!0]}],["aria-keyshortcuts",{type:"string"}],["aria-label",{type:"string"}],["aria-labelledby",{type:"idlist"}],["aria-level",{type:"integer"}],["aria-live",{type:"token",values:["assertive","off","polite"]}],["aria-modal",{type:"boolean"}],["aria-multiline",{type:"boolean"}],["aria-multiselectable",{type:"boolean"}],["aria-orientation",{type:"token",values:["vertical","undefined","horizontal"]}],["aria-owns",{type:"idlist"}],["aria-placeholder",{type:"string"}],["aria-posinset",{type:"integer"}],["aria-pressed",{type:"tristate"}],["aria-readonly",{type:"boolean"}],["aria-relevant",{type:"tokenlist",values:["additions","all","removals","text"]}],["aria-required",{type:"boolean"}],["aria-roledescription",{type:"string"}],["aria-rowcount",{type:"integer"}],["aria-rowindex",{type:"integer"}],["aria-rowspan",{type:"integer"}],["aria-selected",{type:"boolean",allowundefined:!0}],["aria-setsize",{type:"integer"}],["aria-sort",{type:"token",values:["ascending","descending","none","other"]}],["aria-valuemax",{type:"number"}],["aria-valuemin",{type:"number"}],["aria-valuenow",{type:"number"}],["aria-valuetext",{type:"string"}]],gr={entries:function(){return hr},forEach:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=yr(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,l=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return i=e.done,e},e:function(e){l=!0,a=e},f:function(){try{i||null==r.return||r.return()}finally{if(l)throw a}}}}(hr);try{for(n.s();!(t=n.n()).done;){var o=mr(t.value,2),a=o[0],i=o[1];e.call(r,i,a,hr)}}catch(e){n.e(e)}finally{n.f()}},get:function(e){var t=hr.find((function(t){return t[0]===e}));return t&&t[1]},has:function(e){return!!gr.get(e)},keys:function(){return hr.map((function(e){return mr(e,1)[0]}))},values:function(){return hr.map((function(e){return mr(e,2)[1]}))}},Pr=(0,br.default)(gr,gr.entries());ur.default=Pr;var Cr={};Object.defineProperty(Cr,"__esModule",{value:!0}),Cr.default=void 0;var wr=function(e){return e&&e.__esModule?e:{default:e}}(sr);function qr(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,o,a=[],i=!0,l=!1;try{for(r=r.call(e);!(i=(n=r.next()).done)&&(a.push(n.value),!t||a.length!==t);i=!0);}catch(e){l=!0,o=e}finally{try{i||null==r.return||r.return()}finally{if(l)throw o}}return a}(e,t)||Er(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Er(e,t){if(e){if("string"==typeof e)return xr(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?xr(e,t):void 0}}function xr(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Or=[["a",{reserved:!1}],["abbr",{reserved:!1}],["acronym",{reserved:!1}],["address",{reserved:!1}],["applet",{reserved:!1}],["area",{reserved:!1}],["article",{reserved:!1}],["aside",{reserved:!1}],["audio",{reserved:!1}],["b",{reserved:!1}],["base",{reserved:!0}],["bdi",{reserved:!1}],["bdo",{reserved:!1}],["big",{reserved:!1}],["blink",{reserved:!1}],["blockquote",{reserved:!1}],["body",{reserved:!1}],["br",{reserved:!1}],["button",{reserved:!1}],["canvas",{reserved:!1}],["caption",{reserved:!1}],["center",{reserved:!1}],["cite",{reserved:!1}],["code",{reserved:!1}],["col",{reserved:!0}],["colgroup",{reserved:!0}],["content",{reserved:!1}],["data",{reserved:!1}],["datalist",{reserved:!1}],["dd",{reserved:!1}],["del",{reserved:!1}],["details",{reserved:!1}],["dfn",{reserved:!1}],["dialog",{reserved:!1}],["dir",{reserved:!1}],["div",{reserved:!1}],["dl",{reserved:!1}],["dt",{reserved:!1}],["em",{reserved:!1}],["embed",{reserved:!1}],["fieldset",{reserved:!1}],["figcaption",{reserved:!1}],["figure",{reserved:!1}],["font",{reserved:!1}],["footer",{reserved:!1}],["form",{reserved:!1}],["frame",{reserved:!1}],["frameset",{reserved:!1}],["h1",{reserved:!1}],["h2",{reserved:!1}],["h3",{reserved:!1}],["h4",{reserved:!1}],["h5",{reserved:!1}],["h6",{reserved:!1}],["head",{reserved:!0}],["header",{reserved:!1}],["hgroup",{reserved:!1}],["hr",{reserved:!1}],["html",{reserved:!0}],["i",{reserved:!1}],["iframe",{reserved:!1}],["img",{reserved:!1}],["input",{reserved:!1}],["ins",{reserved:!1}],["kbd",{reserved:!1}],["keygen",{reserved:!1}],["label",{reserved:!1}],["legend",{reserved:!1}],["li",{reserved:!1}],["link",{reserved:!0}],["main",{reserved:!1}],["map",{reserved:!1}],["mark",{reserved:!1}],["marquee",{reserved:!1}],["menu",{reserved:!1}],["menuitem",{reserved:!1}],["meta",{reserved:!0}],["meter",{reserved:!1}],["nav",{reserved:!1}],["noembed",{reserved:!0}],["noscript",{reserved:!0}],["object",{reserved:!1}],["ol",{reserved:!1}],["optgroup",{reserved:!1}],["option",{reserved:!1}],["output",{reserved:!1}],["p",{reserved:!1}],["param",{reserved:!0}],["picture",{reserved:!0}],["pre",{reserved:!1}],["progress",{reserved:!1}],["q",{reserved:!1}],["rp",{reserved:!1}],["rt",{reserved:!1}],["rtc",{reserved:!1}],["ruby",{reserved:!1}],["s",{reserved:!1}],["samp",{reserved:!1}],["script",{reserved:!0}],["section",{reserved:!1}],["select",{reserved:!1}],["small",{reserved:!1}],["source",{reserved:!0}],["spacer",{reserved:!1}],["span",{reserved:!1}],["strike",{reserved:!1}],["strong",{reserved:!1}],["style",{reserved:!0}],["sub",{reserved:!1}],["summary",{reserved:!1}],["sup",{reserved:!1}],["table",{reserved:!1}],["tbody",{reserved:!1}],["td",{reserved:!1}],["textarea",{reserved:!1}],["tfoot",{reserved:!1}],["th",{reserved:!1}],["thead",{reserved:!1}],["time",{reserved:!1}],["title",{reserved:!0}],["tr",{reserved:!1}],["track",{reserved:!0}],["tt",{reserved:!1}],["u",{reserved:!1}],["ul",{reserved:!1}],["var",{reserved:!1}],["video",{reserved:!1}],["wbr",{reserved:!1}],["xmp",{reserved:!1}]],Rr={entries:function(){return Or},forEach:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=Er(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,l=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return i=e.done,e},e:function(e){l=!0,a=e},f:function(){try{i||null==r.return||r.return()}finally{if(l)throw a}}}}(Or);try{for(n.s();!(t=n.n()).done;){var o=qr(t.value,2),a=o[0],i=o[1];e.call(r,i,a,Or)}}catch(e){n.e(e)}finally{n.f()}},get:function(e){var t=Or.find((function(t){return t[0]===e}));return t&&t[1]},has:function(e){return!!Rr.get(e)},keys:function(){return Or.map((function(e){return qr(e,1)[0]}))},values:function(){return Or.map((function(e){return qr(e,2)[1]}))}},jr=(0,wr.default)(Rr,Rr.entries());Cr.default=jr;var Sr={},Ar={},_r={};Object.defineProperty(_r,"__esModule",{value:!0}),_r.default=void 0;var Tr={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"menuitem"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget"]]};_r.default=Tr;var Mr={};Object.defineProperty(Mr,"__esModule",{value:!0}),Mr.default=void 0;var Ir={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-disabled":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget"]]};Mr.default=Ir;var Br={};Object.defineProperty(Br,"__esModule",{value:!0}),Br.default=void 0;var kr={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null},relatedConcepts:[{concept:{name:"input"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget"]]};Br.default=kr;var Fr={};Object.defineProperty(Fr,"__esModule",{value:!0}),Fr.default=void 0;var Nr={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Fr.default=Nr;var Lr={};Object.defineProperty(Lr,"__esModule",{value:!0}),Lr.default=void 0;var Ur={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-valuemax":null,"aria-valuemin":null,"aria-valuenow":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};Lr.default=Ur;var Dr={};Object.defineProperty(Dr,"__esModule",{value:!0}),Dr.default=void 0;var Hr={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{"aria-atomic":null,"aria-busy":null,"aria-controls":null,"aria-current":null,"aria-describedby":null,"aria-details":null,"aria-dropeffect":null,"aria-flowto":null,"aria-grabbed":null,"aria-hidden":null,"aria-keyshortcuts":null,"aria-label":null,"aria-labelledby":null,"aria-live":null,"aria-owns":null,"aria-relevant":null,"aria-roledescription":null},relatedConcepts:[{concept:{name:"rel"},module:"HTML"},{concept:{name:"role"},module:"XHTML"},{concept:{name:"type"},module:"Dublin Core"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[]};Dr.default=Hr;var $r={};Object.defineProperty($r,"__esModule",{value:!0}),$r.default=void 0;var Wr={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"frontmatter"},module:"DTB"},{concept:{name:"level"},module:"DTB"},{concept:{name:"level"},module:"SMIL"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};$r.default=Wr;var zr={};Object.defineProperty(zr,"__esModule",{value:!0}),zr.default=void 0;var Vr={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};zr.default=Vr;var Gr={};Object.defineProperty(Gr,"__esModule",{value:!0}),Gr.default=void 0;var Jr={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","composite"],["roletype","structure","section","group"]]};Gr.default=Jr;var Qr={};Object.defineProperty(Qr,"__esModule",{value:!0}),Qr.default=void 0;var Xr={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype"]]};Qr.default=Xr;var Kr={};Object.defineProperty(Kr,"__esModule",{value:!0}),Kr.default=void 0;var Yr={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype"]]};Kr.default=Yr;var Zr={};Object.defineProperty(Zr,"__esModule",{value:!0}),Zr.default=void 0;var en={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-modal":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype"]]};Zr.default=en,Object.defineProperty(Ar,"__esModule",{value:!0}),Ar.default=void 0;var tn=bn(_r),rn=bn(Mr),nn=bn(Br),on=bn(Fr),an=bn(Lr),ln=bn(Dr),un=bn($r),sn=bn(zr),cn=bn(Gr),dn=bn(Qr),pn=bn(Kr),fn=bn(Zr);function bn(e){return e&&e.__esModule?e:{default:e}}var mn=[["command",tn.default],["composite",rn.default],["input",nn.default],["landmark",on.default],["range",an.default],["roletype",ln.default],["section",un.default],["sectionhead",sn.default],["select",cn.default],["structure",dn.default],["widget",pn.default],["window",fn.default]];Ar.default=mn;var yn={},vn={};Object.defineProperty(vn,"__esModule",{value:!0}),vn.default=void 0;var hn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-atomic":"true","aria-live":"assertive"},relatedConcepts:[{concept:{name:"alert"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};vn.default=hn;var gn={};Object.defineProperty(gn,"__esModule",{value:!0}),gn.default=void 0;var Pn={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"alert"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","alert"],["roletype","window","dialog"]]};gn.default=Pn;var Cn={};Object.defineProperty(Cn,"__esModule",{value:!0}),Cn.default=void 0;var wn={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"Device Independence Delivery Unit"}}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};Cn.default=wn;var qn={};Object.defineProperty(qn,"__esModule",{value:!0}),qn.default=void 0;var En={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{name:"article"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","document"]]};qn.default=En;var xn={};Object.defineProperty(xn,"__esModule",{value:!0}),xn.default=void 0;var On={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{constraints:["direct descendant of document"],name:"header"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};xn.default=On;var Rn={};Object.defineProperty(Rn,"__esModule",{value:!0}),Rn.default=void 0;var jn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Rn.default=jn;var Sn={};Object.defineProperty(Sn,"__esModule",{value:!0}),Sn.default=void 0;var An={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null,"aria-pressed":null},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"aria-pressed"},{name:"type",value:"checkbox"}],name:"input"},module:"HTML"},{concept:{attributes:[{name:"aria-expanded",value:"false"}],name:"summary"},module:"HTML"},{concept:{attributes:[{name:"aria-expanded",value:"true"}],constraints:["direct descendant of details element with the open attribute defined"],name:"summary"},module:"HTML"},{concept:{attributes:[{name:"type",value:"button"}],name:"input"},module:"HTML"},{concept:{attributes:[{name:"type",value:"image"}],name:"input"},module:"HTML"},{concept:{attributes:[{name:"type",value:"reset"}],name:"input"},module:"HTML"},{concept:{attributes:[{name:"type",value:"submit"}],name:"input"},module:"HTML"},{concept:{name:"button"},module:"HTML"},{concept:{name:"trigger"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command"]]};Sn.default=An;var _n={};Object.defineProperty(_n,"__esModule",{value:!0}),_n.default=void 0;var Tn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:["figure","grid","table"],requiredContextRole:["figure","grid","table"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};_n.default=Tn;var Mn={};Object.defineProperty(Mn,"__esModule",{value:!0}),Mn.default=void 0;var In={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-colindex":null,"aria-colspan":null,"aria-rowindex":null,"aria-rowspan":null},relatedConcepts:[{concept:{constraints:["descendant of table"],name:"td"},module:"HTML"}],requireContextRole:["row"],requiredContextRole:["row"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Mn.default=In;var Bn={};Object.defineProperty(Bn,"__esModule",{value:!0}),Bn.default=void 0;var kn={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-checked":null,"aria-errormessage":null,"aria-expanded":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null},relatedConcepts:[{concept:{attributes:[{name:"type",value:"checkbox"}],name:"input"},module:"HTML"},{concept:{name:"option"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input"]]};Bn.default=kn;var Fn={};Object.defineProperty(Fn,"__esModule",{value:!0}),Fn.default=void 0;var Nn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Fn.default=Nn;var Ln={};Object.defineProperty(Ln,"__esModule",{value:!0}),Ln.default=void 0;var Un={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-sort":null},relatedConcepts:[{attributes:[{name:"scope",value:"col"}],concept:{name:"th"},module:"HTML"}],requireContextRole:["row"],requiredContextRole:["row"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","cell"],["roletype","structure","section","cell","gridcell"],["roletype","widget","gridcell"],["roletype","structure","sectionhead"]]};Ln.default=Un;var Dn={};Object.defineProperty(Dn,"__esModule",{value:!0}),Dn.default=void 0;var Hn={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-autocomplete":null,"aria-errormessage":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null,"aria-expanded":"false","aria-haspopup":"listbox"},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"email"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"search"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"tel"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"text"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"url"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"url"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"multiple"},{constraints:["undefined"],name:"size"}],name:"select"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"multiple"},{name:"size",value:1}],name:"select"},module:"HTML"},{concept:{name:"select"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-controls":null,"aria-expanded":"false"},superClass:[["roletype","widget","input"]]};Dn.default=Hn;var $n={};Object.defineProperty($n,"__esModule",{value:!0}),$n.default=void 0;var Wn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"aside"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};$n.default=Wn;var zn={};Object.defineProperty(zn,"__esModule",{value:!0}),zn.default=void 0;var Vn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{constraints:["direct descendant of document"],name:"footer"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};zn.default=Vn;var Gn={};Object.defineProperty(Gn,"__esModule",{value:!0}),Gn.default=void 0;var Jn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"dd"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Gn.default=Jn;var Qn={};Object.defineProperty(Qn,"__esModule",{value:!0}),Qn.default=void 0;var Xn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Qn.default=Xn;var Kn={};Object.defineProperty(Kn,"__esModule",{value:!0}),Kn.default=void 0;var Yn={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"dialog"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","window"]]};Kn.default=Yn;var Zn={};Object.defineProperty(Zn,"__esModule",{value:!0}),Zn.default=void 0;var eo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{module:"DAISY Guide"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","list"]]};Zn.default=eo;var to={};Object.defineProperty(to,"__esModule",{value:!0}),to.default=void 0;var ro={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"Device Independence Delivery Unit"}},{concept:{name:"body"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};to.default=ro;var no={};Object.defineProperty(no,"__esModule",{value:!0}),no.default=void 0;var oo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};no.default=oo;var ao={};Object.defineProperty(ao,"__esModule",{value:!0}),ao.default=void 0;var io={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["article"]],requiredProps:{},superClass:[["roletype","structure","section","list"]]};ao.default=io;var lo={};Object.defineProperty(lo,"__esModule",{value:!0}),lo.default=void 0;var uo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"figure"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};lo.default=uo;var so={};Object.defineProperty(so,"__esModule",{value:!0}),so.default=void 0;var co={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"aria-label"}],name:"form"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"aria-labelledby"}],name:"form"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"name"}],name:"form"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};so.default=co;var po={};Object.defineProperty(po,"__esModule",{value:!0}),po.default=void 0;var fo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{name:"span"},module:"HTML"},{concept:{name:"div"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};po.default=fo;var bo={};Object.defineProperty(bo,"__esModule",{value:!0}),bo.default=void 0;var mo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-multiselectable":null,"aria-readonly":null},relatedConcepts:[{concept:{attributes:[{name:"role",value:"grid"}],name:"table"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["row"],["row","rowgroup"]],requiredProps:{},superClass:[["roletype","widget","composite"],["roletype","structure","section","table"]]};bo.default=mo;var yo={};Object.defineProperty(yo,"__esModule",{value:!0}),yo.default=void 0;var vo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null,"aria-selected":null},relatedConcepts:[{concept:{attributes:[{name:"role",value:"gridcell"}],name:"td"},module:"HTML"}],requireContextRole:["row"],requiredContextRole:["row"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","cell"],["roletype","widget"]]};yo.default=vo;var ho={};Object.defineProperty(ho,"__esModule",{value:!0}),ho.default=void 0;var go={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-disabled":null},relatedConcepts:[{concept:{name:"details"},module:"HTML"},{concept:{name:"fieldset"},module:"HTML"},{concept:{name:"optgroup"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ho.default=go;var Po={};Object.defineProperty(Po,"__esModule",{value:!0}),Po.default=void 0;var Co={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-level":"2"},relatedConcepts:[{concept:{name:"h1"},module:"HTML"},{concept:{name:"h2"},module:"HTML"},{concept:{name:"h3"},module:"HTML"},{concept:{name:"h4"},module:"HTML"},{concept:{name:"h5"},module:"HTML"},{concept:{name:"h6"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-level":"2"},superClass:[["roletype","structure","sectionhead"]]};Po.default=Co;var wo={};Object.defineProperty(wo,"__esModule",{value:!0}),wo.default=void 0;var qo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"alt"}],name:"img"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"alt"}],name:"img"},module:"HTML"},{concept:{name:"imggroup"},module:"DTB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};wo.default=qo;var Eo={};Object.defineProperty(Eo,"__esModule",{value:!0}),Eo.default=void 0;var xo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Eo.default=xo;var Oo={};Object.defineProperty(Oo,"__esModule",{value:!0}),Oo.default=void 0;var Ro={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null},relatedConcepts:[{concept:{attributes:[{name:"href"}],name:"a"},module:"HTML"},{concept:{attributes:[{name:"href"}],name:"area"},module:"HTML"},{concept:{attributes:[{name:"href"}],name:"link"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command"]]};Oo.default=Ro;var jo={};Object.defineProperty(jo,"__esModule",{value:!0}),jo.default=void 0;var So={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"menu"},module:"HTML"},{concept:{name:"ol"},module:"HTML"},{concept:{name:"ul"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["listitem"]],requiredProps:{},superClass:[["roletype","structure","section"]]};jo.default=So;var Ao={};Object.defineProperty(Ao,"__esModule",{value:!0}),Ao.default=void 0;var _o={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-expanded":null,"aria-invalid":null,"aria-multiselectable":null,"aria-readonly":null,"aria-required":null,"aria-orientation":"vertical"},relatedConcepts:[{concept:{attributes:[{constraints:[">1"],name:"size"},{name:"multiple"}],name:"select"},module:"HTML"},{concept:{attributes:[{constraints:[">1"],name:"size"}],name:"select"},module:"HTML"},{concept:{attributes:[{name:"multiple"}],name:"select"},module:"HTML"},{concept:{name:"datalist"},module:"HTML"},{concept:{name:"list"},module:"ARIA"},{concept:{name:"select"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["option","group"],["option"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]};Ao.default=_o;var To={};Object.defineProperty(To,"__esModule",{value:!0}),To.default=void 0;var Mo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-level":null,"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{constraints:["direct descendant of ol, ul or menu"],name:"li"},module:"HTML"},{concept:{name:"item"},module:"XForms"}],requireContextRole:["directory","list"],requiredContextRole:["directory","list"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};To.default=Mo;var Io={};Object.defineProperty(Io,"__esModule",{value:!0}),Io.default=void 0;var Bo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-live":"polite"},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Io.default=Bo;var ko={};Object.defineProperty(ko,"__esModule",{value:!0}),ko.default=void 0;var Fo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"main"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};ko.default=Fo;var No={};Object.defineProperty(No,"__esModule",{value:!0}),No.default=void 0;var Lo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};No.default=Lo;var Uo={};Object.defineProperty(Uo,"__esModule",{value:!0}),Uo.default=void 0;var Do={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"math"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Uo.default=Do;var Ho={};Object.defineProperty(Ho,"__esModule",{value:!0}),Ho.default=void 0;var $o={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":"vertical"},relatedConcepts:[{concept:{name:"MENU"},module:"JAPI"},{concept:{name:"list"},module:"ARIA"},{concept:{name:"select"},module:"XForms"},{concept:{name:"sidebar"},module:"DTB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["menuitem","group"],["menuitemradio","group"],["menuitemcheckbox","group"],["menuitem"],["menuitemcheckbox"],["menuitemradio"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]};Ho.default=$o;var Wo={};Object.defineProperty(Wo,"__esModule",{value:!0}),Wo.default=void 0;var zo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":"horizontal"},relatedConcepts:[{concept:{name:"toolbar"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["menuitem","group"],["menuitemradio","group"],["menuitemcheckbox","group"],["menuitem"],["menuitemcheckbox"],["menuitemradio"]],requiredProps:{},superClass:[["roletype","widget","composite","select","menu"],["roletype","structure","section","group","select","menu"]]};Wo.default=zo;var Vo={};Object.defineProperty(Vo,"__esModule",{value:!0}),Vo.default=void 0;var Go={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null,"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{name:"MENU_ITEM"},module:"JAPI"},{concept:{name:"listitem"},module:"ARIA"},{concept:{name:"menuitem"},module:"HTML"},{concept:{name:"option"},module:"ARIA"}],requireContextRole:["group","menu","menubar"],requiredContextRole:["group","menu","menubar"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command"]]};Vo.default=Go;var Jo={};Object.defineProperty(Jo,"__esModule",{value:!0}),Jo.default=void 0;var Qo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"menuitem"},module:"ARIA"}],requireContextRole:["group","menu","menubar"],requiredContextRole:["group","menu","menubar"],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input","checkbox"],["roletype","widget","command","menuitem"]]};Jo.default=Qo;var Xo={};Object.defineProperty(Xo,"__esModule",{value:!0}),Xo.default=void 0;var Ko={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"menuitem"},module:"ARIA"}],requireContextRole:["group","menu","menubar"],requiredContextRole:["group","menu","menubar"],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input","checkbox","menuitemcheckbox"],["roletype","widget","command","menuitem","menuitemcheckbox"],["roletype","widget","input","radio"]]};Xo.default=Ko;var Yo={};Object.defineProperty(Yo,"__esModule",{value:!0}),Yo.default=void 0;var Zo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-valuetext":null,"aria-valuemax":"100","aria-valuemin":"0"},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-valuenow":null},superClass:[["roletype","structure","range"]]};Yo.default=Zo;var ea={};Object.defineProperty(ea,"__esModule",{value:!0}),ea.default=void 0;var ta={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"nav"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};ea.default=ta;var ra={};Object.defineProperty(ra,"__esModule",{value:!0}),ra.default=void 0;var na={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[]};ra.default=na;var oa={};Object.defineProperty(oa,"__esModule",{value:!0}),oa.default=void 0;var aa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};oa.default=aa;var ia={};Object.defineProperty(ia,"__esModule",{value:!0}),ia.default=void 0;var la={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-checked":null,"aria-posinset":null,"aria-setsize":null,"aria-selected":"false"},relatedConcepts:[{concept:{name:"item"},module:"XForms"},{concept:{name:"listitem"},module:"ARIA"},{concept:{name:"option"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-selected":"false"},superClass:[["roletype","widget","input"]]};ia.default=la;var ua={};Object.defineProperty(ua,"__esModule",{value:!0}),ua.default=void 0;var sa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ua.default=sa;var ca={};Object.defineProperty(ca,"__esModule",{value:!0}),ca.default=void 0;var da={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};ca.default=da;var pa={};Object.defineProperty(pa,"__esModule",{value:!0}),pa.default=void 0;var fa={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-valuetext":null},relatedConcepts:[{concept:{name:"progress"},module:"HTML"},{concept:{name:"status"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","range"],["roletype","widget"]]};pa.default=fa;var ba={};Object.defineProperty(ba,"__esModule",{value:!0}),ba.default=void 0;var ma={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-checked":null,"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{attributes:[{name:"type",value:"radio"}],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input"]]};ba.default=ma;var ya={};Object.defineProperty(ya,"__esModule",{value:!0}),ya.default=void 0;var va={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null},relatedConcepts:[{concept:{name:"list"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["radio"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]};ya.default=va;var ha={};Object.defineProperty(ha,"__esModule",{value:!0}),ha.default=void 0;var ga={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"aria-label"}],name:"section"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"aria-labelledby"}],name:"section"},module:"HTML"},{concept:{name:"Device Independence Glossart perceivable unit"}},{concept:{name:"frame"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};ha.default=ga;var Pa={};Object.defineProperty(Pa,"__esModule",{value:!0}),Pa.default=void 0;var Ca={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-colindex":null,"aria-expanded":null,"aria-level":null,"aria-posinset":null,"aria-rowindex":null,"aria-selected":null,"aria-setsize":null},relatedConcepts:[{concept:{name:"tr"},module:"HTML"}],requireContextRole:["grid","rowgroup","table","treegrid"],requiredContextRole:["grid","rowgroup","table","treegrid"],requiredOwnedElements:[["cell"],["columnheader"],["gridcell"],["rowheader"]],requiredProps:{},superClass:[["roletype","structure","section","group"],["roletype","widget"]]};Pa.default=Ca;var wa={};Object.defineProperty(wa,"__esModule",{value:!0}),wa.default=void 0;var qa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"tbody"},module:"HTML"},{concept:{name:"tfoot"},module:"HTML"},{concept:{name:"thead"},module:"HTML"}],requireContextRole:["grid","table","treegrid"],requiredContextRole:["grid","table","treegrid"],requiredOwnedElements:[["row"]],requiredProps:{},superClass:[["roletype","structure"]]};wa.default=qa;var Ea={};Object.defineProperty(Ea,"__esModule",{value:!0}),Ea.default=void 0;var xa={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-sort":null},relatedConcepts:[{concept:{attributes:[{name:"scope",value:"row"}],name:"th"},module:"HTML"},{concept:{attributes:[{name:"scope",value:"rowgroup"}],name:"th"},module:"HTML"}],requireContextRole:["row","rowgroup"],requiredContextRole:["row","rowgroup"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","cell"],["roletype","structure","section","cell","gridcell"],["roletype","widget","gridcell"],["roletype","structure","sectionhead"]]};Ea.default=xa;var Oa={};Object.defineProperty(Oa,"__esModule",{value:!0}),Oa.default=void 0;var Ra={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-valuetext":null,"aria-orientation":"vertical","aria-valuemax":"100","aria-valuemin":"0"},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-controls":null,"aria-valuenow":null},superClass:[["roletype","structure","range"],["roletype","widget"]]};Oa.default=Ra;var ja={};Object.defineProperty(ja,"__esModule",{value:!0}),ja.default=void 0;var Sa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};ja.default=Sa;var Aa={};Object.defineProperty(Aa,"__esModule",{value:!0}),Aa.default=void 0;var _a={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"search"}],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","input","textbox"]]};Aa.default=_a;var Ta={};Object.defineProperty(Ta,"__esModule",{value:!0}),Ta.default=void 0;var Ma={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-orientation":"horizontal","aria-valuemax":"100","aria-valuemin":"0","aria-valuenow":null,"aria-valuetext":null},relatedConcepts:[{concept:{name:"hr"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};Ta.default=Ma;var Ia={};Object.defineProperty(Ia,"__esModule",{value:!0}),Ia.default=void 0;var Ba={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-haspopup":null,"aria-invalid":null,"aria-readonly":null,"aria-valuetext":null,"aria-orientation":"horizontal","aria-valuemax":"100","aria-valuemin":"0"},relatedConcepts:[{concept:{attributes:[{name:"type",value:"range"}],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-valuenow":null},superClass:[["roletype","widget","input"],["roletype","structure","range"]]};Ia.default=Ba;var ka={};Object.defineProperty(ka,"__esModule",{value:!0}),ka.default=void 0;var Fa={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null,"aria-valuetext":null,"aria-valuenow":"0"},relatedConcepts:[{concept:{attributes:[{name:"type",value:"number"}],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","composite"],["roletype","widget","input"],["roletype","structure","range"]]};ka.default=Fa;var Na={};Object.defineProperty(Na,"__esModule",{value:!0}),Na.default=void 0;var La={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-atomic":"true","aria-live":"polite"},relatedConcepts:[{concept:{name:"output"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Na.default=La;var Ua={};Object.defineProperty(Ua,"__esModule",{value:!0}),Ua.default=void 0;var Da={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ua.default=Da;var Ha={};Object.defineProperty(Ha,"__esModule",{value:!0}),Ha.default=void 0;var $a={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ha.default=$a;var Wa={};Object.defineProperty(Wa,"__esModule",{value:!0}),Wa.default=void 0;var za={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Wa.default=za;var Va={};Object.defineProperty(Va,"__esModule",{value:!0}),Va.default=void 0;var Ga={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"button"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input","checkbox"]]};Va.default=Ga;var Ja={};Object.defineProperty(Ja,"__esModule",{value:!0}),Ja.default=void 0;var Qa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null,"aria-posinset":null,"aria-setsize":null,"aria-selected":"false"},relatedConcepts:[],requireContextRole:["tablist"],requiredContextRole:["tablist"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","sectionhead"],["roletype","widget"]]};Ja.default=Qa;var Xa={};Object.defineProperty(Xa,"__esModule",{value:!0}),Xa.default=void 0;var Ka={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-colcount":null,"aria-rowcount":null},relatedConcepts:[{concept:{name:"table"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["row"],["row","rowgroup"]],requiredProps:{},superClass:[["roletype","structure","section"]]};Xa.default=Ka;var Ya={};Object.defineProperty(Ya,"__esModule",{value:!0}),Ya.default=void 0;var Za={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-level":null,"aria-multiselectable":null,"aria-orientation":"horizontal"},relatedConcepts:[{module:"DAISY",concept:{name:"guide"}}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["tab"]],requiredProps:{},superClass:[["roletype","widget","composite"]]};Ya.default=Za;var ei={};Object.defineProperty(ei,"__esModule",{value:!0}),ei.default=void 0;var ti={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ei.default=ti;var ri={};Object.defineProperty(ri,"__esModule",{value:!0}),ri.default=void 0;var ni={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"dfn"},module:"HTML"},{concept:{name:"dt"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ri.default=ni;var oi={};Object.defineProperty(oi,"__esModule",{value:!0}),oi.default=void 0;var ai={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-autocomplete":null,"aria-errormessage":null,"aria-haspopup":null,"aria-invalid":null,"aria-multiline":null,"aria-placeholder":null,"aria-readonly":null,"aria-required":null},relatedConcepts:[{concept:{attributes:[{constraints:["undefined"],name:"type"},{constraints:["undefined"],name:"list"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"email"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"tel"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"text"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"url"}],name:"input"},module:"HTML"},{concept:{name:"input"},module:"XForms"},{concept:{name:"textarea"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","input"]]};oi.default=ai;var ii={};Object.defineProperty(ii,"__esModule",{value:!0}),ii.default=void 0;var li={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ii.default=li;var ui={};Object.defineProperty(ui,"__esModule",{value:!0}),ui.default=void 0;var si={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","status"]]};ui.default=si;var ci={};Object.defineProperty(ci,"__esModule",{value:!0}),ci.default=void 0;var di={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":"horizontal"},relatedConcepts:[{concept:{name:"menubar"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","group"]]};ci.default=di;var pi={};Object.defineProperty(pi,"__esModule",{value:!0}),pi.default=void 0;var fi={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};pi.default=fi;var bi={};Object.defineProperty(bi,"__esModule",{value:!0}),bi.default=void 0;var mi={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null,"aria-multiselectable":null,"aria-required":null,"aria-orientation":"vertical"},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["treeitem","group"],["treeitem"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]};bi.default=mi;var yi={};Object.defineProperty(yi,"__esModule",{value:!0}),yi.default=void 0;var vi={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["row"],["row","rowgroup"]],requiredProps:{},superClass:[["roletype","widget","composite","grid"],["roletype","structure","section","table","grid"],["roletype","widget","composite","select","tree"],["roletype","structure","section","group","select","tree"]]};yi.default=vi;var hi={};Object.defineProperty(hi,"__esModule",{value:!0}),hi.default=void 0;var gi={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-expanded":null,"aria-haspopup":null},relatedConcepts:[],requireContextRole:["group","tree"],requiredContextRole:["group","tree"],requiredOwnedElements:[],requiredProps:{"aria-selected":null},superClass:[["roletype","structure","section","listitem"],["roletype","widget","input","option"]]};hi.default=gi,Object.defineProperty(yn,"__esModule",{value:!0}),yn.default=void 0;var Pi=Jl(vn),Ci=Jl(gn),wi=Jl(Cn),qi=Jl(qn),Ei=Jl(xn),xi=Jl(Rn),Oi=Jl(Sn),Ri=Jl(_n),ji=Jl(Mn),Si=Jl(Bn),Ai=Jl(Fn),_i=Jl(Ln),Ti=Jl(Dn),Mi=Jl($n),Ii=Jl(zn),Bi=Jl(Gn),ki=Jl(Qn),Fi=Jl(Kn),Ni=Jl(Zn),Li=Jl(to),Ui=Jl(no),Di=Jl(ao),Hi=Jl(lo),$i=Jl(so),Wi=Jl(po),zi=Jl(bo),Vi=Jl(yo),Gi=Jl(ho),Ji=Jl(Po),Qi=Jl(wo),Xi=Jl(Eo),Ki=Jl(Oo),Yi=Jl(jo),Zi=Jl(Ao),el=Jl(To),tl=Jl(Io),rl=Jl(ko),nl=Jl(No),ol=Jl(Uo),al=Jl(Ho),il=Jl(Wo),ll=Jl(Vo),ul=Jl(Jo),sl=Jl(Xo),cl=Jl(Yo),dl=Jl(ea),pl=Jl(ra),fl=Jl(oa),bl=Jl(ia),ml=Jl(ua),yl=Jl(ca),vl=Jl(pa),hl=Jl(ba),gl=Jl(ya),Pl=Jl(ha),Cl=Jl(Pa),wl=Jl(wa),ql=Jl(Ea),El=Jl(Oa),xl=Jl(ja),Ol=Jl(Aa),Rl=Jl(Ta),jl=Jl(Ia),Sl=Jl(ka),Al=Jl(Na),_l=Jl(Ua),Tl=Jl(Ha),Ml=Jl(Wa),Il=Jl(Va),Bl=Jl(Ja),kl=Jl(Xa),Fl=Jl(Ya),Nl=Jl(ei),Ll=Jl(ri),Ul=Jl(oi),Dl=Jl(ii),Hl=Jl(ui),$l=Jl(ci),Wl=Jl(pi),zl=Jl(bi),Vl=Jl(yi),Gl=Jl(hi);function Jl(e){return e&&e.__esModule?e:{default:e}}var Ql=[["alert",Pi.default],["alertdialog",Ci.default],["application",wi.default],["article",qi.default],["banner",Ei.default],["blockquote",xi.default],["button",Oi.default],["caption",Ri.default],["cell",ji.default],["checkbox",Si.default],["code",Ai.default],["columnheader",_i.default],["combobox",Ti.default],["complementary",Mi.default],["contentinfo",Ii.default],["definition",Bi.default],["deletion",ki.default],["dialog",Fi.default],["directory",Ni.default],["document",Li.default],["emphasis",Ui.default],["feed",Di.default],["figure",Hi.default],["form",$i.default],["generic",Wi.default],["grid",zi.default],["gridcell",Vi.default],["group",Gi.default],["heading",Ji.default],["img",Qi.default],["insertion",Xi.default],["link",Ki.default],["list",Yi.default],["listbox",Zi.default],["listitem",el.default],["log",tl.default],["main",rl.default],["marquee",nl.default],["math",ol.default],["menu",al.default],["menubar",il.default],["menuitem",ll.default],["menuitemcheckbox",ul.default],["menuitemradio",sl.default],["meter",cl.default],["navigation",dl.default],["none",pl.default],["note",fl.default],["option",bl.default],["paragraph",ml.default],["presentation",yl.default],["progressbar",vl.default],["radio",hl.default],["radiogroup",gl.default],["region",Pl.default],["row",Cl.default],["rowgroup",wl.default],["rowheader",ql.default],["scrollbar",El.default],["search",xl.default],["searchbox",Ol.default],["separator",Rl.default],["slider",jl.default],["spinbutton",Sl.default],["status",Al.default],["strong",_l.default],["subscript",Tl.default],["superscript",Ml.default],["switch",Il.default],["tab",Bl.default],["table",kl.default],["tablist",Fl.default],["tabpanel",Nl.default],["term",Ll.default],["textbox",Ul.default],["time",Dl.default],["timer",Hl.default],["toolbar",$l.default],["tooltip",Wl.default],["tree",zl.default],["treegrid",Vl.default],["treeitem",Gl.default]];yn.default=Ql;var Xl={},Kl={};Object.defineProperty(Kl,"__esModule",{value:!0}),Kl.default=void 0;var Yl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"abstract [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Kl.default=Yl;var Zl={};Object.defineProperty(Zl,"__esModule",{value:!0}),Zl.default=void 0;var eu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"acknowledgments [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Zl.default=eu;var tu={};Object.defineProperty(tu,"__esModule",{value:!0}),tu.default=void 0;var ru={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"afterword [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};tu.default=ru;var nu={};Object.defineProperty(nu,"__esModule",{value:!0}),nu.default=void 0;var ou={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"appendix [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};nu.default=ou;var au={};Object.defineProperty(au,"__esModule",{value:!0}),au.default=void 0;var iu={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","content"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"referrer [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]};au.default=iu;var lu={};Object.defineProperty(lu,"__esModule",{value:!0}),lu.default=void 0;var uu={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"EPUB biblioentry [EPUB-SSV]"},module:"EPUB"}],requireContextRole:["doc-bibliography"],requiredContextRole:["doc-bibliography"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","listitem"]]};lu.default=uu;var su={};Object.defineProperty(su,"__esModule",{value:!0}),su.default=void 0;var cu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"bibliography [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["doc-biblioentry"]],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};su.default=cu;var du={};Object.defineProperty(du,"__esModule",{value:!0}),du.default=void 0;var pu={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"biblioref [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]};du.default=pu;var fu={};Object.defineProperty(fu,"__esModule",{value:!0}),fu.default=void 0;var bu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"chapter [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};fu.default=bu;var mu={};Object.defineProperty(mu,"__esModule",{value:!0}),mu.default=void 0;var yu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"colophon [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};mu.default=yu;var vu={};Object.defineProperty(vu,"__esModule",{value:!0}),vu.default=void 0;var hu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"conclusion [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};vu.default=hu;var gu={};Object.defineProperty(gu,"__esModule",{value:!0}),gu.default=void 0;var Pu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"cover [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","img"]]};gu.default=Pu;var Cu={};Object.defineProperty(Cu,"__esModule",{value:!0}),Cu.default=void 0;var wu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"credit [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Cu.default=wu;var qu={};Object.defineProperty(qu,"__esModule",{value:!0}),qu.default=void 0;var Eu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"credits [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};qu.default=Eu;var xu={};Object.defineProperty(xu,"__esModule",{value:!0}),xu.default=void 0;var Ou={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"dedication [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};xu.default=Ou;var Ru={};Object.defineProperty(Ru,"__esModule",{value:!0}),Ru.default=void 0;var ju={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"rearnote [EPUB-SSV]"},module:"EPUB"}],requireContextRole:["doc-endnotes"],requiredContextRole:["doc-endnotes"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","listitem"]]};Ru.default=ju;var Su={};Object.defineProperty(Su,"__esModule",{value:!0}),Su.default=void 0;var Au={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"rearnotes [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["doc-endnote"]],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Su.default=Au;var _u={};Object.defineProperty(_u,"__esModule",{value:!0}),_u.default=void 0;var Tu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"epigraph [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};_u.default=Tu;var Mu={};Object.defineProperty(Mu,"__esModule",{value:!0}),Mu.default=void 0;var Iu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"epilogue [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Mu.default=Iu;var Bu={};Object.defineProperty(Bu,"__esModule",{value:!0}),Bu.default=void 0;var ku={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"errata [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Bu.default=ku;var Fu={};Object.defineProperty(Fu,"__esModule",{value:!0}),Fu.default=void 0;var Nu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Fu.default=Nu;var Lu={};Object.defineProperty(Lu,"__esModule",{value:!0}),Lu.default=void 0;var Uu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"footnote [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Lu.default=Uu;var Du={};Object.defineProperty(Du,"__esModule",{value:!0}),Du.default=void 0;var Hu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"foreword [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Du.default=Hu;var $u={};Object.defineProperty($u,"__esModule",{value:!0}),$u.default=void 0;var Wu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"glossary [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["definition"],["term"]],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};$u.default=Wu;var zu={};Object.defineProperty(zu,"__esModule",{value:!0}),zu.default=void 0;var Vu={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"glossref [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]};zu.default=Vu;var Gu={};Object.defineProperty(Gu,"__esModule",{value:!0}),Gu.default=void 0;var Ju={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"index [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark","navigation"]]};Gu.default=Ju;var Qu={};Object.defineProperty(Qu,"__esModule",{value:!0}),Qu.default=void 0;var Xu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"introduction [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Qu.default=Xu;var Ku={};Object.defineProperty(Ku,"__esModule",{value:!0}),Ku.default=void 0;var Yu={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"noteref [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]};Ku.default=Yu;var Zu={};Object.defineProperty(Zu,"__esModule",{value:!0}),Zu.default=void 0;var es={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"notice [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","note"]]};Zu.default=es;var ts={};Object.defineProperty(ts,"__esModule",{value:!0}),ts.default=void 0;var rs={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"pagebreak [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","separator"]]};ts.default=rs;var ns={};Object.defineProperty(ns,"__esModule",{value:!0}),ns.default=void 0;var os={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"page-list [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark","navigation"]]};ns.default=os;var as={};Object.defineProperty(as,"__esModule",{value:!0}),as.default=void 0;var is={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"part [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};as.default=is;var ls={};Object.defineProperty(ls,"__esModule",{value:!0}),ls.default=void 0;var us={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"preface [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};ls.default=us;var ss={};Object.defineProperty(ss,"__esModule",{value:!0}),ss.default=void 0;var cs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"prologue [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};ss.default=cs;var ds={};Object.defineProperty(ds,"__esModule",{value:!0}),ds.default=void 0;var ps={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"pullquote [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["none"]]};ds.default=ps;var fs={};Object.defineProperty(fs,"__esModule",{value:!0}),fs.default=void 0;var bs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"qna [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};fs.default=bs;var ms={};Object.defineProperty(ms,"__esModule",{value:!0}),ms.default=void 0;var ys={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"subtitle [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","sectionhead"]]};ms.default=ys;var vs={};Object.defineProperty(vs,"__esModule",{value:!0}),vs.default=void 0;var hs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"help [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","note"]]};vs.default=hs;var gs={};Object.defineProperty(gs,"__esModule",{value:!0}),gs.default=void 0;var Ps={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"toc [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark","navigation"]]};gs.default=Ps,Object.defineProperty(Xl,"__esModule",{value:!0}),Xl.default=void 0;var Cs=ic(Kl),ws=ic(Zl),qs=ic(tu),Es=ic(nu),xs=ic(au),Os=ic(lu),Rs=ic(su),js=ic(du),Ss=ic(fu),As=ic(mu),_s=ic(vu),Ts=ic(gu),Ms=ic(Cu),Is=ic(qu),Bs=ic(xu),ks=ic(Ru),Fs=ic(Su),Ns=ic(_u),Ls=ic(Mu),Us=ic(Bu),Ds=ic(Fu),Hs=ic(Lu),$s=ic(Du),Ws=ic($u),zs=ic(zu),Vs=ic(Gu),Gs=ic(Qu),Js=ic(Ku),Qs=ic(Zu),Xs=ic(ts),Ks=ic(ns),Ys=ic(as),Zs=ic(ls),ec=ic(ss),tc=ic(ds),rc=ic(fs),nc=ic(ms),oc=ic(vs),ac=ic(gs);function ic(e){return e&&e.__esModule?e:{default:e}}var lc=[["doc-abstract",Cs.default],["doc-acknowledgments",ws.default],["doc-afterword",qs.default],["doc-appendix",Es.default],["doc-backlink",xs.default],["doc-biblioentry",Os.default],["doc-bibliography",Rs.default],["doc-biblioref",js.default],["doc-chapter",Ss.default],["doc-colophon",As.default],["doc-conclusion",_s.default],["doc-cover",Ts.default],["doc-credit",Ms.default],["doc-credits",Is.default],["doc-dedication",Bs.default],["doc-endnote",ks.default],["doc-endnotes",Fs.default],["doc-epigraph",Ns.default],["doc-epilogue",Ls.default],["doc-errata",Us.default],["doc-example",Ds.default],["doc-footnote",Hs.default],["doc-foreword",$s.default],["doc-glossary",Ws.default],["doc-glossref",zs.default],["doc-index",Vs.default],["doc-introduction",Gs.default],["doc-noteref",Js.default],["doc-notice",Qs.default],["doc-pagebreak",Xs.default],["doc-pagelist",Ks.default],["doc-part",Ys.default],["doc-preface",Zs.default],["doc-prologue",ec.default],["doc-pullquote",tc.default],["doc-qna",rc.default],["doc-subtitle",nc.default],["doc-tip",oc.default],["doc-toc",ac.default]];Xl.default=lc;var uc={},sc={};Object.defineProperty(sc,"__esModule",{value:!0}),sc.default=void 0;var cc={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{module:"GRAPHICS",concept:{name:"graphics-object"}},{module:"ARIA",concept:{name:"img"}},{module:"ARIA",concept:{name:"article"}}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","document"]]};sc.default=cc;var dc={};Object.defineProperty(dc,"__esModule",{value:!0}),dc.default=void 0;var pc={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{module:"GRAPHICS",concept:{name:"graphics-document"}},{module:"ARIA",concept:{name:"group"}},{module:"ARIA",concept:{name:"img"}},{module:"GRAPHICS",concept:{name:"graphics-symbol"}}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","group"]]};dc.default=pc;var fc={};Object.defineProperty(fc,"__esModule",{value:!0}),fc.default=void 0;var bc={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","img"]]};fc.default=bc,Object.defineProperty(uc,"__esModule",{value:!0}),uc.default=void 0;var mc=hc(sc),yc=hc(dc),vc=hc(fc);function hc(e){return e&&e.__esModule?e:{default:e}}var gc=[["graphics-document",mc.default],["graphics-object",yc.default],["graphics-symbol",vc.default]];uc.default=gc,Object.defineProperty(Sr,"__esModule",{value:!0}),Sr.default=void 0;var Pc=xc(Ar),Cc=xc(yn),wc=xc(Xl),qc=xc(uc),Ec=xc(sr);function xc(e){return e&&e.__esModule?e:{default:e}}function Oc(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Rc(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=Sc(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,l=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return i=e.done,e},e:function(e){l=!0,a=e},f:function(){try{i||null==r.return||r.return()}finally{if(l)throw a}}}}function jc(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,o,a=[],i=!0,l=!1;try{for(r=r.call(e);!(i=(n=r.next()).done)&&(a.push(n.value),!t||a.length!==t);i=!0);}catch(e){l=!0,o=e}finally{try{i||null==r.return||r.return()}finally{if(l)throw o}}return a}(e,t)||Sc(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Sc(e,t){if(e){if("string"==typeof e)return Ac(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Ac(e,t):void 0}}function Ac(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var _c=[].concat(Pc.default,Cc.default,wc.default,qc.default);_c.forEach((function(e){var t,r=jc(e,2)[1],n=Rc(r.superClass);try{for(n.s();!(t=n.n()).done;){var o,a=Rc(t.value);try{var i=function(){var e=o.value,t=_c.find((function(t){return jc(t,1)[0]===e}));if(t)for(var n=t[1],a=0,i=Object.keys(n.props);a<i.length;a++){var l=i[a];Object.prototype.hasOwnProperty.call(r.props,l)||Object.assign(r.props,Oc({},l,n.props[l]))}};for(a.s();!(o=a.n()).done;)i()}catch(e){a.e(e)}finally{a.f()}}}catch(e){n.e(e)}finally{n.f()}}));var Tc={entries:function(){return _c},forEach:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=Rc(_c);try{for(n.s();!(t=n.n()).done;){var o=jc(t.value,2),a=o[0],i=o[1];e.call(r,i,a,_c)}}catch(e){n.e(e)}finally{n.f()}},get:function(e){var t=_c.find((function(t){return t[0]===e}));return t&&t[1]},has:function(e){return!!Tc.get(e)},keys:function(){return _c.map((function(e){return jc(e,1)[0]}))},values:function(){return _c.map((function(e){return jc(e,2)[1]}))}},Mc=(0,Ec.default)(Tc,Tc.entries());Sr.default=Mc;var Ic,Bc,kc={},Fc=Object.prototype.toString,Nc=function(e){var t=Fc.call(e),r="[object Arguments]"===t;return r||(r="[object Array]"!==t&&null!==e&&"object"==typeof e&&"number"==typeof e.length&&e.length>=0&&"[object Function]"===Fc.call(e.callee)),r};var Lc=Array.prototype.slice,Uc=Nc,Dc=Object.keys,Hc=Dc?function(e){return Dc(e)}:function(){if(Bc)return Ic;var e;if(Bc=1,!Object.keys){var t=Object.prototype.hasOwnProperty,r=Object.prototype.toString,n=Object.prototype.propertyIsEnumerable,o=!n.call({toString:null},"toString"),a=n.call((function(){}),"prototype"),i=["toString","toLocaleString","valueOf","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","constructor"],l=function(e){var t=e.constructor;return t&&t.prototype===e},u={$applicationCache:!0,$console:!0,$external:!0,$frame:!0,$frameElement:!0,$frames:!0,$innerHeight:!0,$innerWidth:!0,$onmozfullscreenchange:!0,$onmozfullscreenerror:!0,$outerHeight:!0,$outerWidth:!0,$pageXOffset:!0,$pageYOffset:!0,$parent:!0,$scrollLeft:!0,$scrollTop:!0,$scrollX:!0,$scrollY:!0,$self:!0,$webkitIndexedDB:!0,$webkitStorageInfo:!0,$window:!0},s=function(){if("undefined"==typeof window)return!1;for(var e in window)try{if(!u["$"+e]&&t.call(window,e)&&null!==window[e]&&"object"==typeof window[e])try{l(window[e])}catch(e){return!0}}catch(e){return!0}return!1}();e=function(e){var n=null!==e&&"object"==typeof e,u="[object Function]"===r.call(e),c=Nc(e),d=n&&"[object String]"===r.call(e),p=[];if(!n&&!u&&!c)throw new TypeError("Object.keys called on a non-object");if(d&&e.length>0&&!t.call(e,0))for(var f=0;f<e.length;++f)p.push(String(f));if(c&&e.length>0)for(var b=0;b<e.length;++b)p.push(String(b));else for(var m in e)a&&u&&"prototype"===m||!t.call(e,m)||p.push(String(m));if(o)for(var y=function(e){if("undefined"==typeof window||!s)return l(e);try{return l(e)}catch(e){return!1}}(e),v=0;v<i.length;++v)y&&"constructor"===i[v]||!t.call(e,i[v])||p.push(i[v]);return p}}return Ic=e}(),$c=Object.keys;Hc.shim=function(){if(Object.keys){var e=function(){var e=Object.keys(arguments);return e&&e.length===arguments.length}(1,2);e||(Object.keys=function(e){return Uc(e)?$c(Lc.call(e)):$c(e)})}else Object.keys=Hc;return Object.keys||Hc};var Wc,zc,Vc=Hc,Gc=Error,Jc=EvalError,Qc=RangeError,Xc=ReferenceError,Kc=SyntaxError,Yc=TypeError,Zc=URIError;function ed(){return zc||(zc=1,Wc=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var e={},t=Symbol("test"),r=Object(t);if("string"==typeof t)return!1;if("[object Symbol]"!==Object.prototype.toString.call(t))return!1;if("[object Symbol]"!==Object.prototype.toString.call(r))return!1;for(t in e[t]=42,e)return!1;if("function"==typeof Object.keys&&0!==Object.keys(e).length)return!1;if("function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(e).length)return!1;var n=Object.getOwnPropertySymbols(e);if(1!==n.length||n[0]!==t)return!1;if(!Object.prototype.propertyIsEnumerable.call(e,t))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var o=Object.getOwnPropertyDescriptor(e,t);if(42!==o.value||!0!==o.enumerable)return!1}return!0}),Wc}var td,rd="undefined"!=typeof Symbol&&Symbol,nd=ed(),od=function(){return"function"==typeof rd&&("function"==typeof Symbol&&("symbol"==typeof rd("foo")&&("symbol"==typeof Symbol("bar")&&nd())))},ad={__proto__:null,foo:{}},id=Object,ld=Object.prototype.toString,ud=Math.max,sd=function(e,t){for(var r=[],n=0;n<e.length;n+=1)r[n]=e[n];for(var o=0;o<t.length;o+=1)r[o+e.length]=t[o];return r},cd=function(e){var t=this;if("function"!=typeof t||"[object Function]"!==ld.apply(t))throw new TypeError("Function.prototype.bind called on incompatible "+t);for(var r,n=function(e,t){for(var r=[],n=t||0,o=0;n<e.length;n+=1,o+=1)r[o]=e[n];return r}(arguments,1),o=ud(0,t.length-n.length),a=[],i=0;i<o;i++)a[i]="$"+i;if(r=Function("binder","return function ("+function(e,t){for(var r="",n=0;n<e.length;n+=1)r+=e[n],n+1<e.length&&(r+=t);return r}(a,",")+"){ return binder.apply(this,arguments); }")((function(){if(this instanceof r){var o=t.apply(this,sd(n,arguments));return Object(o)===o?o:this}return t.apply(e,sd(n,arguments))})),t.prototype){var l=function(){};l.prototype=t.prototype,r.prototype=new l,l.prototype=null}return r},dd=Function.prototype.bind||cd,pd=Function.prototype.call,fd=Object.prototype.hasOwnProperty,bd=dd.call(pd,fd),md=Gc,yd=Jc,vd=Qc,hd=Xc,gd=Kc,Pd=Yc,Cd=Zc,wd=Function,qd=function(e){try{return wd('"use strict"; return ('+e+").constructor;")()}catch(e){}},Ed=Object.getOwnPropertyDescriptor;if(Ed)try{Ed({},"")}catch(e){Ed=null}var xd=function(){throw new Pd},Od=Ed?function(){try{return xd}catch(e){try{return Ed(arguments,"callee").get}catch(e){return xd}}}():xd,Rd=od(),jd={__proto__:ad}.foo===ad.foo&&!(ad instanceof id),Sd=Object.getPrototypeOf||(jd?function(e){return e.__proto__}:null),Ad={},_d="undefined"!=typeof Uint8Array&&Sd?Sd(Uint8Array):td,Td={__proto__:null,"%AggregateError%":"undefined"==typeof AggregateError?td:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?td:ArrayBuffer,"%ArrayIteratorPrototype%":Rd&&Sd?Sd([][Symbol.iterator]()):td,"%AsyncFromSyncIteratorPrototype%":td,"%AsyncFunction%":Ad,"%AsyncGenerator%":Ad,"%AsyncGeneratorFunction%":Ad,"%AsyncIteratorPrototype%":Ad,"%Atomics%":"undefined"==typeof Atomics?td:Atomics,"%BigInt%":"undefined"==typeof BigInt?td:BigInt,"%BigInt64Array%":"undefined"==typeof BigInt64Array?td:BigInt64Array,"%BigUint64Array%":"undefined"==typeof BigUint64Array?td:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?td:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":md,"%eval%":eval,"%EvalError%":yd,"%Float32Array%":"undefined"==typeof Float32Array?td:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?td:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?td:FinalizationRegistry,"%Function%":wd,"%GeneratorFunction%":Ad,"%Int8Array%":"undefined"==typeof Int8Array?td:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?td:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?td:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":Rd&&Sd?Sd(Sd([][Symbol.iterator]())):td,"%JSON%":"object"==typeof JSON?JSON:td,"%Map%":"undefined"==typeof Map?td:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&Rd&&Sd?Sd((new Map)[Symbol.iterator]()):td,"%Math%":Math,"%Number%":Number,"%Object%":Object,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?td:Promise,"%Proxy%":"undefined"==typeof Proxy?td:Proxy,"%RangeError%":vd,"%ReferenceError%":hd,"%Reflect%":"undefined"==typeof Reflect?td:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?td:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&Rd&&Sd?Sd((new Set)[Symbol.iterator]()):td,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?td:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":Rd&&Sd?Sd(""[Symbol.iterator]()):td,"%Symbol%":Rd?Symbol:td,"%SyntaxError%":gd,"%ThrowTypeError%":Od,"%TypedArray%":_d,"%TypeError%":Pd,"%Uint8Array%":"undefined"==typeof Uint8Array?td:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?td:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?td:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?td:Uint32Array,"%URIError%":Cd,"%WeakMap%":"undefined"==typeof WeakMap?td:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?td:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?td:WeakSet};if(Sd)try{null.error}catch(e){var Md=Sd(Sd(e));Td["%Error.prototype%"]=Md}var Id,Bd,kd=function e(t){var r;if("%AsyncFunction%"===t)r=qd("async function () {}");else if("%GeneratorFunction%"===t)r=qd("function* () {}");else if("%AsyncGeneratorFunction%"===t)r=qd("async function* () {}");else if("%AsyncGenerator%"===t){var n=e("%AsyncGeneratorFunction%");n&&(r=n.prototype)}else if("%AsyncIteratorPrototype%"===t){var o=e("%AsyncGenerator%");o&&Sd&&(r=Sd(o.prototype))}return Td[t]=r,r},Fd={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},Nd=dd,Ld=bd,Ud=Nd.call(Function.call,Array.prototype.concat),Dd=Nd.call(Function.apply,Array.prototype.splice),Hd=Nd.call(Function.call,String.prototype.replace),$d=Nd.call(Function.call,String.prototype.slice),Wd=Nd.call(Function.call,RegExp.prototype.exec),zd=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,Vd=/\\(\\)?/g,Gd=function(e,t){var r,n=e;if(Ld(Fd,n)&&(n="%"+(r=Fd[n])[0]+"%"),Ld(Td,n)){var o=Td[n];if(o===Ad&&(o=kd(n)),void 0===o&&!t)throw new Pd("intrinsic "+e+" exists, but is not available. Please file an issue!");return{alias:r,name:n,value:o}}throw new gd("intrinsic "+e+" does not exist!")},Jd=function(e,t){if("string"!=typeof e||0===e.length)throw new Pd("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof t)throw new Pd('"allowMissing" argument must be a boolean');if(null===Wd(/^%?[^%]*%?$/,e))throw new gd("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var r=function(e){var t=$d(e,0,1),r=$d(e,-1);if("%"===t&&"%"!==r)throw new gd("invalid intrinsic syntax, expected closing `%`");if("%"===r&&"%"!==t)throw new gd("invalid intrinsic syntax, expected opening `%`");var n=[];return Hd(e,zd,(function(e,t,r,o){n[n.length]=r?Hd(o,Vd,"$1"):t||e})),n}(e),n=r.length>0?r[0]:"",o=Gd("%"+n+"%",t),a=o.name,i=o.value,l=!1,u=o.alias;u&&(n=u[0],Dd(r,Ud([0,1],u)));for(var s=1,c=!0;s<r.length;s+=1){var d=r[s],p=$d(d,0,1),f=$d(d,-1);if(('"'===p||"'"===p||"`"===p||'"'===f||"'"===f||"`"===f)&&p!==f)throw new gd("property names with quotes must have matching quotes");if("constructor"!==d&&c||(l=!0),Ld(Td,a="%"+(n+="."+d)+"%"))i=Td[a];else if(null!=i){if(!(d in i)){if(!t)throw new Pd("base intrinsic for "+e+" exists, but the property is not available.");return}if(Ed&&s+1>=r.length){var b=Ed(i,d);i=(c=!!b)&&"get"in b&&!("originalValue"in b.get)?b.get:i[d]}else c=Ld(i,d),i=i[d];c&&!l&&(Td[a]=i)}}return i};function Qd(){if(Bd)return Id;Bd=1;var e=Jd("%Object.defineProperty%",!0)||!1;if(e)try{e({},"a",{value:1})}catch(t){e=!1}return Id=e}var Xd=Jd("%Object.getOwnPropertyDescriptor%",!0);if(Xd)try{Xd([],"length")}catch(e){Xd=null}var Kd=Xd,Yd=Qd(),Zd=Kc,ep=Yc,tp=Kd,rp=function(e,t,r){if(!e||"object"!=typeof e&&"function"!=typeof e)throw new ep("`obj` must be an object or a function`");if("string"!=typeof t&&"symbol"!=typeof t)throw new ep("`property` must be a string or a symbol`");if(arguments.length>3&&"boolean"!=typeof arguments[3]&&null!==arguments[3])throw new ep("`nonEnumerable`, if provided, must be a boolean or null");if(arguments.length>4&&"boolean"!=typeof arguments[4]&&null!==arguments[4])throw new ep("`nonWritable`, if provided, must be a boolean or null");if(arguments.length>5&&"boolean"!=typeof arguments[5]&&null!==arguments[5])throw new ep("`nonConfigurable`, if provided, must be a boolean or null");if(arguments.length>6&&"boolean"!=typeof arguments[6])throw new ep("`loose`, if provided, must be a boolean");var n=arguments.length>3?arguments[3]:null,o=arguments.length>4?arguments[4]:null,a=arguments.length>5?arguments[5]:null,i=arguments.length>6&&arguments[6],l=!!tp&&tp(e,t);if(Yd)Yd(e,t,{configurable:null===a&&l?l.configurable:!a,enumerable:null===n&&l?l.enumerable:!n,value:r,writable:null===o&&l?l.writable:!o});else{if(!i&&(n||o||a))throw new Zd("This environment does not support defining a property as non-configurable, non-writable, or non-enumerable.");e[t]=r}},np=Qd(),op=function(){return!!np};op.hasArrayLengthDefineBug=function(){if(!np)return null;try{return 1!==np([],"length",{value:1}).length}catch(e){return!0}};var ap=op,ip=Vc,lp="function"==typeof Symbol&&"symbol"==typeof Symbol("foo"),up=Object.prototype.toString,sp=Array.prototype.concat,cp=rp,dp=ap(),pp=function(e,t,r,n){if(t in e)if(!0===n){if(e[t]===r)return}else if("function"!=typeof(o=n)||"[object Function]"!==up.call(o)||!n())return;var o;dp?cp(e,t,r,!0):cp(e,t,r)},fp=function(e,t){var r=arguments.length>2?arguments[2]:{},n=ip(t);lp&&(n=sp.call(n,Object.getOwnPropertySymbols(t)));for(var o=0;o<n.length;o+=1)pp(e,n[o],t[n[o]],r[n[o]])};fp.supportsDescriptors=!!dp;var bp=fp,mp={exports:{}},yp=Jd,vp=rp,hp=ap(),gp=Kd,Pp=Yc,Cp=yp("%Math.floor%");!function(e){var t=dd,r=Jd,n=r("%Function.prototype.apply%"),o=r("%Function.prototype.call%"),a=r("%Reflect.apply%",!0)||t.call(o,n),i=Qd(),l=r("%Math.max%");e.exports=function(e){if("function"!=typeof e)throw new Yc("a function is required");return function(e,t){if("function"!=typeof e)throw new Pp("`fn` is not a function");if("number"!=typeof t||t<0||t>4294967295||Cp(t)!==t)throw new Pp("`length` must be a positive 32-bit integer");var r=arguments.length>2&&!!arguments[2],n=!0,o=!0;if("length"in e&&gp){var a=gp(e,"length");a&&!a.configurable&&(n=!1),a&&!a.writable&&(o=!1)}return(n||o||!r)&&(hp?vp(e,"length",t,!0,!0):vp(e,"length",t)),e}(a(t,o,arguments),1+l(0,e.length-(arguments.length-1)),!0)};var u=function(){return a(t,n,arguments)};i?i(e.exports,"apply",{value:u}):e.exports.apply=u}(mp);var wp=Jd,qp=mp.exports,Ep=qp(wp("String.prototype.indexOf")),xp=function(e,t){var r=wp(e,!!t);return"function"==typeof r&&Ep(e,".prototype.")>-1?qp(r):r},Op=Vc,Rp=ed()(),jp=xp,Sp=Object,Ap=jp("Array.prototype.push"),_p=jp("Object.prototype.propertyIsEnumerable"),Tp=Rp?Object.getOwnPropertySymbols:null,Mp=function(e){if(null==e)throw new TypeError("target must be an object");var t=Sp(e);if(1===arguments.length)return t;for(var r=1;r<arguments.length;++r){var n=Sp(arguments[r]),o=Op(n),a=Rp&&(Object.getOwnPropertySymbols||Tp);if(a)for(var i=a(n),l=0;l<i.length;++l){var u=i[l];_p(n,u)&&Ap(o,u)}for(var s=0;s<o.length;++s){var c=o[s];if(_p(n,c)){var d=n[c];t[c]=d}}}return t},Ip=Mp,Bp=function(){return Object.assign?function(){if(!Object.assign)return!1;for(var e="abcdefghijklmnopqrst",t=e.split(""),r={},n=0;n<t.length;++n)r[t[n]]=t[n];var o=Object.assign({},r),a="";for(var i in o)a+=i;return e!==a}()||function(){if(!Object.assign||!Object.preventExtensions)return!1;var e=Object.preventExtensions({1:2});try{Object.assign(e,"xy")}catch(t){return"y"===e[1]}return!1}()?Ip:Object.assign:Ip},kp=bp,Fp=Bp,Np=bp,Lp=Mp,Up=Bp,Dp=function(){var e=Fp();return kp(Object,{assign:e},{assign:function(){return Object.assign!==e}}),e},Hp=mp.exports.apply(Up()),$p=function(){return Hp(Object,arguments)};Np($p,{getPolyfill:Up,implementation:Lp,shim:Dp});var Wp=$p,zp=function(){return"string"==typeof function(){}.name},Vp=Object.getOwnPropertyDescriptor;if(Vp)try{Vp([],"length")}catch(e){Vp=null}zp.functionsHaveConfigurableNames=function(){if(!zp()||!Vp)return!1;var e=Vp((function(){}),"name");return!!e&&!!e.configurable};var Gp=Function.prototype.bind;zp.boundFunctionsHaveNames=function(){return zp()&&"function"==typeof Gp&&""!==function(){}.bind().name};var Jp=zp,Qp=rp,Xp=ap(),Kp=Jp.functionsHaveConfigurableNames(),Yp=Yc,Zp=function(e,t){if("function"!=typeof e)throw new Yp("`fn` is not a function");return arguments.length>2&&!!arguments[2]&&!Kp||(Xp?Qp(e,"name",t,!0,!0):Qp(e,"name",t)),e},ef=Yc,tf=Object,rf=Zp((function(){if(null==this||this!==tf(this))throw new ef("RegExp.prototype.flags getter called on non-object");var e="";return this.hasIndices&&(e+="d"),this.global&&(e+="g"),this.ignoreCase&&(e+="i"),this.multiline&&(e+="m"),this.dotAll&&(e+="s"),this.unicode&&(e+="u"),this.unicodeSets&&(e+="v"),this.sticky&&(e+="y"),e}),"get flags",!0),nf=rf,of=bp.supportsDescriptors,af=Object.getOwnPropertyDescriptor,lf=function(){if(of&&"gim"===/a/gim.flags){var e=af(RegExp.prototype,"flags");if(e&&"function"==typeof e.get&&"boolean"==typeof RegExp.prototype.dotAll&&"boolean"==typeof RegExp.prototype.hasIndices){var t="",r={};if(Object.defineProperty(r,"hasIndices",{get:function(){t+="d"}}),Object.defineProperty(r,"sticky",{get:function(){t+="y"}}),"dy"===t)return e.get}}return nf},uf=bp.supportsDescriptors,sf=lf,cf=Object.getOwnPropertyDescriptor,df=Object.defineProperty,pf=TypeError,ff=Object.getPrototypeOf,bf=/a/,mf=bp,yf=rf,vf=lf,hf=function(){if(!uf||!ff)throw new pf("RegExp.prototype.flags requires a true ES5 environment that supports property descriptors");var e=sf(),t=ff(bf),r=cf(t,"flags");return r&&r.get===e||df(t,"flags",{configurable:!0,enumerable:!1,get:e}),e},gf=(0,mp.exports)(vf());mf(gf,{getPolyfill:vf,implementation:yf,shim:hf});var Pf=gf,Cf={exports:{}},wf=ed(),qf=function(){return wf()&&!!Symbol.toStringTag},Ef=qf(),xf=xp("Object.prototype.toString"),Of=function(e){return!(Ef&&e&&"object"==typeof e&&Symbol.toStringTag in e)&&"[object Arguments]"===xf(e)},Rf=function(e){return!!Of(e)||null!==e&&"object"==typeof e&&"number"==typeof e.length&&e.length>=0&&"[object Array]"!==xf(e)&&"[object Function]"===xf(e.callee)},jf=function(){return Of(arguments)}();Of.isLegacyArguments=Rf;var Sf=jf?Of:Rf,Af=g(Object.freeze({__proto__:null,default:{}})),_f="function"==typeof Map&&Map.prototype,Tf=Object.getOwnPropertyDescriptor&&_f?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,Mf=_f&&Tf&&"function"==typeof Tf.get?Tf.get:null,If=_f&&Map.prototype.forEach,Bf="function"==typeof Set&&Set.prototype,kf=Object.getOwnPropertyDescriptor&&Bf?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,Ff=Bf&&kf&&"function"==typeof kf.get?kf.get:null,Nf=Bf&&Set.prototype.forEach,Lf="function"==typeof WeakMap&&WeakMap.prototype?WeakMap.prototype.has:null,Uf="function"==typeof WeakSet&&WeakSet.prototype?WeakSet.prototype.has:null,Df="function"==typeof WeakRef&&WeakRef.prototype?WeakRef.prototype.deref:null,Hf=Boolean.prototype.valueOf,$f=Object.prototype.toString,Wf=Function.prototype.toString,zf=String.prototype.match,Vf=String.prototype.slice,Gf=String.prototype.replace,Jf=String.prototype.toUpperCase,Qf=String.prototype.toLowerCase,Xf=RegExp.prototype.test,Kf=Array.prototype.concat,Yf=Array.prototype.join,Zf=Array.prototype.slice,eb=Math.floor,tb="function"==typeof BigInt?BigInt.prototype.valueOf:null,rb=Object.getOwnPropertySymbols,nb="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol.prototype.toString:null,ob="function"==typeof Symbol&&"object"==typeof Symbol.iterator,ab="function"==typeof Symbol&&Symbol.toStringTag&&(typeof Symbol.toStringTag===ob||"symbol")?Symbol.toStringTag:null,ib=Object.prototype.propertyIsEnumerable,lb=("function"==typeof Reflect?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(e){return e.__proto__}:null);function ub(e,t){if(e===1/0||e===-1/0||e!=e||e&&e>-1e3&&e<1e3||Xf.call(/e/,t))return t;var r=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if("number"==typeof e){var n=e<0?-eb(-e):eb(e);if(n!==e){var o=String(n),a=Vf.call(t,o.length+1);return Gf.call(o,r,"$&_")+"."+Gf.call(Gf.call(a,/([0-9]{3})/g,"$&_"),/_$/,"")}}return Gf.call(t,r,"$&_")}var sb=Af,cb=sb.custom,db=yb(cb)?cb:null;function pb(e,t,r){var n="double"===(r.quoteStyle||t)?'"':"'";return n+e+n}function fb(e){return Gf.call(String(e),/"/g,"&quot;")}function bb(e){return!("[object Array]"!==gb(e)||ab&&"object"==typeof e&&ab in e)}function mb(e){return!("[object RegExp]"!==gb(e)||ab&&"object"==typeof e&&ab in e)}function yb(e){if(ob)return e&&"object"==typeof e&&e instanceof Symbol;if("symbol"==typeof e)return!0;if(!e||"object"!=typeof e||!nb)return!1;try{return nb.call(e),!0}catch(e){}return!1}var vb=Object.prototype.hasOwnProperty||function(e){return e in this};function hb(e,t){return vb.call(e,t)}function gb(e){return $f.call(e)}function Pb(e,t){if(e.indexOf)return e.indexOf(t);for(var r=0,n=e.length;r<n;r++)if(e[r]===t)return r;return-1}function Cb(e,t){if(e.length>t.maxStringLength){var r=e.length-t.maxStringLength,n="... "+r+" more character"+(r>1?"s":"");return Cb(Vf.call(e,0,t.maxStringLength),t)+n}return pb(Gf.call(Gf.call(e,/(['\\])/g,"\\$1"),/[\x00-\x1f]/g,wb),"single",t)}function wb(e){var t=e.charCodeAt(0),r={8:"b",9:"t",10:"n",12:"f",13:"r"}[t];return r?"\\"+r:"\\x"+(t<16?"0":"")+Jf.call(t.toString(16))}function qb(e){return"Object("+e+")"}function Eb(e){return e+" { ? }"}function xb(e,t,r,n){return e+" ("+t+") {"+(n?Ob(r,n):Yf.call(r,", "))+"}"}function Ob(e,t){if(0===e.length)return"";var r="\n"+t.prev+t.base;return r+Yf.call(e,","+r)+"\n"+t.prev}function Rb(e,t){var r=bb(e),n=[];if(r){n.length=e.length;for(var o=0;o<e.length;o++)n[o]=hb(e,o)?t(e[o],e):""}var a,i="function"==typeof rb?rb(e):[];if(ob){a={};for(var l=0;l<i.length;l++)a["$"+i[l]]=i[l]}for(var u in e)hb(e,u)&&(r&&String(Number(u))===u&&u<e.length||ob&&a["$"+u]instanceof Symbol||(Xf.call(/[^\w$]/,u)?n.push(t(u,e)+": "+t(e[u],e)):n.push(u+": "+t(e[u],e))));if("function"==typeof rb)for(var s=0;s<i.length;s++)ib.call(e,i[s])&&n.push("["+t(i[s])+"]: "+t(e[i[s]],e));return n}var jb=Jd,Sb=xp,Ab=function e(t,r,n,o){var a=r||{};if(hb(a,"quoteStyle")&&"single"!==a.quoteStyle&&"double"!==a.quoteStyle)throw new TypeError('option "quoteStyle" must be "single" or "double"');if(hb(a,"maxStringLength")&&("number"==typeof a.maxStringLength?a.maxStringLength<0&&a.maxStringLength!==1/0:null!==a.maxStringLength))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var i=!hb(a,"customInspect")||a.customInspect;if("boolean"!=typeof i&&"symbol"!==i)throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(hb(a,"indent")&&null!==a.indent&&"\t"!==a.indent&&!(parseInt(a.indent,10)===a.indent&&a.indent>0))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(hb(a,"numericSeparator")&&"boolean"!=typeof a.numericSeparator)throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var l=a.numericSeparator;if(void 0===t)return"undefined";if(null===t)return"null";if("boolean"==typeof t)return t?"true":"false";if("string"==typeof t)return Cb(t,a);if("number"==typeof t){if(0===t)return 1/0/t>0?"0":"-0";var u=String(t);return l?ub(t,u):u}if("bigint"==typeof t){var s=String(t)+"n";return l?ub(t,s):s}var c=void 0===a.depth?5:a.depth;if(void 0===n&&(n=0),n>=c&&c>0&&"object"==typeof t)return bb(t)?"[Array]":"[Object]";var d=function(e,t){var r;if("\t"===e.indent)r="\t";else{if(!("number"==typeof e.indent&&e.indent>0))return null;r=Yf.call(Array(e.indent+1)," ")}return{base:r,prev:Yf.call(Array(t+1),r)}}(a,n);if(void 0===o)o=[];else if(Pb(o,t)>=0)return"[Circular]";function p(t,r,i){if(r&&(o=Zf.call(o)).push(r),i){var l={depth:a.depth};return hb(a,"quoteStyle")&&(l.quoteStyle=a.quoteStyle),e(t,l,n+1,o)}return e(t,a,n+1,o)}if("function"==typeof t&&!mb(t)){var f=function(e){if(e.name)return e.name;var t=zf.call(Wf.call(e),/^function\s*([\w$]+)/);if(t)return t[1];return null}(t),b=Rb(t,p);return"[Function"+(f?": "+f:" (anonymous)")+"]"+(b.length>0?" { "+Yf.call(b,", ")+" }":"")}if(yb(t)){var m=ob?Gf.call(String(t),/^(Symbol\(.*\))_[^)]*$/,"$1"):nb.call(t);return"object"!=typeof t||ob?m:qb(m)}if(function(e){if(!e||"object"!=typeof e)return!1;if("undefined"!=typeof HTMLElement&&e instanceof HTMLElement)return!0;return"string"==typeof e.nodeName&&"function"==typeof e.getAttribute}(t)){for(var y="<"+Qf.call(String(t.nodeName)),v=t.attributes||[],g=0;g<v.length;g++)y+=" "+v[g].name+"="+pb(fb(v[g].value),"double",a);return y+=">",t.childNodes&&t.childNodes.length&&(y+="..."),y+="</"+Qf.call(String(t.nodeName))+">"}if(bb(t)){if(0===t.length)return"[]";var P=Rb(t,p);return d&&!function(e){for(var t=0;t<e.length;t++)if(Pb(e[t],"\n")>=0)return!1;return!0}(P)?"["+Ob(P,d)+"]":"[ "+Yf.call(P,", ")+" ]"}if(function(e){return!("[object Error]"!==gb(e)||ab&&"object"==typeof e&&ab in e)}(t)){var C=Rb(t,p);return"cause"in Error.prototype||!("cause"in t)||ib.call(t,"cause")?0===C.length?"["+String(t)+"]":"{ ["+String(t)+"] "+Yf.call(C,", ")+" }":"{ ["+String(t)+"] "+Yf.call(Kf.call("[cause]: "+p(t.cause),C),", ")+" }"}if("object"==typeof t&&i){if(db&&"function"==typeof t[db]&&sb)return sb(t,{depth:c-n});if("symbol"!==i&&"function"==typeof t.inspect)return t.inspect()}if(function(e){if(!Mf||!e||"object"!=typeof e)return!1;try{Mf.call(e);try{Ff.call(e)}catch(e){return!0}return e instanceof Map}catch(e){}return!1}(t)){var w=[];return If&&If.call(t,(function(e,r){w.push(p(r,t,!0)+" => "+p(e,t))})),xb("Map",Mf.call(t),w,d)}if(function(e){if(!Ff||!e||"object"!=typeof e)return!1;try{Ff.call(e);try{Mf.call(e)}catch(e){return!0}return e instanceof Set}catch(e){}return!1}(t)){var q=[];return Nf&&Nf.call(t,(function(e){q.push(p(e,t))})),xb("Set",Ff.call(t),q,d)}if(function(e){if(!Lf||!e||"object"!=typeof e)return!1;try{Lf.call(e,Lf);try{Uf.call(e,Uf)}catch(e){return!0}return e instanceof WeakMap}catch(e){}return!1}(t))return Eb("WeakMap");if(function(e){if(!Uf||!e||"object"!=typeof e)return!1;try{Uf.call(e,Uf);try{Lf.call(e,Lf)}catch(e){return!0}return e instanceof WeakSet}catch(e){}return!1}(t))return Eb("WeakSet");if(function(e){if(!Df||!e||"object"!=typeof e)return!1;try{return Df.call(e),!0}catch(e){}return!1}(t))return Eb("WeakRef");if(function(e){return!("[object Number]"!==gb(e)||ab&&"object"==typeof e&&ab in e)}(t))return qb(p(Number(t)));if(function(e){if(!e||"object"!=typeof e||!tb)return!1;try{return tb.call(e),!0}catch(e){}return!1}(t))return qb(p(tb.call(t)));if(function(e){return!("[object Boolean]"!==gb(e)||ab&&"object"==typeof e&&ab in e)}(t))return qb(Hf.call(t));if(function(e){return!("[object String]"!==gb(e)||ab&&"object"==typeof e&&ab in e)}(t))return qb(p(String(t)));if("undefined"!=typeof window&&t===window)return"{ [object Window] }";if(t===h)return"{ [object globalThis] }";if(!function(e){return!("[object Date]"!==gb(e)||ab&&"object"==typeof e&&ab in e)}(t)&&!mb(t)){var E=Rb(t,p),x=lb?lb(t)===Object.prototype:t instanceof Object||t.constructor===Object,O=t instanceof Object?"":"null prototype",R=!x&&ab&&Object(t)===t&&ab in t?Vf.call(gb(t),8,-1):O?"Object":"",j=(x||"function"!=typeof t.constructor?"":t.constructor.name?t.constructor.name+" ":"")+(R||O?"["+Yf.call(Kf.call([],R||[],O||[]),": ")+"] ":"");return 0===E.length?j+"{}":d?j+"{"+Ob(E,d)+"}":j+"{ "+Yf.call(E,", ")+" }"}return String(t)},_b=Yc,Tb=jb("%WeakMap%",!0),Mb=jb("%Map%",!0),Ib=Sb("WeakMap.prototype.get",!0),Bb=Sb("WeakMap.prototype.set",!0),kb=Sb("WeakMap.prototype.has",!0),Fb=Sb("Map.prototype.get",!0),Nb=Sb("Map.prototype.set",!0),Lb=Sb("Map.prototype.has",!0),Ub=function(e,t){for(var r,n=e;null!==(r=n.next);n=r)if(r.key===t)return n.next=r.next,r.next=e.next,e.next=r,r},Db=function(){var e,t,r,n={assert:function(e){if(!n.has(e))throw new _b("Side channel does not contain "+Ab(e))},get:function(n){if(Tb&&n&&("object"==typeof n||"function"==typeof n)){if(e)return Ib(e,n)}else if(Mb){if(t)return Fb(t,n)}else if(r)return function(e,t){var r=Ub(e,t);return r&&r.value}(r,n)},has:function(n){if(Tb&&n&&("object"==typeof n||"function"==typeof n)){if(e)return kb(e,n)}else if(Mb){if(t)return Lb(t,n)}else if(r)return function(e,t){return!!Ub(e,t)}(r,n);return!1},set:function(n,o){Tb&&n&&("object"==typeof n||"function"==typeof n)?(e||(e=new Tb),Bb(e,n,o)):Mb?(t||(t=new Mb),Nb(t,n,o)):(r||(r={key:{},next:null}),function(e,t,r){var n=Ub(e,t);n?n.value=r:e.next={key:t,next:e.next,value:r}}(r,n,o))}};return n},Hb=bd,$b=Db(),Wb=Yc,zb={assert:function(e,t){if(!e||"object"!=typeof e&&"function"!=typeof e)throw new Wb("`O` is not an object");if("string"!=typeof t)throw new Wb("`slot` must be a string");if($b.assert(e),!zb.has(e,t))throw new Wb("`"+t+"` is not present on `O`")},get:function(e,t){if(!e||"object"!=typeof e&&"function"!=typeof e)throw new Wb("`O` is not an object");if("string"!=typeof t)throw new Wb("`slot` must be a string");var r=$b.get(e);return r&&r["$"+t]},has:function(e,t){if(!e||"object"!=typeof e&&"function"!=typeof e)throw new Wb("`O` is not an object");if("string"!=typeof t)throw new Wb("`slot` must be a string");var r=$b.get(e);return!!r&&Hb(r,"$"+t)},set:function(e,t,r){if(!e||"object"!=typeof e&&"function"!=typeof e)throw new Wb("`O` is not an object");if("string"!=typeof t)throw new Wb("`slot` must be a string");var n=$b.get(e);n||(n={},$b.set(e,n)),n["$"+t]=r}};Object.freeze&&Object.freeze(zb);var Vb,Gb=zb,Jb=SyntaxError,Qb="object"==typeof StopIteration?StopIteration:null,Xb={}.toString,Kb=Array.isArray||function(e){return"[object Array]"==Xb.call(e)},Yb=String.prototype.valueOf,Zb=Object.prototype.toString,em=qf(),tm=function(e){return"string"==typeof e||"object"==typeof e&&(em?function(e){try{return Yb.call(e),!0}catch(e){return!1}}(e):"[object String]"===Zb.call(e))},rm="function"==typeof Map&&Map.prototype?Map:null,nm="function"==typeof Set&&Set.prototype?Set:null;rm||(Vb=function(){return!1});var om=rm?Map.prototype.has:null,am=nm?Set.prototype.has:null;Vb||om||(Vb=function(){return!1});var im,lm=Vb||function(e){if(!e||"object"!=typeof e)return!1;try{if(om.call(e),am)try{am.call(e)}catch(e){return!0}return e instanceof rm}catch(e){}return!1},um="function"==typeof Map&&Map.prototype?Map:null,sm="function"==typeof Set&&Set.prototype?Set:null;sm||(im=function(){return!1});var cm=um?Map.prototype.has:null,dm=sm?Set.prototype.has:null;im||dm||(im=function(){return!1});var pm=im||function(e){if(!e||"object"!=typeof e)return!1;try{if(dm.call(e),cm)try{cm.call(e)}catch(e){return!0}return e instanceof sm}catch(e){}return!1},fm=Sf,bm=function(e){if(!Qb)throw new Jb("this environment lacks StopIteration");Gb.set(e,"[[Done]]",!1);var t={next:function(){var e=Gb.get(this,"[[Iterator]]"),t=Gb.get(e,"[[Done]]");try{return{done:t,value:t?void 0:e.next()}}catch(t){if(Gb.set(e,"[[Done]]",!0),t!==Qb)throw t;return{done:!0,value:void 0}}}};return Gb.set(t,"[[Iterator]]",e),t};if(od()||ed()()){var mm=Symbol.iterator;Cf.exports=function(e){return null!=e&&void 0!==e[mm]?e[mm]():fm(e)?Array.prototype[mm].call(e):void 0}}else{var ym=Kb,vm=tm,hm=Jd,gm=hm("%Map%",!0),Pm=hm("%Set%",!0),Cm=xp,wm=Cm("Array.prototype.push"),qm=Cm("String.prototype.charCodeAt"),Em=Cm("String.prototype.slice"),xm=function(e){var t=0;return{next:function(){var r,n=t>=e.length;return n||(r=e[t],t+=1),{done:n,value:r}}}},Om=function(e,t){if(ym(e)||fm(e))return xm(e);if(vm(e)){var r=0;return{next:function(){var t=function(e,t){if(t+1>=e.length)return t+1;var r=qm(e,t);if(r<55296||r>56319)return t+1;var n=qm(e,t+1);return n<56320||n>57343?t+1:t+2}(e,r),n=Em(e,r,t);return r=t,{done:t>e.length,value:n}}}}return t&&void 0!==e["_es6-shim iterator_"]?e["_es6-shim iterator_"]():void 0};if(gm||Pm){var Rm=lm,jm=pm,Sm=Cm("Map.prototype.forEach",!0),Am=Cm("Set.prototype.forEach",!0);if("undefined"==typeof process||!process.versions||!process.versions.node)var _m=Cm("Map.prototype.iterator",!0),Tm=Cm("Set.prototype.iterator",!0);var Mm=Cm("Map.prototype.@@iterator",!0)||Cm("Map.prototype._es6-shim iterator_",!0),Im=Cm("Set.prototype.@@iterator",!0)||Cm("Set.prototype._es6-shim iterator_",!0);Cf.exports=function(e){return function(e){if(Rm(e)){if(_m)return bm(_m(e));if(Mm)return Mm(e);if(Sm){var t=[];return Sm(e,(function(e,r){wm(t,[r,e])})),xm(t)}}if(jm(e)){if(Tm)return bm(Tm(e));if(Im)return Im(e);if(Am){var r=[];return Am(e,(function(e){wm(r,e)})),xm(r)}}}(e)||Om(e)}}else Cf.exports=function(e){if(null!=e)return Om(e,!0)}}var Bm=function(e){return e!=e},km=function(e,t){return 0===e&&0===t?1/e==1/t:e===t||!(!Bm(e)||!Bm(t))},Fm=km,Nm=function(){return"function"==typeof Object.is?Object.is:Fm},Lm=Nm,Um=bp,Dm=bp,Hm=km,$m=Nm,Wm=function(){var e=Lm();return Um(Object,{is:e},{is:function(){return Object.is!==e}}),e},zm=(0,mp.exports)($m(),Object);Dm(zm,{getPolyfill:$m,implementation:Hm,shim:Wm});var Vm,Gm,Jm,Qm,Xm=zm,Km=mp.exports,Ym=xp,Zm=Jd("%ArrayBuffer%",!0),ey=Ym("ArrayBuffer.prototype.byteLength",!0),ty=Ym("Object.prototype.toString"),ry=!!Zm&&!ey&&new Zm(0).slice,ny=!!ry&&Km(ry),oy=ey||ny?function(e){if(!e||"object"!=typeof e)return!1;try{return ey?ey(e):ny(e,0),!0}catch(e){return!1}}:Zm?function(e){return"[object ArrayBuffer]"===ty(e)}:function(){return!1},ay=Date.prototype.getDay,iy=Object.prototype.toString,ly=qf(),uy=xp,sy=qf();if(sy){Vm=uy("Object.prototype.hasOwnProperty"),Gm=uy("RegExp.prototype.exec"),Jm={};var cy=function(){throw Jm};Qm={toString:cy,valueOf:cy},"symbol"==typeof Symbol.toPrimitive&&(Qm[Symbol.toPrimitive]=cy)}var dy=uy("Object.prototype.toString"),py=Object.getOwnPropertyDescriptor,fy=sy?function(e){if(!e||"object"!=typeof e)return!1;var t=py(e,"lastIndex");if(!(t&&Vm(t,"value")))return!1;try{Gm(e,Qm)}catch(e){return e===Jm}}:function(e){return!(!e||"object"!=typeof e&&"function"!=typeof e)&&"[object RegExp]"===dy(e)},by=xp("SharedArrayBuffer.prototype.byteLength",!0),my=by?function(e){if(!e||"object"!=typeof e)return!1;try{return by(e),!0}catch(e){return!1}}:function(){return!1},yy=Number.prototype.toString,vy=Object.prototype.toString,hy=qf(),gy=xp,Py=gy("Boolean.prototype.toString"),Cy=gy("Object.prototype.toString"),wy=qf(),qy={exports:{}},Ey=Object.prototype.toString;if(od()){var xy=Symbol.prototype.toString,Oy=/^Symbol\(.*\)$/;qy.exports=function(e){if("symbol"==typeof e)return!0;if("[object Symbol]"!==Ey.call(e))return!1;try{return function(e){return"symbol"==typeof e.valueOf()&&Oy.test(xy.call(e))}(e)}catch(e){return!1}}}else qy.exports=function(e){return!1};var Ry={exports:{}},jy="undefined"!=typeof BigInt&&BigInt;if("function"==typeof jy&&"function"==typeof BigInt&&"bigint"==typeof jy(42)&&"bigint"==typeof BigInt(42)){var Sy=BigInt.prototype.valueOf;Ry.exports=function(e){return null!=e&&"boolean"!=typeof e&&"string"!=typeof e&&"number"!=typeof e&&"symbol"!=typeof e&&"function"!=typeof e&&("bigint"==typeof e||function(e){try{return Sy.call(e),!0}catch(e){}return!1}(e))}}else Ry.exports=function(e){return!1};var Ay,_y=tm,Ty=function(e){return"number"==typeof e||"object"==typeof e&&(hy?function(e){try{return yy.call(e),!0}catch(e){return!1}}(e):"[object Number]"===vy.call(e))},My=function(e){return"boolean"==typeof e||null!==e&&"object"==typeof e&&(wy&&Symbol.toStringTag in e?function(e){try{return Py(e),!0}catch(e){return!1}}(e):"[object Boolean]"===Cy(e))},Iy=qy.exports,By=Ry.exports,ky="function"==typeof WeakMap&&WeakMap.prototype?WeakMap:null,Fy="function"==typeof WeakSet&&WeakSet.prototype?WeakSet:null;ky||(Ay=function(){return!1});var Ny=ky?ky.prototype.has:null,Ly=Fy?Fy.prototype.has:null;Ay||Ny||(Ay=function(){return!1});var Uy=Ay||function(e){if(!e||"object"!=typeof e)return!1;try{if(Ny.call(e,Ny),Ly)try{Ly.call(e,Ly)}catch(e){return!0}return e instanceof ky}catch(e){}return!1},Dy={exports:{}},Hy=xp,$y=Jd("%WeakSet%",!0),Wy=Hy("WeakSet.prototype.has",!0);if(Wy){var zy=Hy("WeakMap.prototype.has",!0);Dy.exports=function(e){if(!e||"object"!=typeof e)return!1;try{if(Wy(e,Wy),zy)try{zy(e,zy)}catch(e){return!0}return e instanceof $y}catch(e){}return!1}}else Dy.exports=function(){return!1};var Vy,Gy,Jy=lm,Qy=pm,Xy=Uy,Ky=Dy.exports,Yy=Function.prototype.toString,Zy="object"==typeof Reflect&&null!==Reflect&&Reflect.apply;if("function"==typeof Zy&&"function"==typeof Object.defineProperty)try{Vy=Object.defineProperty({},"length",{get:function(){throw Gy}}),Gy={},Zy((function(){throw 42}),null,Vy)}catch(e){e!==Gy&&(Zy=null)}else Zy=null;var ev=/^\s*class\b/,tv=function(e){try{var t=Yy.call(e);return ev.test(t)}catch(e){return!1}},rv=function(e){try{return!tv(e)&&(Yy.call(e),!0)}catch(e){return!1}},nv=Object.prototype.toString,ov="function"==typeof Symbol&&!!Symbol.toStringTag,av=!(0 in[,]),iv=function(){return!1};if("object"==typeof document){var lv=document.all;nv.call(lv)===nv.call(document.all)&&(iv=function(e){if((av||!e)&&(void 0===e||"object"==typeof e))try{var t=nv.call(e);return("[object HTMLAllCollection]"===t||"[object HTML document.all class]"===t||"[object HTMLCollection]"===t||"[object Object]"===t)&&null==e("")}catch(e){}return!1})}var uv=Zy?function(e){if(iv(e))return!0;if(!e)return!1;if("function"!=typeof e&&"object"!=typeof e)return!1;try{Zy(e,null,Vy)}catch(e){if(e!==Gy)return!1}return!tv(e)&&rv(e)}:function(e){if(iv(e))return!0;if(!e)return!1;if("function"!=typeof e&&"object"!=typeof e)return!1;if(ov)return rv(e);if(tv(e))return!1;var t=nv.call(e);return!("[object Function]"!==t&&"[object GeneratorFunction]"!==t&&!/^\[object HTML/.test(t))&&rv(e)},sv=uv,cv=Object.prototype.toString,dv=Object.prototype.hasOwnProperty,pv=function(e,t,r){if(!sv(t))throw new TypeError("iterator must be a function");var n;arguments.length>=3&&(n=r),"[object Array]"===cv.call(e)?function(e,t,r){for(var n=0,o=e.length;n<o;n++)dv.call(e,n)&&(null==r?t(e[n],n,e):t.call(r,e[n],n,e))}(e,t,n):"string"==typeof e?function(e,t,r){for(var n=0,o=e.length;n<o;n++)null==r?t(e.charAt(n),n,e):t.call(r,e.charAt(n),n,e)}(e,t,n):function(e,t,r){for(var n in e)dv.call(e,n)&&(null==r?t(e[n],n,e):t.call(r,e[n],n,e))}(e,t,n)},fv=["Float32Array","Float64Array","Int8Array","Int16Array","Int32Array","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","BigInt64Array","BigUint64Array"],bv="undefined"==typeof globalThis?h:globalThis,mv=pv,yv=function(){for(var e=[],t=0;t<fv.length;t++)"function"==typeof bv[fv[t]]&&(e[e.length]=fv[t]);return e},vv=mp.exports,hv=xp,gv=Kd,Pv=hv("Object.prototype.toString"),Cv=qf(),wv="undefined"==typeof globalThis?h:globalThis,qv=yv(),Ev=hv("String.prototype.slice"),xv=Object.getPrototypeOf,Ov=hv("Array.prototype.indexOf",!0)||function(e,t){for(var r=0;r<e.length;r+=1)if(e[r]===t)return r;return-1},Rv={__proto__:null};mv(qv,Cv&&gv&&xv?function(e){var t=new wv[e];if(Symbol.toStringTag in t){var r=xv(t),n=gv(r,Symbol.toStringTag);if(!n){var o=xv(r);n=gv(o,Symbol.toStringTag)}Rv["$"+e]=vv(n.get)}}:function(e){var t=new wv[e],r=t.slice||t.set;r&&(Rv["$"+e]=vv(r))});var jv=xp("ArrayBuffer.prototype.byteLength",!0),Sv=oy,Av=Wp,_v=xp,Tv=Pf,Mv=Jd,Iv=Cf.exports,Bv=Db,kv=Xm,Fv=Sf,Nv=Kb,Lv=oy,Uv=function(e){return"object"==typeof e&&null!==e&&(ly?function(e){try{return ay.call(e),!0}catch(e){return!1}}(e):"[object Date]"===iy.call(e))},Dv=fy,Hv=my,$v=Vc,Wv=function(e){return null==e||"object"!=typeof e&&"function"!=typeof e?null:_y(e)?"String":Ty(e)?"Number":My(e)?"Boolean":Iy(e)?"Symbol":By(e)?"BigInt":void 0},zv=function(e){if(e&&"object"==typeof e){if(Jy(e))return"Map";if(Qy(e))return"Set";if(Xy(e))return"WeakMap";if(Ky(e))return"WeakSet"}return!1},Vv=function(e){if(!e||"object"!=typeof e)return!1;if(!Cv){var t=Ev(Pv(e),8,-1);return Ov(qv,t)>-1?t:"Object"===t&&function(e){var t=!1;return mv(Rv,(function(r,n){if(!t)try{r(e),t=Ev(n,1)}catch(e){}})),t}(e)}return gv?function(e){var t=!1;return mv(Rv,(function(r,n){if(!t)try{"$"+r(e)===n&&(t=Ev(n,1))}catch(e){}})),t}(e):null},Gv=function(e){return Sv(e)?jv?jv(e):e.byteLength:NaN},Jv=_v("SharedArrayBuffer.prototype.byteLength",!0),Qv=_v("Date.prototype.getTime"),Xv=Object.getPrototypeOf,Kv=_v("Object.prototype.toString"),Yv=Mv("%Set%",!0),Zv=_v("Map.prototype.has",!0),eh=_v("Map.prototype.get",!0),th=_v("Map.prototype.size",!0),rh=_v("Set.prototype.add",!0),nh=_v("Set.prototype.delete",!0),oh=_v("Set.prototype.has",!0),ah=_v("Set.prototype.size",!0);function ih(e,t,r,n){for(var o,a=Iv(e);(o=a.next())&&!o.done;)if(dh(t,o.value,r,n))return nh(e,o.value),!0;return!1}function lh(e){return void 0===e?null:"object"!=typeof e?"symbol"!=typeof e&&("string"!=typeof e&&"number"!=typeof e||+e==+e):void 0}function uh(e,t,r,n,o,a){var i=lh(r);if(null!=i)return i;var l=eh(t,i),u=Av({},o,{strict:!1});return!(void 0===l&&!Zv(t,i)||!dh(n,l,u,a))&&(!Zv(e,i)&&dh(n,l,u,a))}function sh(e,t,r){var n=lh(r);return null!=n?n:oh(t,n)&&!oh(e,n)}function ch(e,t,r,n,o,a){for(var i,l,u=Iv(e);(i=u.next())&&!i.done;)if(dh(r,l=i.value,o,a)&&dh(n,eh(t,l),o,a))return nh(e,l),!0;return!1}function dh(e,t,r,n){var o=r||{};if(o.strict?kv(e,t):e===t)return!0;if(Wv(e)!==Wv(t))return!1;if(!e||!t||"object"!=typeof e&&"object"!=typeof t)return o.strict?kv(e,t):e==t;var a,i=n.has(e),l=n.has(t);if(i&&l){if(n.get(e)===n.get(t))return!0}else a={};return i||n.set(e,a),l||n.set(t,a),function(e,t,r,n){var o,a;if(typeof e!=typeof t)return!1;if(null==e||null==t)return!1;if(Kv(e)!==Kv(t))return!1;if(Fv(e)!==Fv(t))return!1;var i=Nv(e),l=Nv(t);if(i!==l)return!1;var u=e instanceof Error,s=t instanceof Error;if(u!==s)return!1;if((u||s)&&(e.name!==t.name||e.message!==t.message))return!1;var c=Dv(e),d=Dv(t);if(c!==d)return!1;if((c||d)&&(e.source!==t.source||Tv(e)!==Tv(t)))return!1;var p=Uv(e),f=Uv(t);if(p!==f)return!1;if((p||f)&&Qv(e)!==Qv(t))return!1;if(r.strict&&Xv&&Xv(e)!==Xv(t))return!1;var b=Vv(e),m=Vv(t);if(b!==m)return!1;if(b||m){if(e.length!==t.length)return!1;for(o=0;o<e.length;o++)if(e[o]!==t[o])return!1;return!0}var y=ph(e),v=ph(t);if(y!==v)return!1;if(y||v){if(e.length!==t.length)return!1;for(o=0;o<e.length;o++)if(e[o]!==t[o])return!1;return!0}var h=Lv(e),g=Lv(t);if(h!==g)return!1;if(h||g)return Gv(e)===Gv(t)&&("function"==typeof Uint8Array&&dh(new Uint8Array(e),new Uint8Array(t),r,n));var P=Hv(e),C=Hv(t);if(P!==C)return!1;if(P||C)return Jv(e)===Jv(t)&&("function"==typeof Uint8Array&&dh(new Uint8Array(e),new Uint8Array(t),r,n));if(typeof e!=typeof t)return!1;var w=$v(e),q=$v(t);if(w.length!==q.length)return!1;for(w.sort(),q.sort(),o=w.length-1;o>=0;o--)if(w[o]!=q[o])return!1;for(o=w.length-1;o>=0;o--)if(!dh(e[a=w[o]],t[a],r,n))return!1;var E=zv(e),x=zv(t);if(E!==x)return!1;if("Set"===E||"Set"===x)return function(e,t,r,n){if(ah(e)!==ah(t))return!1;var o,a,i,l=Iv(e),u=Iv(t);for(;(o=l.next())&&!o.done;)if(o.value&&"object"==typeof o.value)i||(i=new Yv),rh(i,o.value);else if(!oh(t,o.value)){if(r.strict)return!1;if(!sh(e,t,o.value))return!1;i||(i=new Yv),rh(i,o.value)}if(i){for(;(a=u.next())&&!a.done;)if(a.value&&"object"==typeof a.value){if(!ih(i,a.value,r.strict,n))return!1}else if(!r.strict&&!oh(e,a.value)&&!ih(i,a.value,r.strict,n))return!1;return 0===ah(i)}return!0}(e,t,r,n);if("Map"===E)return function(e,t,r,n){if(th(e)!==th(t))return!1;var o,a,i,l,u,s,c=Iv(e),d=Iv(t);for(;(o=c.next())&&!o.done;)if(l=o.value[0],u=o.value[1],l&&"object"==typeof l)i||(i=new Yv),rh(i,l);else if(void 0===(s=eh(t,l))&&!Zv(t,l)||!dh(u,s,r,n)){if(r.strict)return!1;if(!uh(e,t,l,u,r,n))return!1;i||(i=new Yv),rh(i,l)}if(i){for(;(a=d.next())&&!a.done;)if(l=a.value[0],s=a.value[1],l&&"object"==typeof l){if(!ch(i,e,l,s,r,n))return!1}else if(!(r.strict||e.has(l)&&dh(eh(e,l),s,r,n)||ch(i,e,l,s,Av({},r,{strict:!1}),n)))return!1;return 0===ah(i)}return!0}(e,t,r,n);return!0}(e,t,o,n)}function ph(e){return!(!e||"object"!=typeof e||"number"!=typeof e.length)&&("function"==typeof e.copy&&"function"==typeof e.slice&&(!(e.length>0&&"number"!=typeof e[0])&&!!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e))))}Object.defineProperty(kc,"__esModule",{value:!0}),kc.default=void 0;var fh=yh((function(e,t,r){return dh(e,t,r,Bv())})),bh=yh(sr),mh=yh(Sr);function yh(e){return e&&e.__esModule?e:{default:e}}function vh(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,o,a=[],i=!0,l=!1;try{for(r=r.call(e);!(i=(n=r.next()).done)&&(a.push(n.value),!t||a.length!==t);i=!0);}catch(e){l=!0,o=e}finally{try{i||null==r.return||r.return()}finally{if(l)throw o}}return a}(e,t)||hh(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function hh(e,t){if(e){if("string"==typeof e)return gh(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?gh(e,t):void 0}}function gh(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}for(var Ph=[],Ch=mh.default.keys(),wh=0;wh<Ch.length;wh++){var qh=Ch[wh],Eh=mh.default.get(qh);if(Eh)for(var xh=[].concat(Eh.baseConcepts,Eh.relatedConcepts),Oh=0;Oh<xh.length;Oh++){var Rh=xh[Oh];if("HTML"===Rh.module){var jh=Rh.concept;jh&&function(){var e=JSON.stringify(jh),t=Ph.find((function(t){return JSON.stringify(t[0])===e})),r=void 0;r=t?t[1]:[];for(var n=!0,o=0;o<r.length;o++)if(r[o]===qh){n=!1;break}n&&r.push(qh),Ph.push([jh,r])}()}}}var Sh={entries:function(){return Ph},forEach:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=hh(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,l=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return i=e.done,e},e:function(e){l=!0,a=e},f:function(){try{i||null==r.return||r.return()}finally{if(l)throw a}}}}(Ph);try{for(n.s();!(t=n.n()).done;){var o=vh(t.value,2),a=o[0],i=o[1];e.call(r,i,a,Ph)}}catch(e){n.e(e)}finally{n.f()}},get:function(e){var t=Ph.find((function(t){return(0,fh.default)(e,t[0])}));return t&&t[1]},has:function(e){return!!Sh.get(e)},keys:function(){return Ph.map((function(e){return vh(e,1)[0]}))},values:function(){return Ph.map((function(e){return vh(e,2)[1]}))}},Ah=(0,bh.default)(Sh,Sh.entries());kc.default=Ah;var _h={};Object.defineProperty(_h,"__esModule",{value:!0}),_h.default=void 0;var Th=Ih(sr),Mh=Ih(Sr);function Ih(e){return e&&e.__esModule?e:{default:e}}function Bh(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,o,a=[],i=!0,l=!1;try{for(r=r.call(e);!(i=(n=r.next()).done)&&(a.push(n.value),!t||a.length!==t);i=!0);}catch(e){l=!0,o=e}finally{try{i||null==r.return||r.return()}finally{if(l)throw o}}return a}(e,t)||kh(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function kh(e,t){if(e){if("string"==typeof e)return Fh(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Fh(e,t):void 0}}function Fh(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}for(var Nh=[],Lh=Mh.default.keys(),Uh=function(e){var t=Lh[e],r=Mh.default.get(t);if(r)for(var n=[].concat(r.baseConcepts,r.relatedConcepts),o=0;o<n.length;o++){var a=n[o];if("HTML"===a.module){var i=a.concept;if(i){var l=Nh.find((function(e){return e[0]===t})),u=void 0;(u=l?l[1]:[]).push(i),Nh.push([t,u])}}}},Dh=0;Dh<Lh.length;Dh++)Uh(Dh);var Hh={entries:function(){return Nh},forEach:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=kh(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,l=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return i=e.done,e},e:function(e){l=!0,a=e},f:function(){try{i||null==r.return||r.return()}finally{if(l)throw a}}}}(Nh);try{for(n.s();!(t=n.n()).done;){var o=Bh(t.value,2),a=o[0],i=o[1];e.call(r,i,a,Nh)}}catch(e){n.e(e)}finally{n.f()}},get:function(e){var t=Nh.find((function(t){return t[0]===e}));return t&&t[1]},has:function(e){return!!Hh.get(e)},keys:function(){return Nh.map((function(e){return Bh(e,1)[0]}))},values:function(){return Nh.map((function(e){return Bh(e,2)[1]}))}},$h=(0,Th.default)(Hh,Hh.entries());_h.default=$h,Object.defineProperty(lr,"__esModule",{value:!0});var Wh=lr.roles=ng=lr.roleElements=lr.elementRoles=lr.dom=lr.aria=void 0,zh=Xh(ur),Vh=Xh(Cr),Gh=Xh(Sr),Jh=Xh(kc),Qh=Xh(_h);function Xh(e){return e&&e.__esModule?e:{default:e}}var Kh=zh.default;lr.aria=Kh;var Yh=Vh.default;lr.dom=Yh;var Zh=Gh.default;Wh=lr.roles=Zh;var eg=Jh.default,tg=lr.elementRoles=eg,rg=Qh.default,ng=lr.roleElements=rg,og={exports:{}};!function(e){var t=function(){var e=String.fromCharCode,t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+-$",n={};function o(e,t){if(!n[e]){n[e]={};for(var r=0;r<e.length;r++)n[e][e.charAt(r)]=r}return n[e][t]}var a={compressToBase64:function(e){if(null==e)return"";var r=a._compress(e,6,(function(e){return t.charAt(e)}));switch(r.length%4){default:case 0:return r;case 1:return r+"===";case 2:return r+"==";case 3:return r+"="}},decompressFromBase64:function(e){return null==e?"":""==e?null:a._decompress(e.length,32,(function(r){return o(t,e.charAt(r))}))},compressToUTF16:function(t){return null==t?"":a._compress(t,15,(function(t){return e(t+32)}))+" "},decompressFromUTF16:function(e){return null==e?"":""==e?null:a._decompress(e.length,16384,(function(t){return e.charCodeAt(t)-32}))},compressToUint8Array:function(e){for(var t=a.compress(e),r=new Uint8Array(2*t.length),n=0,o=t.length;n<o;n++){var i=t.charCodeAt(n);r[2*n]=i>>>8,r[2*n+1]=i%256}return r},decompressFromUint8Array:function(t){if(null==t)return a.decompress(t);for(var r=new Array(t.length/2),n=0,o=r.length;n<o;n++)r[n]=256*t[2*n]+t[2*n+1];var i=[];return r.forEach((function(t){i.push(e(t))})),a.decompress(i.join(""))},compressToEncodedURIComponent:function(e){return null==e?"":a._compress(e,6,(function(e){return r.charAt(e)}))},decompressFromEncodedURIComponent:function(e){return null==e?"":""==e?null:(e=e.replace(/ /g,"+"),a._decompress(e.length,32,(function(t){return o(r,e.charAt(t))})))},compress:function(t){return a._compress(t,16,(function(t){return e(t)}))},_compress:function(e,t,r){if(null==e)return"";var n,o,a,i={},l={},u="",s="",c="",d=2,p=3,f=2,b=[],m=0,y=0;for(a=0;a<e.length;a+=1)if(u=e.charAt(a),Object.prototype.hasOwnProperty.call(i,u)||(i[u]=p++,l[u]=!0),s=c+u,Object.prototype.hasOwnProperty.call(i,s))c=s;else{if(Object.prototype.hasOwnProperty.call(l,c)){if(c.charCodeAt(0)<256){for(n=0;n<f;n++)m<<=1,y==t-1?(y=0,b.push(r(m)),m=0):y++;for(o=c.charCodeAt(0),n=0;n<8;n++)m=m<<1|1&o,y==t-1?(y=0,b.push(r(m)),m=0):y++,o>>=1}else{for(o=1,n=0;n<f;n++)m=m<<1|o,y==t-1?(y=0,b.push(r(m)),m=0):y++,o=0;for(o=c.charCodeAt(0),n=0;n<16;n++)m=m<<1|1&o,y==t-1?(y=0,b.push(r(m)),m=0):y++,o>>=1}0==--d&&(d=Math.pow(2,f),f++),delete l[c]}else for(o=i[c],n=0;n<f;n++)m=m<<1|1&o,y==t-1?(y=0,b.push(r(m)),m=0):y++,o>>=1;0==--d&&(d=Math.pow(2,f),f++),i[s]=p++,c=String(u)}if(""!==c){if(Object.prototype.hasOwnProperty.call(l,c)){if(c.charCodeAt(0)<256){for(n=0;n<f;n++)m<<=1,y==t-1?(y=0,b.push(r(m)),m=0):y++;for(o=c.charCodeAt(0),n=0;n<8;n++)m=m<<1|1&o,y==t-1?(y=0,b.push(r(m)),m=0):y++,o>>=1}else{for(o=1,n=0;n<f;n++)m=m<<1|o,y==t-1?(y=0,b.push(r(m)),m=0):y++,o=0;for(o=c.charCodeAt(0),n=0;n<16;n++)m=m<<1|1&o,y==t-1?(y=0,b.push(r(m)),m=0):y++,o>>=1}0==--d&&(d=Math.pow(2,f),f++),delete l[c]}else for(o=i[c],n=0;n<f;n++)m=m<<1|1&o,y==t-1?(y=0,b.push(r(m)),m=0):y++,o>>=1;0==--d&&(d=Math.pow(2,f),f++)}for(o=2,n=0;n<f;n++)m=m<<1|1&o,y==t-1?(y=0,b.push(r(m)),m=0):y++,o>>=1;for(;;){if(m<<=1,y==t-1){b.push(r(m));break}y++}return b.join("")},decompress:function(e){return null==e?"":""==e?null:a._decompress(e.length,32768,(function(t){return e.charCodeAt(t)}))},_decompress:function(t,r,n){var o,a,i,l,u,s,c,d=[],p=4,f=4,b=3,m="",y=[],v={val:n(0),position:r,index:1};for(o=0;o<3;o+=1)d[o]=o;for(i=0,u=Math.pow(2,2),s=1;s!=u;)l=v.val&v.position,v.position>>=1,0==v.position&&(v.position=r,v.val=n(v.index++)),i|=(l>0?1:0)*s,s<<=1;switch(i){case 0:for(i=0,u=Math.pow(2,8),s=1;s!=u;)l=v.val&v.position,v.position>>=1,0==v.position&&(v.position=r,v.val=n(v.index++)),i|=(l>0?1:0)*s,s<<=1;c=e(i);break;case 1:for(i=0,u=Math.pow(2,16),s=1;s!=u;)l=v.val&v.position,v.position>>=1,0==v.position&&(v.position=r,v.val=n(v.index++)),i|=(l>0?1:0)*s,s<<=1;c=e(i);break;case 2:return""}for(d[3]=c,a=c,y.push(c);;){if(v.index>t)return"";for(i=0,u=Math.pow(2,b),s=1;s!=u;)l=v.val&v.position,v.position>>=1,0==v.position&&(v.position=r,v.val=n(v.index++)),i|=(l>0?1:0)*s,s<<=1;switch(c=i){case 0:for(i=0,u=Math.pow(2,8),s=1;s!=u;)l=v.val&v.position,v.position>>=1,0==v.position&&(v.position=r,v.val=n(v.index++)),i|=(l>0?1:0)*s,s<<=1;d[f++]=e(i),c=f-1,p--;break;case 1:for(i=0,u=Math.pow(2,16),s=1;s!=u;)l=v.val&v.position,v.position>>=1,0==v.position&&(v.position=r,v.val=n(v.index++)),i|=(l>0?1:0)*s,s<<=1;d[f++]=e(i),c=f-1,p--;break;case 2:return y.join("")}if(0==p&&(p=Math.pow(2,b),b++),d[c])m=d[c];else{if(c!==f)return null;m=a+a.charAt(0)}y.push(m),d[f++]=a+m.charAt(0),a=m,0==--p&&(p=Math.pow(2,b),b++)}}};return a}();null!=e?e.exports=t:"undefined"!=typeof angular&&null!=angular&&angular.module("LZString",[]).factory("LZString",(function(){return t}))}(og);var ag=og.exports;function ig(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;")}const lg=(e,t,r,n,o,a,i)=>{const l=n+r.indent,u=r.colors;return e.map((e=>{const s=t[e];let c=i(s,r,l,o,a);return"string"!=typeof s&&(-1!==c.indexOf("\n")&&(c=r.spacingOuter+l+c+r.spacingOuter+n),c="{"+c+"}"),r.spacingInner+n+u.prop.open+e+u.prop.close+"="+u.value.open+c+u.value.close})).join("")},ug=(e,t,r,n,o,a)=>e.map((e=>{const i="string"==typeof e?sg(e,t):a(e,t,r,n,o);return""===i&&"object"==typeof e&&null!==e&&3!==e.nodeType?"":t.spacingOuter+r+i})).join(""),sg=(e,t)=>{const r=t.colors.content;return r.open+ig(e)+r.close},cg=(e,t)=>{const r=t.colors.comment;return r.open+"\x3c!--"+ig(e)+"--\x3e"+r.close},dg=(e,t,r,n,o)=>{const a=n.colors.tag;return a.open+"<"+e+(t&&a.close+t+n.spacingOuter+o+a.open)+(r?">"+a.close+r+n.spacingOuter+o+a.open+"</"+e:(t&&!n.min?"":" ")+"/")+">"+a.close},pg=(e,t)=>{const r=t.colors.tag;return r.open+"<"+e+r.close+" …"+r.open+" />"+r.close},fg=3,bg=8,mg=11,yg=/^((HTML|SVG)\w*)?Element$/,vg=e=>{const t=e.constructor.name,{nodeType:r,tagName:n}=e,o="string"==typeof n&&n.includes("-")||"function"==typeof e.hasAttribute&&e.hasAttribute("is");return 1===r&&(yg.test(t)||o)||r===fg&&"Text"===t||r===bg&&"Comment"===t||r===mg&&"DocumentFragment"===t};function hg(e){return e.nodeType===mg}function gg(e){return{test:e=>{var t;return(null==e||null==(t=e.constructor)?void 0:t.name)&&vg(e)},serialize:(t,r,n,o,a,i)=>{if(function(e){return e.nodeType===fg}(t))return sg(t.data,r);if(function(e){return e.nodeType===bg}(t))return cg(t.data,r);const l=hg(t)?"DocumentFragment":t.tagName.toLowerCase();return++o>r.maxDepth?pg(l,r):dg(l,lg(hg(t)?[]:Array.from(t.attributes).map((e=>e.name)).sort(),hg(t)?{}:Array.from(t.attributes).reduce(((e,t)=>(e[t.name]=t.value,e)),{}),r,n+r.indent,o,a,i),ug(Array.prototype.slice.call(t.childNodes||t.children).filter(e),r,n+r.indent,o,a,i),r,n)}}}let Pg=null,Cg=null,wg=null;try{const e=module&&module.require;Cg=e.call(module,"fs").readFileSync,wg=e.call(module,"@babel/code-frame").codeFrameColumns,Pg=e.call(module,"chalk")}catch{}function qg(){if(!Cg||!wg)return"";return function(e){const t=e.indexOf("(")+1,r=e.indexOf(")"),n=e.slice(t,r),o=n.split(":"),[a,i,l]=[o[0],parseInt(o[1],10),parseInt(o[2],10)];let u="";try{u=Cg(a,"utf-8")}catch{return""}const s=wg(u,{start:{line:i,column:l}},{highlightCode:!0,linesBelow:0});return Pg.dim(n)+"\n"+s+"\n"}((new Error).stack.split("\n").slice(1).find((e=>!e.includes("node_modules/"))))}const Eg=3;function xg(){return"undefined"!=typeof jest&&null!==jest&&(!0===setTimeout._isMockFunction||Object.prototype.hasOwnProperty.call(setTimeout,"clock"))}function Og(){if("undefined"==typeof window)throw new Error("Could not find default container");return window.document}function Rg(e){if(e.defaultView)return e.defaultView;if(e.ownerDocument&&e.ownerDocument.defaultView)return e.ownerDocument.defaultView;if(e.window)return e.window;throw e.ownerDocument&&null===e.ownerDocument.defaultView?new Error("It looks like the window object is not available for the provided node."):e.then instanceof Function?new Error("It looks like you passed a Promise object instead of a DOM node. Did you do something like `fireEvent.click(screen.findBy...` when you meant to use a `getBy` query `fireEvent.click(screen.getBy...`, or await the findBy query `fireEvent.click(await screen.findBy...`?"):Array.isArray(e)?new Error("It looks like you passed an Array instead of a DOM node. Did you do something like `fireEvent.click(screen.getAllBy...` when you meant to use a `getBy` query `fireEvent.click(screen.getBy...`?"):"function"==typeof e.debug&&"function"==typeof e.logTestingPlaygroundURL?new Error("It looks like you passed a `screen` object. Did you do something like `fireEvent.click(screen, ...` when you meant to use a query, e.g. `fireEvent.click(screen.getBy..., `?"):new Error("The given node is not an Element, the node type is: "+typeof e+".")}function jg(e){if(!e||"function"!=typeof e.querySelector||"function"!=typeof e.querySelectorAll)throw new TypeError("Expected container to be an Element, a Document or a DocumentFragment but got "+function(e){if("object"==typeof e)return null===e?"null":e.constructor.name;return typeof e}(e)+".")}const Sg=()=>{let e;try{var t;e=JSON.parse(null==(t=process)||null==(t=t.env)?void 0:t.COLORS)}catch(e){}return"boolean"==typeof e?e:"undefined"!=typeof process&&void 0!==process.versions&&void 0!==process.versions.node},{DOMCollection:Ag}=Fe,_g=1,Tg=8;function Mg(e){return e.nodeType!==Tg&&(e.nodeType!==_g||!e.matches(Ng().defaultIgnore))}function Ig(e,t,r){if(void 0===r&&(r={}),e||(e=Og().body),"number"!=typeof t&&(t=7e3),0===t)return"";e.documentElement&&(e=e.documentElement);let n=typeof e;if("object"===n?n=e.constructor.name:e={},!("outerHTML"in e))throw new TypeError("Expected an element or document but got "+n);const{filterNode:o=Mg,...a}=r,i=ke(e,{plugins:[gg(o),Ag],printFunctionName:!1,highlight:Sg(),...a});return void 0!==t&&e.outerHTML.length>t?i.slice(0,t)+"...":i}const Bg=function(){const e=qg();e?console.log(Ig(...arguments)+"\n\n"+e):console.log(Ig(...arguments))};let kg={testIdAttribute:"data-testid",asyncUtilTimeout:1e3,asyncWrapper:e=>e(),unstable_advanceTimersWrapper:e=>e(),eventWrapper:e=>e(),defaultHidden:!1,defaultIgnore:"script, style",showOriginalStackTrace:!1,throwSuggestions:!1,getElementError(e,t){const r=Ig(t),n=new Error([e,"Ignored nodes: comments, "+kg.defaultIgnore+"\n"+r].filter(Boolean).join("\n\n"));return n.name="TestingLibraryElementError",n},_disableExpensiveErrorDiagnostics:!1,computedStyleSupportsPseudoElements:!1};function Fg(e){"function"==typeof e&&(e=e(kg)),kg={...kg,...e}}function Ng(){return kg}const Lg=["button","meter","output","progress","select","textarea","input"];function Ug(e){return Lg.includes(e.nodeName.toLowerCase())?"":e.nodeType===Eg?e.textContent:Array.from(e.childNodes).map((e=>Ug(e))).join("")}function Dg(e){let t;return t="label"===e.tagName.toLowerCase()?Ug(e):e.value||e.textContent,t}function Hg(e){var t;if(void 0!==e.labels)return null!=(t=e.labels)?t:[];if(!function(e){return/BUTTON|METER|OUTPUT|PROGRESS|SELECT|TEXTAREA/.test(e.tagName)||"INPUT"===e.tagName&&"hidden"!==e.getAttribute("type")}(e))return[];const r=e.ownerDocument.querySelectorAll("label");return Array.from(r).filter((t=>t.control===e))}function $g(e,t,r){let{selector:n="*"}=void 0===r?{}:r;const o=t.getAttribute("aria-labelledby"),a=o?o.split(" "):[];return a.length?a.map((t=>{const r=e.querySelector('[id="'+t+'"]');return r?{content:Dg(r),formControl:null}:{content:"",formControl:null}})):Array.from(Hg(t)).map((e=>({content:Dg(e),formControl:Array.from(e.querySelectorAll("button, input, meter, output, progress, select, textarea")).filter((e=>e.matches(n)))[0]})))}function Wg(e){if(null==e)throw new Error("It looks like "+e+" was passed instead of a matcher. Did you do something like getByText("+e+")?")}function zg(e,t,r,n){if("string"!=typeof e)return!1;Wg(r);const o=n(e);return"string"==typeof r||"number"==typeof r?o.toLowerCase().includes(r.toString().toLowerCase()):"function"==typeof r?r(o,t):Qg(r,o)}function Vg(e,t,r,n){if("string"!=typeof e)return!1;Wg(r);const o=n(e);return r instanceof Function?r(o,t):r instanceof RegExp?Qg(r,o):o===String(r)}function Gg(e){let{trim:t=!0,collapseWhitespace:r=!0}=void 0===e?{}:e;return e=>{let n=e;return n=t?n.trim():n,n=r?n.replace(/\s+/g," "):n,n}}function Jg(e){let{trim:t,collapseWhitespace:r,normalizer:n}=e;if(!n)return Gg({trim:t,collapseWhitespace:r});if(void 0!==t||void 0!==r)throw new Error('trim and collapseWhitespace are not supported with a normalizer. If you want to use the default trim and collapseWhitespace logic in your normalizer, use "getDefaultNormalizer({trim, collapseWhitespace})" and compose that into your normalizer');return n}function Qg(e,t){const r=e.test(t);return e.global&&0!==e.lastIndex&&(console.warn("To match all elements we had to reset the lastIndex of the RegExp because the global flag is enabled. We encourage to remove the global flag from the RegExp."),e.lastIndex=0),r}function Xg(e){return e.matches("input[type=submit], input[type=button], input[type=reset]")?e.value:Array.from(e.childNodes).filter((e=>e.nodeType===Eg&&Boolean(e.textContent))).map((e=>e.textContent)).join("")}const Kg=function(e){function t(e){let{attributes:t=[]}=e;return t.length}function r(e){let{attributes:t=[]}=e;const r=t.findIndex((e=>e.value&&"type"===e.name&&"text"===e.value));r>=0&&(t=[...t.slice(0,r),...t.slice(r+1)]);const n=function(e){let{name:t,attributes:r}=e;return""+t+r.map((e=>{let{name:t,value:r,constraints:n=[]}=e;return-1!==n.indexOf("undefined")?":not(["+t+"])":r?"["+t+'="'+r+'"]':"["+t+"]"})).join("")}({...e,attributes:t});return e=>!(r>=0&&"text"!==e.type)&&e.matches(n)}let n=[];for(const[o,a]of e.entries())n=[...n,{match:r(o),roles:Array.from(a),specificity:t(o)}];return n.sort((function(e,t){let{specificity:r}=e,{specificity:n}=t;return n-r}))}(tg);function Yg(e){if(!0===e.hidden)return!0;if("true"===e.getAttribute("aria-hidden"))return!0;return"none"===e.ownerDocument.defaultView.getComputedStyle(e).display}function Zg(e,t){void 0===t&&(t={});const{isSubtreeInaccessible:r=Yg}=t;if("hidden"===e.ownerDocument.defaultView.getComputedStyle(e).visibility)return!0;let n=e;for(;n;){if(r(n))return!0;n=n.parentElement}return!1}function eP(e){for(const{match:t,roles:r}of Kg)if(t(e))return[...r];return[]}function tP(e,t){let{hidden:r=!1}=void 0===t?{}:t;return function e(t){return[t,...Array.from(t.children).reduce(((t,r)=>[...t,...e(r)]),[])]}(e).filter((e=>!1!==r||!1===Zg(e))).reduce(((e,t)=>{let r=[];return r=t.hasAttribute("role")?t.getAttribute("role").split(" ").slice(0,1):eP(t),r.reduce(((e,r)=>Array.isArray(e[r])?{...e,[r]:[...e[r],t]}:{...e,[r]:[t]}),e)}),{})}function rP(e,t){let{hidden:r,includeDescription:n}=t;const o=tP(e,{hidden:r});return Object.entries(o).filter((e=>{let[t]=e;return"generic"!==t})).map((e=>{let[t,r]=e;const o="-".repeat(50);return t+":\n\n"+r.map((e=>{const t='Name "'+ir(e,{computedStyleSupportsPseudoElements:Ng().computedStyleSupportsPseudoElements})+'":\n',r=Ig(e.cloneNode(!1));if(n){return""+t+('Description "'+ar(e,{computedStyleSupportsPseudoElements:Ng().computedStyleSupportsPseudoElements})+'":\n')+r}return""+t+r})).join("\n\n")+"\n\n"+o})).join("\n")}function nP(e,t){const r=e.getAttribute(t);return"true"===r||"false"!==r&&void 0}const oP=Gg();function aP(e){return new RegExp(function(e){return e.replace(/[.*+\-?^${}()|[\]\\]/g,"\\$&")}(e.toLowerCase()),"i")}function iP(e,t,r,n){let{variant:o,name:a}=n,i="";const l={},u=[["Role","TestId"].includes(e)?r:aP(r)];a&&(l.name=aP(a)),"Role"===e&&Zg(t)&&(l.hidden=!0,i="Element is inaccessible. This means that the element and all its children are invisible to screen readers.\n    If you are using the aria-hidden prop, make sure this is the right choice for your case.\n    "),Object.keys(l).length>0&&u.push(l);const s=o+"By"+e;return{queryName:e,queryMethod:s,queryArgs:u,variant:o,warning:i,toString(){i&&console.warn(i);let[e,t]=u;return e="string"==typeof e?"'"+e+"'":e,t=t?", { "+Object.entries(t).map((e=>{let[t,r]=e;return t+": "+r})).join(", ")+" }":"",s+"("+e+t+")"}}}function lP(e,t,r){return r&&(!t||t.toLowerCase()===e.toLowerCase())}function uP(e,t,r){var n,o;if(void 0===t&&(t="get"),e.matches(Ng().defaultIgnore))return;const a=null!=(n=e.getAttribute("role"))?n:null==(o=eP(e))?void 0:o[0];if("generic"!==a&&lP("Role",r,a))return iP("Role",e,a,{variant:t,name:ir(e,{computedStyleSupportsPseudoElements:Ng().computedStyleSupportsPseudoElements})});const i=$g(document,e).map((e=>e.content)).join(" ");if(lP("LabelText",r,i))return iP("LabelText",e,i,{variant:t});const l=e.getAttribute("placeholder");if(lP("PlaceholderText",r,l))return iP("PlaceholderText",e,l,{variant:t});const u=oP(Xg(e));if(lP("Text",r,u))return iP("Text",e,u,{variant:t});if(lP("DisplayValue",r,e.value))return iP("DisplayValue",e,oP(e.value),{variant:t});const s=e.getAttribute("alt");if(lP("AltText",r,s))return iP("AltText",e,s,{variant:t});const c=e.getAttribute("title");if(lP("Title",r,c))return iP("Title",e,c,{variant:t});const d=e.getAttribute(Ng().testIdAttribute);return lP("TestId",r,d)?iP("TestId",e,d,{variant:t}):void 0}function sP(e,t){e.stack=t.stack.replace(t.message,e.message)}function cP(e,t){let{container:r=Og(),timeout:n=Ng().asyncUtilTimeout,showOriginalStackTrace:o=Ng().showOriginalStackTrace,stackTraceError:a,interval:i=50,onTimeout:l=(e=>(Object.defineProperty(e,"message",{value:Ng().getElementError(e.message,r).message}),e)),mutationObserverOptions:u={subtree:!0,childList:!0,attributes:!0,characterData:!0}}=t;if("function"!=typeof e)throw new TypeError("Received `callback` arg must be a function");return new Promise((async(t,s)=>{let c,d,p,f=!1,b="idle";const m=setTimeout((function(){let e;c?(e=c,o||"TestingLibraryElementError"!==e.name||sP(e,a)):(e=new Error("Timed out in waitFor."),o||sP(e,a)),v(l(e),null)}),n),y=xg();if(y){const{unstable_advanceTimersWrapper:e}=Ng();for(g();!f;){if(!xg()){const e=new Error("Changed from using fake timers to real timers while using waitFor. This is not allowed and will result in very strange behavior. Please ensure you're awaiting all async things your test is doing before changing to real timers. For more info, please go to https://github.com/testing-library/dom-testing-library/issues/830");return o||sP(e,a),void s(e)}if(await e((async()=>{jest.advanceTimersByTime(i)})),f)break;g()}}else{try{jg(r)}catch(e){return void s(e)}d=setInterval(h,i);const{MutationObserver:e}=Rg(r);p=new e(h),p.observe(r,u),g()}function v(e,r){f=!0,clearTimeout(m),y||(clearInterval(d),p.disconnect()),e?s(e):t(r)}function h(){if(xg()){const e=new Error("Changed from using real timers to fake timers while using waitFor. This is not allowed and will result in very strange behavior. Please ensure you're awaiting all async things your test is doing before changing to fake timers. For more info, please go to https://github.com/testing-library/dom-testing-library/issues/830");return o||sP(e,a),s(e)}return g()}function g(){if("pending"!==b)try{const t=function(e){try{return kg._disableExpensiveErrorDiagnostics=!0,e()}finally{kg._disableExpensiveErrorDiagnostics=!1}}(e);"function"==typeof(null==t?void 0:t.then)?(b="pending",t.then((e=>{b="resolved",v(null,e)}),(e=>{b="rejected",c=e}))):v(null,t)}catch(e){c=e}}}))}function dP(e,t){const r=new Error("STACK_TRACE_MESSAGE");return Ng().asyncWrapper((()=>cP(e,{stackTraceError:r,...t})))}function pP(e,t){return Ng().getElementError(e,t)}function fP(e,t){return pP(e+"\n\n(If this is intentional, then use the `*AllBy*` variant of the query (like `queryAllByText`, `getAllByText`, or `findAllByText`)).",t)}function bP(e,t,r,n){let{exact:o=!0,collapseWhitespace:a,trim:i,normalizer:l}=void 0===n?{}:n;const u=o?Vg:zg,s=Jg({collapseWhitespace:a,trim:i,normalizer:l});return Array.from(t.querySelectorAll("["+e+"]")).filter((t=>u(t.getAttribute(e),t,r,s)))}function mP(e,t,r,n){const o=bP(e,t,r,n);if(o.length>1)throw fP("Found multiple elements by ["+e+"="+r+"]",t);return o[0]||null}function yP(e,t){return function(r){for(var n=arguments.length,o=new Array(n>1?n-1:0),a=1;a<n;a++)o[a-1]=arguments[a];const i=e(r,...o);if(i.length>1){const e=i.map((e=>pP(null,e).message)).join("\n\n");throw fP(t(r,...o)+"\n\nHere are the matching elements:\n\n"+e,r)}return i[0]||null}}function vP(e,t){return Ng().getElementError("A better query is available, try this:\n"+e.toString()+"\n",t)}function hP(e,t){return function(r){for(var n=arguments.length,o=new Array(n>1?n-1:0),a=1;a<n;a++)o[a-1]=arguments[a];const i=e(r,...o);if(!i.length)throw Ng().getElementError(t(r,...o),r);return i}}function gP(e){return(t,r,n,o)=>dP((()=>e(t,r,n)),{container:t,...o})}const PP=(e,t,r)=>function(n){for(var o=arguments.length,a=new Array(o>1?o-1:0),i=1;i<o;i++)a[i-1]=arguments[i];const l=e(n,...a),[{suggest:u=Ng().throwSuggestions}={}]=a.slice(-1);if(l&&u){const e=uP(l,r);if(e&&!t.endsWith(e.queryName))throw vP(e.toString(),n)}return l},CP=(e,t,r)=>function(n){for(var o=arguments.length,a=new Array(o>1?o-1:0),i=1;i<o;i++)a[i-1]=arguments[i];const l=e(n,...a),[{suggest:u=Ng().throwSuggestions}={}]=a.slice(-1);if(l.length&&u){const e=[...new Set(l.map((e=>{var t;return null==(t=uP(e,r))?void 0:t.toString()})))];if(1===e.length&&!t.endsWith(uP(l[0],r).queryName))throw vP(e[0],n)}return l};function wP(e,t,r){const n=PP(yP(e,t),e.name,"query"),o=hP(e,r),a=yP(o,t),i=PP(a,e.name,"get");return[n,CP(o,e.name.replace("query","get"),"getAll"),i,gP(CP(o,e.name,"findAll")),gP(PP(a,e.name,"find"))]}var qP=Object.freeze({__proto__:null,getElementError:pP,wrapAllByQueryWithSuggestion:CP,wrapSingleQueryWithSuggestion:PP,getMultipleElementsFoundError:fP,queryAllByAttribute:bP,queryByAttribute:mP,makeSingleQuery:yP,makeGetAllQuery:hP,makeFindQuery:gP,buildQueries:wP});const EP=function(e,t,r){let{exact:n=!0,trim:o,collapseWhitespace:a,normalizer:i}=void 0===r?{}:r;const l=n?Vg:zg,u=Jg({collapseWhitespace:a,trim:o,normalizer:i}),s=function(e){return Array.from(e.querySelectorAll("label,input")).map((e=>({node:e,textToMatch:Dg(e)}))).filter((e=>{let{textToMatch:t}=e;return null!==t}))}(e);return s.filter((e=>{let{node:r,textToMatch:n}=e;return l(n,r,t,u)})).map((e=>{let{node:t}=e;return t}))},xP=function(e,t,r){let{selector:n="*",exact:o=!0,collapseWhitespace:a,trim:i,normalizer:l}=void 0===r?{}:r;jg(e);const u=o?Vg:zg,s=Jg({collapseWhitespace:a,trim:i,normalizer:l}),c=Array.from(e.querySelectorAll("*")).filter((e=>Hg(e).length||e.hasAttribute("aria-labelledby"))).reduce(((r,o)=>{const a=$g(e,o,{selector:n});a.filter((e=>Boolean(e.formControl))).forEach((e=>{u(e.content,e.formControl,t,s)&&e.formControl&&r.push(e.formControl)}));const i=a.filter((e=>Boolean(e.content))).map((e=>e.content));return u(i.join(" "),o,t,s)&&r.push(o),i.length>1&&i.forEach(((e,n)=>{u(e,o,t,s)&&r.push(o);const a=[...i];a.splice(n,1),a.length>1&&u(a.join(" "),o,t,s)&&r.push(o)})),r}),[]).concat(bP("aria-label",e,t,{exact:o,normalizer:s}));return Array.from(new Set(c)).filter((e=>e.matches(n)))},OP=function(e,t){for(var r=arguments.length,n=new Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o];const a=xP(e,t,...n);if(!a.length){const r=EP(e,t,...n);if(r.length){const n=r.map((t=>function(e,t){const r=t.getAttribute("for");if(!r)return null;const n=e.querySelector('[id="'+r+'"]');return n?n.tagName.toLowerCase():null}(e,t))).filter((e=>!!e));throw n.length?Ng().getElementError(n.map((e=>"Found a label with the text of: "+t+", however the element associated with this label (<"+e+" />) is non-labellable [https://html.spec.whatwg.org/multipage/forms.html#category-label]. If you really need to label a <"+e+" />, you can use aria-label or aria-labelledby instead.")).join("\n\n"),e):Ng().getElementError("Found a label with the text of: "+t+', however no form control was found associated to that label. Make sure you\'re using the "for" attribute or "aria-labelledby" attribute correctly.',e)}throw Ng().getElementError("Unable to find a label with the text of: "+t,e)}return a};const RP=(e,t)=>"Found multiple elements with the text of: "+t,jP=PP(yP(xP,RP),xP.name,"query"),SP=yP(OP,RP),AP=gP(CP(OP,OP.name,"findAll")),_P=gP(PP(SP,OP.name,"find")),TP=CP(OP,OP.name,"getAll"),MP=PP(SP,OP.name,"get"),IP=CP(xP,xP.name,"queryAll"),BP=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return jg(t[0]),bP("placeholder",...t)},kP=CP(BP,BP.name,"queryAll"),[FP,NP,LP,UP,DP]=wP(BP,((e,t)=>"Found multiple elements with the placeholder text of: "+t),((e,t)=>"Unable to find an element with the placeholder text of: "+t)),HP=function(e,t,r){let{selector:n="*",exact:o=!0,collapseWhitespace:a,trim:i,ignore:l=Ng().defaultIgnore,normalizer:u}=void 0===r?{}:r;jg(e);const s=o?Vg:zg,c=Jg({collapseWhitespace:a,trim:i,normalizer:u});let d=[];return"function"==typeof e.matches&&e.matches(n)&&(d=[e]),[...d,...Array.from(e.querySelectorAll(n))].filter((e=>!l||!e.matches(l))).filter((e=>s(Xg(e),e,t,c)))},$P=CP(HP,HP.name,"queryAll"),[WP,zP,VP,GP,JP]=wP(HP,((e,t)=>"Found multiple elements with the text: "+t),(function(e,t,r){void 0===r&&(r={});const{collapseWhitespace:n,trim:o,normalizer:a,selector:i}=r,l=Jg({collapseWhitespace:n,trim:o,normalizer:a})(t.toString());return"Unable to find an element with the text: "+(l!==t.toString()?l+" (normalized from '"+t+"')":t)+("*"!==(null!=i?i:"*")?", which matches selector '"+i+"'":"")+". This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible."})),QP=function(e,t,r){let{exact:n=!0,collapseWhitespace:o,trim:a,normalizer:i}=void 0===r?{}:r;jg(e);const l=n?Vg:zg,u=Jg({collapseWhitespace:o,trim:a,normalizer:i});return Array.from(e.querySelectorAll("input,textarea,select")).filter((e=>{if("SELECT"===e.tagName){return Array.from(e.options).filter((e=>e.selected)).some((e=>l(Xg(e),e,t,u)))}return l(e.value,e,t,u)}))},XP=CP(QP,QP.name,"queryAll"),[KP,YP,ZP,eC,tC]=wP(QP,((e,t)=>"Found multiple elements with the display value: "+t+"."),((e,t)=>"Unable to find an element with the display value: "+t+".")),rC=/^(img|input|area|.+-.+)$/i,nC=function(e,t,r){return void 0===r&&(r={}),jg(e),bP("alt",e,t,r).filter((e=>rC.test(e.tagName)))},oC=CP(nC,nC.name,"queryAll"),[aC,iC,lC,uC,sC]=wP(nC,((e,t)=>"Found multiple elements with the alt text: "+t),((e,t)=>"Unable to find an element with the alt text: "+t)),cC=function(e,t,r){let{exact:n=!0,collapseWhitespace:o,trim:a,normalizer:i}=void 0===r?{}:r;jg(e);const l=n?Vg:zg,u=Jg({collapseWhitespace:o,trim:a,normalizer:i});return Array.from(e.querySelectorAll("[title], svg > title")).filter((e=>l(e.getAttribute("title"),e,t,u)||(e=>{var t;return"title"===e.tagName.toLowerCase()&&"svg"===(null==(t=e.parentElement)?void 0:t.tagName.toLowerCase())})(e)&&l(Xg(e),e,t,u)))},dC=CP(cC,cC.name,"queryAll"),[pC,fC,bC,mC,yC]=wP(cC,((e,t)=>"Found multiple elements with the title: "+t+"."),((e,t)=>"Unable to find an element with the title: "+t+".")),vC=function(e,t,r){let{hidden:n=Ng().defaultHidden,name:o,description:a,queryFallbacks:i=!1,selected:l,busy:u,checked:s,pressed:c,current:d,level:p,expanded:f,value:{now:b,min:m,max:y,text:v}={}}=void 0===r?{}:r;var h,g,P,C,w,q,E,x,O,R;if((jg(e),void 0!==l)&&void 0===(null==(h=Wh.get(t))?void 0:h.props["aria-selected"]))throw new Error('"aria-selected" is not supported on role "'+t+'".');if(void 0!==u&&void 0===(null==(g=Wh.get(t))?void 0:g.props["aria-busy"]))throw new Error('"aria-busy" is not supported on role "'+t+'".');if(void 0!==s&&void 0===(null==(P=Wh.get(t))?void 0:P.props["aria-checked"]))throw new Error('"aria-checked" is not supported on role "'+t+'".');if(void 0!==c&&void 0===(null==(C=Wh.get(t))?void 0:C.props["aria-pressed"]))throw new Error('"aria-pressed" is not supported on role "'+t+'".');if(void 0!==d&&void 0===(null==(w=Wh.get(t))?void 0:w.props["aria-current"]))throw new Error('"aria-current" is not supported on role "'+t+'".');if(void 0!==p&&"heading"!==t)throw new Error('Role "'+t+'" cannot have "level" property.');if(void 0!==b&&void 0===(null==(q=Wh.get(t))?void 0:q.props["aria-valuenow"]))throw new Error('"aria-valuenow" is not supported on role "'+t+'".');if(void 0!==y&&void 0===(null==(E=Wh.get(t))?void 0:E.props["aria-valuemax"]))throw new Error('"aria-valuemax" is not supported on role "'+t+'".');if(void 0!==m&&void 0===(null==(x=Wh.get(t))?void 0:x.props["aria-valuemin"]))throw new Error('"aria-valuemin" is not supported on role "'+t+'".');if(void 0!==v&&void 0===(null==(O=Wh.get(t))?void 0:O.props["aria-valuetext"]))throw new Error('"aria-valuetext" is not supported on role "'+t+'".');if(void 0!==f&&void 0===(null==(R=Wh.get(t))?void 0:R.props["aria-expanded"]))throw new Error('"aria-expanded" is not supported on role "'+t+'".');const j=new WeakMap;function S(e){return j.has(e)||j.set(e,Yg(e)),j.get(e)}return Array.from(e.querySelectorAll(function(e){var t;const r='*[role~="'+e+'"]',n=null!=(t=ng.get(e))?t:new Set,o=new Set(Array.from(n).map((e=>{let{name:t}=e;return t})));return[r].concat(Array.from(o)).join(",")}(t))).filter((e=>{if(e.hasAttribute("role")){const r=e.getAttribute("role");if(i)return r.split(" ").filter(Boolean).some((e=>e===t));const[n]=r.split(" ");return n===t}return eP(e).some((e=>e===t))})).filter((e=>{if(void 0!==l)return l===function(e){return"OPTION"===e.tagName?e.selected:nP(e,"aria-selected")}(e);if(void 0!==u)return u===function(e){return"true"===e.getAttribute("aria-busy")}(e);if(void 0!==s)return s===function(e){if(!("indeterminate"in e)||!e.indeterminate)return"checked"in e?e.checked:nP(e,"aria-checked")}(e);if(void 0!==c)return c===function(e){return nP(e,"aria-pressed")}(e);if(void 0!==d)return d===function(e){var t,r;return null!=(t=null!=(r=nP(e,"aria-current"))?r:e.getAttribute("aria-current"))&&t}(e);if(void 0!==f)return f===function(e){return nP(e,"aria-expanded")}(e);if(void 0!==p)return p===function(e){return e.getAttribute("aria-level")&&Number(e.getAttribute("aria-level"))||{H1:1,H2:2,H3:3,H4:4,H5:5,H6:6}[e.tagName]}(e);if(void 0!==b||void 0!==y||void 0!==m||void 0!==v){let r=!0;var t;if(void 0!==b&&r&&(r=b===function(e){const t=e.getAttribute("aria-valuenow");return null===t?void 0:+t}(e)),void 0!==y&&r&&(r=y===function(e){const t=e.getAttribute("aria-valuemax");return null===t?void 0:+t}(e)),void 0!==m&&r&&(r=m===function(e){const t=e.getAttribute("aria-valuemin");return null===t?void 0:+t}(e)),void 0!==v)r&&(r=Vg(null!=(t=function(e){const t=e.getAttribute("aria-valuetext");return null===t?void 0:t}(e))?t:null,e,v,(e=>e)));return r}return!0})).filter((e=>void 0===o||Vg(ir(e,{computedStyleSupportsPseudoElements:Ng().computedStyleSupportsPseudoElements}),e,o,(e=>e)))).filter((e=>void 0===a||Vg(ar(e,{computedStyleSupportsPseudoElements:Ng().computedStyleSupportsPseudoElements}),e,a,(e=>e)))).filter((e=>!1!==n||!1===Zg(e,{isSubtreeInaccessible:S})))};const hC=e=>{let t="";return t=void 0===e?"":"string"==typeof e?' and name "'+e+'"':" and name `"+e+"`",t},gC=CP(vC,vC.name,"queryAll"),[PC,CC,wC,qC,EC]=wP(vC,(function(e,t,r){let{name:n}=void 0===r?{}:r;return'Found multiple elements with the role "'+t+'"'+hC(n)}),(function(e,t,r){let{hidden:n=Ng().defaultHidden,name:o,description:a}=void 0===r?{}:r;if(Ng()._disableExpensiveErrorDiagnostics)return'Unable to find role="'+t+'"'+hC(o);let i,l="";Array.from(e.children).forEach((e=>{l+=rP(e,{hidden:n,includeDescription:void 0!==a})})),i=0===l.length?!1===n?"There are no accessible roles. But there might be some inaccessible roles. If you wish to access them, then set the `hidden` option to `true`. Learn more about this here: https://testing-library.com/docs/dom-testing-library/api-queries#byrole":"There are no available roles.":("\nHere are the "+(!1===n?"accessible":"available")+" roles:\n\n  "+l.replace(/\n/g,"\n  ").replace(/\n\s\s\n/g,"\n\n")+"\n").trim();let u="";u=void 0===o?"":"string"==typeof o?' and name "'+o+'"':" and name `"+o+"`";let s="";return s=void 0===a?"":"string"==typeof a?' and description "'+a+'"':" and description `"+a+"`",("\nUnable to find an "+(!1===n?"accessible ":"")+'element with the role "'+t+'"'+u+s+"\n\n"+i).trim()})),xC=()=>Ng().testIdAttribute,OC=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return jg(t[0]),bP(xC(),...t)},RC=CP(OC,OC.name,"queryAll"),[jC,SC,AC,_C,TC]=wP(OC,((e,t)=>"Found multiple elements by: ["+xC()+'="'+t+'"]'),((e,t)=>"Unable to find an element by: ["+xC()+'="'+t+'"]'));var MC=Object.freeze({__proto__:null,queryAllByLabelText:IP,queryByLabelText:jP,getAllByLabelText:TP,getByLabelText:MP,findAllByLabelText:AP,findByLabelText:_P,queryByPlaceholderText:FP,queryAllByPlaceholderText:kP,getByPlaceholderText:LP,getAllByPlaceholderText:NP,findAllByPlaceholderText:UP,findByPlaceholderText:DP,queryByText:WP,queryAllByText:$P,getByText:VP,getAllByText:zP,findAllByText:GP,findByText:JP,queryByDisplayValue:KP,queryAllByDisplayValue:XP,getByDisplayValue:ZP,getAllByDisplayValue:YP,findAllByDisplayValue:eC,findByDisplayValue:tC,queryByAltText:aC,queryAllByAltText:oC,getByAltText:lC,getAllByAltText:iC,findAllByAltText:uC,findByAltText:sC,queryByTitle:pC,queryAllByTitle:dC,getByTitle:bC,getAllByTitle:fC,findAllByTitle:mC,findByTitle:yC,queryByRole:PC,queryAllByRole:gC,getAllByRole:CC,getByRole:wC,findAllByRole:qC,findByRole:EC,queryByTestId:jC,queryAllByTestId:RC,getByTestId:AC,getAllByTestId:SC,findAllByTestId:_C,findByTestId:TC});function IC(e,t,r){return void 0===t&&(t=MC),void 0===r&&(r={}),Object.keys(t).reduce(((r,n)=>{const o=t[n];return r[n]=o.bind(null,e),r}),r)}const BC=e=>!e||Array.isArray(e)&&!e.length;function kC(e){if(BC(e))throw new Error("The element(s) given to waitForElementToBeRemoved are already removed. waitForElementToBeRemoved requires that the element(s) exist(s) before waiting for removal.")}const FC={copy:{EventType:"ClipboardEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},cut:{EventType:"ClipboardEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},paste:{EventType:"ClipboardEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},compositionEnd:{EventType:"CompositionEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},compositionStart:{EventType:"CompositionEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},compositionUpdate:{EventType:"CompositionEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},keyDown:{EventType:"KeyboardEvent",defaultInit:{bubbles:!0,cancelable:!0,charCode:0,composed:!0}},keyPress:{EventType:"KeyboardEvent",defaultInit:{bubbles:!0,cancelable:!0,charCode:0,composed:!0}},keyUp:{EventType:"KeyboardEvent",defaultInit:{bubbles:!0,cancelable:!0,charCode:0,composed:!0}},focus:{EventType:"FocusEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},blur:{EventType:"FocusEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},focusIn:{EventType:"FocusEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},focusOut:{EventType:"FocusEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},change:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!1}},input:{EventType:"InputEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},invalid:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!0}},submit:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!0}},reset:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!0}},click:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,button:0,composed:!0}},contextMenu:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dblClick:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},drag:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dragEnd:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},dragEnter:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dragExit:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},dragLeave:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},dragOver:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dragStart:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},drop:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseDown:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseEnter:{EventType:"MouseEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},mouseLeave:{EventType:"MouseEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},mouseMove:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseOut:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseOver:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseUp:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},select:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!1}},touchCancel:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},touchEnd:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},touchMove:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},touchStart:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},resize:{EventType:"UIEvent",defaultInit:{bubbles:!1,cancelable:!1}},scroll:{EventType:"UIEvent",defaultInit:{bubbles:!1,cancelable:!1}},wheel:{EventType:"WheelEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},abort:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},canPlay:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},canPlayThrough:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},durationChange:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},emptied:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},encrypted:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},ended:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},loadedData:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},loadedMetadata:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},loadStart:{EventType:"ProgressEvent",defaultInit:{bubbles:!1,cancelable:!1}},pause:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},play:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},playing:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},progress:{EventType:"ProgressEvent",defaultInit:{bubbles:!1,cancelable:!1}},rateChange:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},seeked:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},seeking:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},stalled:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},suspend:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},timeUpdate:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},volumeChange:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},waiting:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},load:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},error:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},animationStart:{EventType:"AnimationEvent",defaultInit:{bubbles:!0,cancelable:!1}},animationEnd:{EventType:"AnimationEvent",defaultInit:{bubbles:!0,cancelable:!1}},animationIteration:{EventType:"AnimationEvent",defaultInit:{bubbles:!0,cancelable:!1}},transitionCancel:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!1}},transitionEnd:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!0}},transitionRun:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!1}},transitionStart:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!1}},pointerOver:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerEnter:{EventType:"PointerEvent",defaultInit:{bubbles:!1,cancelable:!1}},pointerDown:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerMove:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerUp:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerCancel:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},pointerOut:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerLeave:{EventType:"PointerEvent",defaultInit:{bubbles:!1,cancelable:!1}},gotPointerCapture:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},lostPointerCapture:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},popState:{EventType:"PopStateEvent",defaultInit:{bubbles:!0,cancelable:!1}},offline:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},online:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}}},NC={doubleClick:"dblClick"};function LC(e,t){return Ng().eventWrapper((()=>{if(!t)throw new Error("Unable to fire an event - please provide an event object.");if(!e)throw new Error('Unable to fire a "'+t.type+'" event - please provide a DOM element.');return e.dispatchEvent(t)}))}function UC(e,t,r,n){let{EventType:o="Event",defaultInit:a={}}=void 0===n?{}:n;if(!t)throw new Error('Unable to fire a "'+e+'" event - please provide a DOM element.');const i={...a,...r},{target:{value:l,files:u,...s}={}}=i;void 0!==l&&function(e,t){const{set:r}=Object.getOwnPropertyDescriptor(e,"value")||{},n=Object.getPrototypeOf(e),{set:o}=Object.getOwnPropertyDescriptor(n,"value")||{};if(o&&r!==o)o.call(e,t);else{if(!r)throw new Error("The given element does not have a value setter");r.call(e,t)}}(t,l),void 0!==u&&Object.defineProperty(t,"files",{configurable:!0,enumerable:!0,writable:!0,value:u}),Object.assign(t,s);const c=Rg(t),d=c[o]||c.Event;let p;if("function"==typeof d)p=new d(e,i);else{p=c.document.createEvent(o);const{bubbles:t,cancelable:r,detail:n,...a}=i;p.initEvent(e,t,r,n),Object.keys(a).forEach((e=>{p[e]=a[e]}))}return["dataTransfer","clipboardData"].forEach((e=>{const t=i[e];"object"==typeof t&&("function"==typeof c.DataTransfer?Object.defineProperty(p,e,{value:Object.getOwnPropertyNames(t).reduce(((e,r)=>(Object.defineProperty(e,r,{value:t[r]}),e)),new c.DataTransfer)}):Object.defineProperty(p,e,{value:t}))})),p}function DC(e){return"https://testing-playground.com/#markup="+(t=e,ag.compressToEncodedURIComponent(t.replace(/[ \t]*[\n][ \t]*/g,"\n")));var t}Object.keys(FC).forEach((e=>{const{EventType:t,defaultInit:r}=FC[e],n=e.toLowerCase();UC[e]=(e,o)=>UC(n,e,o,{EventType:t,defaultInit:r}),LC[e]=(t,r)=>LC(t,UC[e](t,r))})),Object.keys(NC).forEach((e=>{const t=NC[e];LC[e]=function(){return LC[t](...arguments)}}));const HC={debug:(e,t,r)=>Array.isArray(e)?e.forEach((e=>Bg(e,t,r))):Bg(e,t,r),logTestingPlaygroundURL:function(e){if(void 0===e&&(e=Og().body),!e||!("innerHTML"in e))return void console.log("The element you're providing isn't a valid DOM element.");if(!e.innerHTML)return void console.log("The provided element doesn't have any children.");const t=DC(e.innerHTML);return console.log("Open this URL in your browser\n\n"+t),t}},$C="undefined"!=typeof document&&document.body?IC(document.body,MC,HC):Object.keys(MC).reduce(((e,t)=>(e[t]=()=>{throw new TypeError("For queries bound to document.body a global document has to be available... Learn more: https://testing-library.com/s/screen-global-error")},e)),HC),WC=function(){return LC(...arguments)};Object.keys(LC).forEach((e=>{WC[e]=function(){return LC[e](...arguments)}}));const zC=WC.mouseEnter,VC=WC.mouseLeave;WC.mouseEnter=function(){return zC(...arguments),WC.mouseOver(...arguments)},WC.mouseLeave=function(){return VC(...arguments),WC.mouseOut(...arguments)};const GC=WC.pointerEnter,JC=WC.pointerLeave;WC.pointerEnter=function(){return GC(...arguments),WC.pointerOver(...arguments)},WC.pointerLeave=function(){return JC(...arguments),WC.pointerOut(...arguments)};const QC=WC.select;WC.select=(e,t)=>{QC(e,t),e.focus(),WC.keyUp(e,t)};const XC=WC.blur,KC=WC.focus;WC.blur=function(){return WC.focusOut(...arguments),XC(...arguments)},WC.focus=function(){return WC.focusIn(...arguments),KC(...arguments)};let YC={reactStrictMode:!1};function ZC(){return{...Ng(),...YC}}Fg({unstable_advanceTimersWrapper:e=>y(e),asyncWrapper:async e=>{const t=m();b(!1);try{const t=await e();return await new Promise((e=>{setTimeout((()=>{e()}),0),"undefined"==typeof jest||null===jest||!0!==setTimeout._isMockFunction&&!Object.prototype.hasOwnProperty.call(setTimeout,"clock")||jest.advanceTimersByTime(0)})),t}finally{b(t)}},eventWrapper:e=>{let t;return y((()=>{t=e()})),t}});const ew=new Set,tw=[];function rw(e){return ZC().reactStrictMode?u.createElement(u.StrictMode,null,e):e}function nw(e,t){return t?u.createElement(t,null,e):e}function ow(e,t){let r,{hydrate:n,ui:o,wrapper:a}=t;return n?y((()=>{r=d.hydrateRoot(e,rw(nw(o,a)))})):r=d.createRoot(e),{hydrate(){if(!n)throw new Error("Attempted to hydrate a non-hydrateable root. This is a bug in `@testing-library/react`.")},render(e){r.render(e)},unmount(){r.unmount()}}}function aw(e){return{hydrate(t){c.default.hydrate(t,e)},render(t){c.default.render(t,e)},unmount(){c.default.unmountComponentAtNode(e)}}}function iw(e,t){let{baseElement:r,container:n,hydrate:o,queries:a,root:i,wrapper:l}=t;return y((()=>{o?i.hydrate(rw(nw(e,l)),n):i.render(rw(nw(e,l)),n)})),{container:n,baseElement:r,debug:function(e,t,n){return void 0===e&&(e=r),Array.isArray(e)?e.forEach((e=>console.log(Ig(e,t,n)))):console.log(Ig(e,t,n))},unmount:()=>{y((()=>{i.unmount()}))},rerender:e=>{iw(e,{container:n,baseElement:r,root:i,wrapper:l})},asFragment:()=>{if("function"==typeof document.createRange)return document.createRange().createContextualFragment(n.innerHTML);{const e=document.createElement("template");return e.innerHTML=n.innerHTML,e.content}},...IC(r,a)}}function lw(e,t){let r,{container:n,baseElement:o=n,legacyRoot:a=!1,queries:i,hydrate:l=!1,wrapper:u}=void 0===t?{}:t;if(a&&"function"!=typeof c.default.render){const e=new Error("`legacyRoot: true` is not supported in this version of React. Please use React 18 instead.");throw Error.captureStackTrace(e,lw),e}if(o||(o=document.body),n||(n=o.appendChild(document.createElement("div"))),ew.has(n))tw.forEach((e=>{e.container===n&&(r=e.root)}));else{r=(a?aw:ow)(n,{hydrate:l,ui:e,wrapper:u}),tw.push({container:n,root:r}),ew.add(n)}return iw(e,{container:n,baseElement:o,queries:i,hydrate:l,wrapper:u,root:r})}function uw(){tw.forEach((e=>{let{root:t,container:r}=e;y((()=>{t.unmount()})),r.parentNode===document.body&&document.body.removeChild(r)})),tw.length=0,ew.clear()}if(("undefined"==typeof process||!process.env?.RTL_SKIP_AUTO_CLEANUP)&&("function"==typeof afterEach?afterEach((()=>{uw()})):"function"==typeof teardown&&teardown((()=>{uw()})),"function"==typeof beforeAll&&"function"==typeof afterAll)){let e=m();beforeAll((()=>{e=m(),b(!0)})),afterAll((()=>{b(e)}))}e.act=y,e.buildQueries=wP,e.cleanup=uw,e.configure=function(e){"function"==typeof e&&(e=e(ZC()));const{reactStrictMode:t,...r}=e;Fg(r),YC={...YC,reactStrictMode:t}},e.createEvent=UC,e.findAllByAltText=uC,e.findAllByDisplayValue=eC,e.findAllByLabelText=AP,e.findAllByPlaceholderText=UP,e.findAllByRole=qC,e.findAllByTestId=_C,e.findAllByText=GP,e.findAllByTitle=mC,e.findByAltText=sC,e.findByDisplayValue=tC,e.findByLabelText=_P,e.findByPlaceholderText=DP,e.findByRole=EC,e.findByTestId=TC,e.findByText=JP,e.findByTitle=yC,e.fireEvent=WC,e.getAllByAltText=iC,e.getAllByDisplayValue=YP,e.getAllByLabelText=TP,e.getAllByPlaceholderText=NP,e.getAllByRole=CC,e.getAllByTestId=SC,e.getAllByText=zP,e.getAllByTitle=fC,e.getByAltText=lC,e.getByDisplayValue=ZP,e.getByLabelText=MP,e.getByPlaceholderText=LP,e.getByRole=wC,e.getByTestId=AC,e.getByText=VP,e.getByTitle=bC,e.getConfig=ZC,e.getDefaultNormalizer=Gg,e.getElementError=pP,e.getMultipleElementsFoundError=fP,e.getNodeText=Xg,e.getQueriesForElement=IC,e.getRoles=tP,e.getSuggestedQuery=uP,e.isInaccessible=Zg,e.logDOM=Bg,e.logRoles=function(e,t){let{hidden:r=!1}=void 0===t?{}:t;return console.log(rP(e,{hidden:r}))},e.makeFindQuery=gP,e.makeGetAllQuery=hP,e.makeSingleQuery=yP,e.prettyDOM=Ig,e.prettyFormat=qt,e.queries=MC,e.queryAllByAltText=oC,e.queryAllByAttribute=bP,e.queryAllByDisplayValue=XP,e.queryAllByLabelText=IP,e.queryAllByPlaceholderText=kP,e.queryAllByRole=gC,e.queryAllByTestId=RC,e.queryAllByText=$P,e.queryAllByTitle=dC,e.queryByAltText=aC,e.queryByAttribute=mP,e.queryByDisplayValue=KP,e.queryByLabelText=jP,e.queryByPlaceholderText=FP,e.queryByRole=PC,e.queryByTestId=jC,e.queryByText=WP,e.queryByTitle=pC,e.queryHelpers=qP,e.render=lw,e.renderHook=function e(t,r){void 0===r&&(r={});const{initialProps:n,...o}=r;if(o.legacyRoot&&"function"!=typeof c.default.render){const t=new Error("`legacyRoot: true` is not supported in this version of React. Please use React 18 instead.");throw Error.captureStackTrace(t,e),t}const a=u.createRef();function i(e){let{renderCallbackProps:r}=e;const n=t(r);return u.useEffect((()=>{a.current=n})),null}const{rerender:l,unmount:s}=lw(u.createElement(i,{renderCallbackProps:n}),o);return{result:a,rerender:function(e){return l(u.createElement(i,{renderCallbackProps:e}))},unmount:s}},e.screen=$C,e.waitFor=dP,e.waitForElementToBeRemoved=async function(e,t){const r=new Error("Timed out in waitForElementToBeRemoved.");if("function"!=typeof e){kC(e);const t=(Array.isArray(e)?e:[e]).map((e=>{let t=e.parentElement;if(null===t)return()=>null;for(;t.parentElement;)t=t.parentElement;return()=>t.contains(e)?e:null}));e=()=>t.map((e=>e())).filter(Boolean)}return kC(e()),dP((()=>{let t;try{t=e()}catch(e){if("TestingLibraryElementError"===e.name)return;throw e}if(!BC(t))throw r}),t)},e.within=IC,e.wrapAllByQueryWithSuggestion=CP,e.wrapSingleQueryWithSuggestion=PP,Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=react.umd.min.js.map
