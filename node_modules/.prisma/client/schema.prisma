// QueryCraft Studio Database Schema
// This schema defines the complete data model for the application

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// User management
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String
  avatar    String?
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  databaseConnections DatabaseConnection[]
  querySessions       QuerySession[]
  generatedQueries    GeneratedQuery[]
  teamMemberships     TeamMembership[]
  apiKeys             ApiKey[]

  @@map("users")
}

// Team management for collaboration
model Team {
  id          String   @id @default(cuid())
  name        String
  description String?
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  members     TeamMembership[]
  connections DatabaseConnection[]

  @@map("teams")
}

model TeamMembership {
  id     String @id @default(cuid())
  userId String @map("user_id")
  teamId String @map("team_id")
  role   String @default("MEMBER")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  team Team @relation(fields: [teamId], references: [id], onDelete: Cascade)

  @@unique([userId, teamId])
  @@map("team_memberships")
}

// TeamRole as String instead of enum for SQLite compatibility
// Values: OWNER, ADMIN, MEMBER, VIEWER

// Database connections
model DatabaseConnection {
  id                   String    @id @default(cuid())
  userId               String?   @map("user_id")
  teamId               String?   @map("team_id")
  name                 String
  databaseType         String    @map("database_type")
  connectionStringHash String    @map("connection_string_hash") // Encrypted connection string
  host                 String?
  port                 Int?
  database             String?
  username             String?
  isActive             Boolean   @default(true) @map("is_active")
  lastConnectedAt      DateTime? @map("last_connected_at")
  schemaMetadata       String?   @map("schema_metadata") // JSON as String
  createdAt            DateTime  @default(now()) @map("created_at")
  updatedAt            DateTime  @updatedAt @map("updated_at")

  // Relations
  user          User?          @relation(fields: [userId], references: [id], onDelete: Cascade)
  team          Team?          @relation(fields: [teamId], references: [id], onDelete: Cascade)
  querySessions QuerySession[]

  @@map("database_connections")
}

// DatabaseType as String instead of enum for SQLite compatibility
// Values: MYSQL, POSTGRESQL

// Query sessions and history
model QuerySession {
  id                   String   @id @default(cuid())
  userId               String   @map("user_id")
  databaseConnectionId String   @map("database_connection_id")
  name                 String?
  sessionData          String   @map("session_data") // JSON as String
  isActive             Boolean  @default(true) @map("is_active")
  createdAt            DateTime @default(now()) @map("created_at")
  updatedAt            DateTime @updatedAt @map("updated_at")

  // Relations
  user               User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  databaseConnection DatabaseConnection @relation(fields: [databaseConnectionId], references: [id], onDelete: Cascade)
  generatedQueries   GeneratedQuery[]
  chatMessages       ChatMessage[]

  @@map("query_sessions")
}

// Generated queries and their metadata
model GeneratedQuery {
  id               String   @id @default(cuid())
  sessionId        String   @map("session_id")
  userId           String   @map("user_id")
  userInput        String   @map("user_input")
  generatedSQL     String   @map("generated_sql")
  explanation      String?
  databaseType     String   @map("database_type")
  executionTime    Int?     @map("execution_time") // in milliseconds
  rowsAffected     Int?     @map("rows_affected")
  userFeedback     Int?     @map("user_feedback") // 1-5 rating
  status           String   @default("GENERATED")
  errorMessage     String?  @map("error_message")
  performanceData  String?  @map("performance_data") // JSON as String
  optimizationTips String?  @map("optimization_tips") // JSON as String
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  // Relations
  session QuerySession @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  user    User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("generated_queries")
}

// QueryStatus as String instead of enum for SQLite compatibility
// Values: GENERATED, EXECUTED, FAILED, OPTIMIZED

// Chat messages for AI conversations
model ChatMessage {
  id        String   @id @default(cuid())
  sessionId String   @map("session_id")
  role      String
  content   String
  metadata  String? // JSON as String
  createdAt DateTime @default(now()) @map("created_at")

  // Relations
  session QuerySession @relation(fields: [sessionId], references: [id], onDelete: Cascade)

  @@map("chat_messages")
}

// MessageRole as String instead of enum for SQLite compatibility
// Values: USER, ASSISTANT, SYSTEM

// API Keys for external integrations
model ApiKey {
  id          String    @id @default(cuid())
  userId      String    @map("user_id")
  name        String
  keyHash     String    @unique @map("key_hash")
  permissions String    @default("[]") // JSON as String
  lastUsedAt  DateTime? @map("last_used_at")
  expiresAt   DateTime? @map("expires_at")
  isActive    Boolean   @default(true) @map("is_active")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("api_keys")
}
