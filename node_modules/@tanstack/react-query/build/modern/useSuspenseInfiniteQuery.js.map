{"version": 3, "sources": ["../../src/useSuspenseInfiniteQuery.ts"], "sourcesContent": ["'use client'\nimport { InfiniteQueryObserver, skipToken } from '@tanstack/query-core'\nimport { useBaseQuery } from './useBaseQuery'\nimport { defaultThrowOnError } from './suspense'\nimport type {\n  DefaultError,\n  InfiniteData,\n  InfiniteQueryObserverSuccessResult,\n  QueryClient,\n  QueryKey,\n  QueryObserver,\n} from '@tanstack/query-core'\nimport type {\n  UseSuspenseInfiniteQueryOptions,\n  UseSuspenseInfiniteQueryResult,\n} from './types'\n\nexport function useSuspenseInfiniteQuery<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n>(\n  options: UseSuspenseInfiniteQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryKey,\n    TPageParam\n  >,\n  queryClient?: QueryClient,\n): UseSuspenseInfiniteQueryResult<TData, TError> {\n  if (process.env.NODE_ENV !== 'production') {\n    if ((options.queryFn as any) === skipToken) {\n      console.error('skipToken is not allowed for useSuspenseInfiniteQuery')\n    }\n  }\n\n  return useBaseQuery(\n    {\n      ...options,\n      enabled: true,\n      suspense: true,\n      throwOnError: defaultThrowOnError,\n    },\n    InfiniteQueryObserver as typeof QueryObserver,\n    queryClient,\n  ) as InfiniteQueryObserverSuccessResult<TData, TError>\n}\n"], "mappings": ";;;AACA,SAAS,uBAAuB,iBAAiB;AACjD,SAAS,oBAAoB;AAC7B,SAAS,2BAA2B;AAc7B,SAAS,yBAOd,SAOA,aAC+C;AAC/C,MAAI,QAAQ,IAAI,aAAa,cAAc;AACzC,QAAK,QAAQ,YAAoB,WAAW;AAC1C,cAAQ,MAAM,uDAAuD;AAAA,IACvE;AAAA,EACF;AAEA,SAAO;AAAA,IACL;AAAA,MACE,GAAG;AAAA,MACH,SAAS;AAAA,MACT,UAAU;AAAA,MACV,cAAc;AAAA,IAChB;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;", "names": []}