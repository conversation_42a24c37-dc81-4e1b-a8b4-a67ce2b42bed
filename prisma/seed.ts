import { PrismaClient } from '@prisma/client';
import { encrypt } from '../src/lib/utils/encryption';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create demo user
  const demoUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Demo User',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    },
  });

  console.log('✅ Created demo user:', demoUser.email);

  // Create demo database connections
  const postgresConnection = await prisma.databaseConnection.create({
    data: {
      userId: demoUser.id,
      name: 'Demo PostgreSQL',
      databaseType: 'POSTGRESQL',
      connectionStringHash: encrypt('postgresql://demo:demo@localhost:5432/demo_db'),
      host: 'localhost',
      port: 5432,
      database: 'demo_db',
      username: 'demo',
      isActive: true,
      lastConnectedAt: new Date(),
      schemaMetadata: JSON.stringify({
        tables: [
          {
            name: 'users',
            columns: [
              { name: 'id', type: 'integer', isPrimary: true },
              { name: 'email', type: 'varchar', isUnique: true },
              { name: 'name', type: 'varchar' },
              { name: 'created_at', type: 'timestamp' },
            ],
          },
          {
            name: 'orders',
            columns: [
              { name: 'id', type: 'integer', isPrimary: true },
              { name: 'user_id', type: 'integer', isForeignKey: true },
              { name: 'total', type: 'decimal' },
              { name: 'status', type: 'varchar' },
              { name: 'created_at', type: 'timestamp' },
            ],
          },
          {
            name: 'products',
            columns: [
              { name: 'id', type: 'integer', isPrimary: true },
              { name: 'name', type: 'varchar' },
              { name: 'price', type: 'decimal' },
              { name: 'category', type: 'varchar' },
              { name: 'stock', type: 'integer' },
            ],
          },
        ],
      }),
    },
  });

  const mysqlConnection = await prisma.databaseConnection.create({
    data: {
      userId: demoUser.id,
      name: 'Demo MySQL',
      databaseType: 'MYSQL',
      connectionStringHash: encrypt('mysql://demo:demo@localhost:3306/demo_db'),
      host: 'localhost',
      port: 3306,
      database: 'demo_db',
      username: 'demo',
      isActive: true,
      schemaMetadata: JSON.stringify({
        tables: [
          {
            name: 'customers',
            columns: [
              { name: 'id', type: 'int', isPrimary: true },
              { name: 'email', type: 'varchar', isUnique: true },
              { name: 'first_name', type: 'varchar' },
              { name: 'last_name', type: 'varchar' },
              { name: 'phone', type: 'varchar' },
            ],
          },
          {
            name: 'sales',
            columns: [
              { name: 'id', type: 'int', isPrimary: true },
              { name: 'customer_id', type: 'int', isForeignKey: true },
              { name: 'amount', type: 'decimal' },
              { name: 'sale_date', type: 'datetime' },
              { name: 'region', type: 'varchar' },
            ],
          },
        ],
      }),
    },
  });

  console.log('✅ Created database connections');

  // Create demo query session
  const session = await prisma.querySession.create({
    data: {
      userId: demoUser.id,
      databaseConnectionId: postgresConnection.id,
      name: 'Customer Analysis Session',
      sessionData: JSON.stringify({
        context: 'Analyzing customer behavior and sales patterns',
        preferences: {
          outputFormat: 'table',
          includeExplanations: true,
        },
      }),
    },
  });

  console.log('✅ Created query session');

  // Create demo chat messages
  await prisma.chatMessage.createMany({
    data: [
      {
        sessionId: session.id,
        role: 'USER',
        content: 'Show me the top 10 customers by total order value',
        createdAt: new Date(Date.now() - 3600000), // 1 hour ago
      },
      {
        sessionId: session.id,
        role: 'ASSISTANT',
        content: 'I\'ll help you find the top 10 customers by total order value. Let me generate a SQL query for that.',
        metadata: JSON.stringify({
          type: 'response',
          confidence: 0.95,
        }),
        createdAt: new Date(Date.now() - 3590000),
      },
    ],
  });

  // Create demo generated queries
  const queries = await prisma.generatedQuery.createMany({
    data: [
      {
        sessionId: session.id,
        userId: demoUser.id,
        userInput: 'Show me the top 10 customers by total order value',
        generatedSQL: `SELECT 
  u.id,
  u.name,
  u.email,
  SUM(o.total) as total_orders,
  COUNT(o.id) as order_count
FROM users u
JOIN orders o ON u.id = o.user_id
WHERE o.status = 'completed'
GROUP BY u.id, u.name, u.email
ORDER BY total_orders DESC
LIMIT 10;`,
        explanation: 'This query joins the users and orders tables to calculate the total order value for each customer. It filters for completed orders only and returns the top 10 customers by total spending.',
        databaseType: 'POSTGRESQL',
        status: 'EXECUTED',
        executionTime: 245,
        rowsAffected: 10,
        userFeedback: 5,
        performanceData: JSON.stringify({
          executionPlan: 'Hash Join on user_id, Sort by total_orders',
          indexesUsed: ['users_pkey', 'orders_user_id_idx'],
          estimatedCost: 156.23,
        }),
        optimizationTips: JSON.stringify({
          suggestions: [
            'Consider adding an index on orders.status for better performance',
            'The query is well-optimized for this dataset size',
          ],
        }),
        createdAt: new Date(Date.now() - 3580000),
      },
      {
        sessionId: session.id,
        userId: demoUser.id,
        userInput: 'What are the monthly sales trends for this year?',
        generatedSQL: `SELECT 
  DATE_TRUNC('month', o.created_at) as month,
  COUNT(o.id) as order_count,
  SUM(o.total) as total_revenue,
  AVG(o.total) as avg_order_value
FROM orders o
WHERE o.created_at >= DATE_TRUNC('year', CURRENT_DATE)
  AND o.status = 'completed'
GROUP BY DATE_TRUNC('month', o.created_at)
ORDER BY month;`,
        explanation: 'This query analyzes monthly sales trends by grouping orders by month and calculating key metrics like total revenue, order count, and average order value.',
        databaseType: 'POSTGRESQL',
        status: 'EXECUTED',
        executionTime: 189,
        rowsAffected: 12,
        userFeedback: 4,
        createdAt: new Date(Date.now() - 1800000), // 30 minutes ago
      },
      {
        sessionId: session.id,
        userId: demoUser.id,
        userInput: 'Find products that are running low on stock',
        generatedSQL: `SELECT 
  p.id,
  p.name,
  p.category,
  p.stock,
  p.price
FROM products p
WHERE p.stock < 10
ORDER BY p.stock ASC, p.category;`,
        explanation: 'This query identifies products with low stock levels (less than 10 units) and orders them by stock level and category for easy inventory management.',
        databaseType: 'POSTGRESQL',
        status: 'GENERATED',
        createdAt: new Date(Date.now() - 300000), // 5 minutes ago
      },
    ],
  });

  console.log('✅ Created demo queries and chat messages');

  // Create a team
  const team = await prisma.team.create({
    data: {
      name: 'Data Analytics Team',
      description: 'Team focused on business intelligence and data analysis',
    },
  });

  // Add user to team
  await prisma.teamMembership.create({
    data: {
      userId: demoUser.id,
      teamId: team.id,
      role: 'OWNER',
    },
  });

  console.log('✅ Created demo team and membership');

  console.log('🎉 Database seeding completed successfully!');
  console.log(`
📊 Demo Data Summary:
- User: ${demoUser.email}
- Database Connections: 2 (PostgreSQL, MySQL)
- Query Sessions: 1
- Generated Queries: 3
- Chat Messages: 2
- Teams: 1

🚀 You can now test the application with realistic data!
  `);
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
