{"name": "querycraft-studio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "tsx prisma/seed.ts", "db:studio": "prisma studio", "db:reset": "prisma migrate reset"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.3.2", "@monaco-editor/react": "^4.6.0", "@prisma/client": "^5.22.0", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "@tanstack/react-query": "^5.8.4", "@tanstack/react-query-devtools": "^5.80.2", "@types/d3": "^7.4.3", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "d3": "^7.8.5", "framer-motion": "^10.16.16", "ioredis": "^5.3.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.294.0", "next": "14.0.4", "next-themes": "^0.4.6", "openai": "^4.20.1", "prisma": "^5.22.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7", "uuid": "^9.0.1", "zod": "^3.25.49", "zustand": "^4.4.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.9.4", "@types/react": "^18.2.38", "@types/react-dom": "^18.2.17", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "autoprefixer": "^10.4.16", "eslint": "^8.54.0", "eslint-config-next": "14.0.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.32", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7", "tailwindcss": "^3.3.6", "tsx": "^4.19.4", "typescript": "^5.3.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}