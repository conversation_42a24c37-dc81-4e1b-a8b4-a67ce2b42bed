# QueryCraft Studio - Database Setup Guide

Este guia explica como configurar o banco de dados PostgreSQL com Prisma para o QueryCraft Studio.

## 🗄️ Arquitetura do Banco de Dados

O QueryCraft Studio utiliza PostgreSQL como banco principal com as seguintes características:

- **ORM**: Prisma para type-safe database access
- **Criptografia**: Strings de conexão criptografadas
- **Auditoria**: Timestamps automáticos
- **Relacionamentos**: Suporte completo a teams e colaboração
- **Performance**: Índices otimizados para queries frequentes

## 📊 Schema Principal

### Modelos Principais:

1. **User** - Gerenciamento de usuários
2. **Team** - Colaboração em equipes
3. **DatabaseConnection** - Conexões de banco (criptografadas)
4. **QuerySession** - Sessões de trabalho
5. **GeneratedQuery** - Queries geradas pela IA
6. **ChatMessage** - Histórico de conversas
7. **ApiKey** - Chaves de API para integrações

## 🚀 Setup Rápido

### 1. Configuração do Ambiente

```bash
# Copie as variáveis de ambiente
cp .env.example .env.local

# Configure as seguintes variáveis:
DATABASE_URL="postgresql://postgres:password@localhost:5432/querycraft_studio"
ENCRYPTION_KEY="querycraft-studio-encryption-key-2024-super-secret-key"
```

### 2. Opção A: Docker (Recomendado)

```bash
# Inicie PostgreSQL e Redis
docker-compose up -d postgres redis

# Aguarde o banco inicializar (30 segundos)
sleep 30

# Execute as migrações
npm run db:push

# Popule com dados de exemplo
npm run db:seed
```

### 3. Opção B: PostgreSQL Local

```bash
# Instale PostgreSQL localmente
# macOS: brew install postgresql
# Ubuntu: sudo apt-get install postgresql

# Crie o banco
createdb querycraft_studio

# Execute as migrações
npm run db:push

# Popule com dados de exemplo
npm run db:seed
```

## 🛠️ Scripts Disponíveis

```bash
# Gerar cliente Prisma
npm run db:generate

# Aplicar mudanças no schema (desenvolvimento)
npm run db:push

# Criar e aplicar migrações (produção)
npm run db:migrate

# Popular banco com dados de exemplo
npm run db:seed

# Abrir Prisma Studio (interface visual)
npm run db:studio

# Reset completo do banco
npm run db:reset
```

## 📋 Dados de Exemplo

O script de seed cria:

- **Usuário Demo**: `<EMAIL>`
- **2 Conexões**: PostgreSQL e MySQL de exemplo
- **1 Sessão**: Com histórico de queries
- **3 Queries**: Exemplos de diferentes tipos
- **1 Team**: Para demonstrar colaboração

## 🔐 Segurança

### Criptografia de Conexões

As strings de conexão são criptografadas usando AES-256-GCM:

```typescript
// Exemplo de uso
import { encrypt, decrypt } from '@/lib/utils/encryption';

const encrypted = encrypt('********************************/db');
const decrypted = decrypt(encrypted);
```

### Chaves de API

```typescript
import { generateApiKey, verifyApiKey } from '@/lib/utils/encryption';

const { key, hash } = generateApiKey();
const isValid = verifyApiKey(key, hash);
```

## 🔄 Migrações

### Desenvolvimento
```bash
# Aplicar mudanças diretamente
npm run db:push
```

### Produção
```bash
# Criar migração
npm run db:migrate

# Aplicar em produção
npx prisma migrate deploy
```

## 📊 Monitoramento

### Prisma Studio
```bash
npm run db:studio
# Acesse: http://localhost:5555
```

### pgAdmin (via Docker)
```bash
docker-compose up -d pgadmin
# Acesse: http://localhost:5050
# Email: <EMAIL>
# Senha: admin123
```

## 🧪 Testes

```bash
# Testar conexão
npx prisma db pull

# Validar schema
npx prisma validate

# Verificar status
npx prisma migrate status
```

## 🚨 Troubleshooting

### Erro de Conexão
```bash
# Verificar se PostgreSQL está rodando
docker-compose ps

# Logs do banco
docker-compose logs postgres
```

### Reset Completo
```bash
# Parar containers
docker-compose down

# Remover volumes
docker-compose down -v

# Reiniciar
docker-compose up -d postgres
npm run db:push
npm run db:seed
```

### Problemas de Criptografia
```bash
# Verificar variável de ambiente
echo $ENCRYPTION_KEY

# Regenerar chave se necessário
openssl rand -hex 32
```

## 📈 Performance

### Índices Recomendados

O schema já inclui índices otimizados:

- `users.email` (unique)
- `database_connections.user_id`
- `query_sessions.user_id`
- `generated_queries.session_id`
- `chat_messages.session_id`

### Queries Otimizadas

```sql
-- Exemplo de query otimizada para histórico
SELECT gq.*, qs.name as session_name 
FROM generated_queries gq
JOIN query_sessions qs ON gq.session_id = qs.id
WHERE gq.user_id = $1
ORDER BY gq.created_at DESC
LIMIT 50;
```

## 🔗 Integração com APIs

### Exemplo de Uso

```typescript
import { UserService } from '@/lib/database/services/user-service';
import { QueryService } from '@/lib/database/services/query-service';

// Criar usuário
const user = await UserService.createUser({
  email: '<EMAIL>',
  name: 'John Doe'
});

// Criar sessão
const session = await QueryService.createSession({
  userId: user.id,
  databaseConnectionId: 'connection-id'
});
```

## 📚 Recursos Adicionais

- [Prisma Documentation](https://www.prisma.io/docs)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Docker Compose Guide](https://docs.docker.com/compose/)

---

🎉 **Pronto!** Seu banco de dados está configurado e pronto para uso com o QueryCraft Studio!
