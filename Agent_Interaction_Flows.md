# QueryCraft Studio - Agent Interaction Flows

## Overview

This document details the step-by-step agent interactions for key user scenarios in QueryCraft Studio. Each workflow demonstrates how the seven specialized agents collaborate to deliver comprehensive SQL development assistance.

## Scenario 1: Complex Natural Language Query with Error Resolution

**User Request**: "Show me the top 10 customers by revenue in the last quarter, including their contact information and the number of orders they placed, but exclude any customers from the test environment"

### Workflow Sequence Diagram

```mermaid
sequenceDiagram
    participant User
    participant U<PERSON> as User Interface
    participant Or<PERSON> as Orchestrator Agent
    participant NLP as NLP Intent Agent
    participant <PERSON>hema as Schema Intelligence Agent
    participant SQL as SQL Generation Agent
    participant Debug as Debugging Agent
    participant Explain as Explanation Agent

    User->>UI: Submit natural language query
    UI->>Orch: Process user request
    
    Orch->>NLP: Analyze user intent
    NLP->>NLP: Extract entities and requirements
    NLP->>Orch: Return intent analysis
    
    Orch->>Schema: Get schema context
    Schema->>Schema: Analyze customer, order, revenue tables
    Schema->>Orch: Return schema metadata and relationships
    
    Orch->>SQL: Generate SQL query
    SQL->>SQL: Create complex query with joins and aggregations
    SQL->>Orch: Return generated query
    
    Note over SQL: Generated query has syntax error
    
    Orch->>Debug: Validate generated query
    Debug->>Debug: Detect syntax error in GROUP BY clause
    Debug->>Orch: Return error analysis
    
    Orch->>SQL: Fix query based on error analysis
    SQL->>SQL: Correct GROUP BY clause
    SQL->>Orch: Return corrected query
    
    Orch->>Debug: Re-validate corrected query
    Debug->>Debug: Validate syntax and logic
    Debug->>Orch: Confirm query is valid
    
    Orch->>Explain: Generate explanation
    Explain->>Explain: Break down query logic
    Explain->>Orch: Return step-by-step explanation
    
    Orch->>UI: Return complete response
    UI->>User: Display query, explanation, and insights
```

### Detailed Step-by-Step Flow

#### Step 1: Intent Analysis (NLP Agent)
```json
{
  "input": "Show me the top 10 customers by revenue in the last quarter...",
  "analysis": {
    "primaryIntent": "analytical_query",
    "entities": {
      "metrics": ["revenue", "number of orders"],
      "dimensions": ["customers", "contact information"],
      "filters": ["last quarter", "exclude test environment"],
      "aggregations": ["top 10", "sum", "count"],
      "timeframe": "last quarter"
    },
    "complexity": "high",
    "requiredTables": ["customers", "orders", "order_items", "products"]
  }
}
```

#### Step 2: Schema Intelligence (Schema Agent)
```json
{
  "schemaAnalysis": {
    "identifiedTables": {
      "customers": {
        "columns": ["customer_id", "name", "email", "phone", "environment_flag"],
        "relationships": ["orders.customer_id"]
      },
      "orders": {
        "columns": ["order_id", "customer_id", "order_date", "total_amount"],
        "relationships": ["customers.customer_id", "order_items.order_id"]
      },
      "order_items": {
        "columns": ["order_id", "product_id", "quantity", "unit_price"],
        "relationships": ["orders.order_id"]
      }
    },
    "suggestedJoins": [
      "customers.customer_id = orders.customer_id",
      "orders.order_id = order_items.order_id"
    ],
    "filterSuggestions": [
      "customers.environment_flag != 'test'",
      "orders.order_date >= DATE_SUB(NOW(), INTERVAL 3 MONTH)"
    ]
  }
}
```

#### Step 3: SQL Generation (SQL Agent)
```sql
-- Initial generated query (with error)
SELECT 
    c.customer_id,
    c.name,
    c.email,
    c.phone,
    SUM(oi.quantity * oi.unit_price) as total_revenue,
    COUNT(DISTINCT o.order_id) as order_count
FROM customers c
JOIN orders o ON c.customer_id = o.customer_id
JOIN order_items oi ON o.order_id = oi.order_id
WHERE c.environment_flag != 'test'
    AND o.order_date >= DATE_SUB(NOW(), INTERVAL 3 MONTH)
GROUP BY c.customer_id  -- Error: missing columns from SELECT
ORDER BY total_revenue DESC
LIMIT 10;
```

#### Step 4: Error Detection (Debug Agent)
```json
{
  "errorAnalysis": {
    "errorType": "syntax",
    "severity": "high",
    "location": {
      "line": 10,
      "column": 1,
      "clause": "GROUP BY"
    },
    "description": "GROUP BY clause missing columns that appear in SELECT",
    "specificIssue": "c.name, c.email, c.phone must be included in GROUP BY or removed from SELECT",
    "suggestedFix": "Add missing columns to GROUP BY clause"
  }
}
```

#### Step 5: Query Correction (SQL Agent)
```sql
-- Corrected query
SELECT 
    c.customer_id,
    c.name,
    c.email,
    c.phone,
    SUM(oi.quantity * oi.unit_price) as total_revenue,
    COUNT(DISTINCT o.order_id) as order_count
FROM customers c
JOIN orders o ON c.customer_id = o.customer_id
JOIN order_items oi ON o.order_id = oi.order_id
WHERE c.environment_flag != 'test'
    AND o.order_date >= DATE_SUB(NOW(), INTERVAL 3 MONTH)
GROUP BY c.customer_id, c.name, c.email, c.phone
ORDER BY total_revenue DESC
LIMIT 10;
```

#### Step 6: Query Explanation (Explanation Agent)
```json
{
  "explanation": {
    "overview": "This query identifies the top 10 customers by revenue in the last quarter, excluding test environment customers.",
    "stepByStep": [
      {
        "step": 1,
        "operation": "JOIN customers and orders",
        "description": "Connect customer information with their orders using customer_id"
      },
      {
        "step": 2,
        "operation": "JOIN with order_items",
        "description": "Get detailed order item information to calculate revenue"
      },
      {
        "step": 3,
        "operation": "FILTER by environment and date",
        "description": "Exclude test customers and limit to last quarter orders"
      },
      {
        "step": 4,
        "operation": "GROUP BY customer",
        "description": "Aggregate data per customer to calculate totals"
      },
      {
        "step": 5,
        "operation": "CALCULATE metrics",
        "description": "Sum revenue and count distinct orders per customer"
      },
      {
        "step": 6,
        "operation": "ORDER and LIMIT",
        "description": "Sort by revenue descending and take top 10"
      }
    ]
  }
}
```

## Scenario 2: Query Performance Optimization

**User Request**: "This query is running too slowly, can you help optimize it?"

### Workflow Sequence Diagram

```mermaid
sequenceDiagram
    participant User
    participant UI as User Interface
    participant Orch as Orchestrator Agent
    participant Debug as Debugging Agent
    participant Opt as Optimization Agent
    participant Schema as Schema Intelligence Agent
    participant SQL as SQL Generation Agent

    User->>UI: Submit slow query for optimization
    UI->>Orch: Process optimization request
    
    Orch->>Debug: Analyze query performance
    Debug->>Debug: Identify performance bottlenecks
    Debug->>Orch: Return performance analysis
    
    Orch->>Schema: Get table statistics and indexes
    Schema->>Schema: Analyze table sizes and index usage
    Schema->>Orch: Return schema performance data
    
    Orch->>Opt: Generate optimization suggestions
    Opt->>Opt: Analyze execution plan and suggest improvements
    Opt->>Orch: Return ranked optimization suggestions
    
    Orch->>SQL: Generate optimized query variants
    SQL->>SQL: Create multiple optimized versions
    SQL->>Orch: Return optimized queries with explanations
    
    Orch->>UI: Return optimization recommendations
    UI->>User: Display suggestions and optimized queries
```

### Detailed Optimization Flow

#### Step 1: Performance Analysis (Debug Agent)
```json
{
  "performanceAnalysis": {
    "executionTime": "45.2 seconds",
    "rowsExamined": 2500000,
    "rowsReturned": 100,
    "bottlenecks": [
      {
        "type": "full_table_scan",
        "table": "orders",
        "impact": "high",
        "description": "No suitable index for date range filter"
      },
      {
        "type": "inefficient_join",
        "tables": ["customers", "orders"],
        "impact": "medium",
        "description": "Join condition not optimally indexed"
      }
    ],
    "recommendations": [
      "Add index on orders.order_date",
      "Consider composite index on orders(customer_id, order_date)"
    ]
  }
}
```

#### Step 2: Schema Analysis (Schema Agent)
```json
{
  "schemaPerformanceData": {
    "tableStatistics": {
      "orders": {
        "rowCount": 2500000,
        "avgRowLength": 256,
        "dataSize": "640MB",
        "indexSize": "128MB"
      },
      "customers": {
        "rowCount": 50000,
        "avgRowLength": 512,
        "dataSize": "25MB",
        "indexSize": "5MB"
      }
    },
    "existingIndexes": [
      "orders.PRIMARY (order_id)",
      "customers.PRIMARY (customer_id)",
      "orders.idx_customer_id (customer_id)"
    ],
    "missingIndexes": [
      "orders.order_date",
      "orders(customer_id, order_date)"
    ]
  }
}
```

#### Step 3: Optimization Suggestions (Optimization Agent)
```json
{
  "optimizationSuggestions": [
    {
      "priority": 1,
      "type": "index_creation",
      "description": "Create composite index on orders table",
      "implementation": "CREATE INDEX idx_orders_customer_date ON orders(customer_id, order_date);",
      "expectedImprovement": {
        "executionTimeReduction": 85,
        "resourceUsageReduction": 90
      },
      "effort": "low",
      "risk": "low"
    },
    {
      "priority": 2,
      "type": "query_rewrite",
      "description": "Rewrite subquery as JOIN for better performance",
      "expectedImprovement": {
        "executionTimeReduction": 40,
        "resourceUsageReduction": 30
      },
      "effort": "medium",
      "risk": "low"
    }
  ]
}
```

## Scenario 3: Legacy Query Understanding

**User Request**: "I inherited this complex query and need to understand what it does"

### Workflow Sequence Diagram

```mermaid
sequenceDiagram
    participant User
    participant UI as User Interface
    participant Orch as Orchestrator Agent
    participant Schema as Schema Intelligence Agent
    participant Explain as Explanation Agent
    participant Debug as Debugging Agent

    User->>UI: Submit complex legacy query
    UI->>Orch: Process explanation request
    
    Orch->>Schema: Analyze query tables and relationships
    Schema->>Schema: Map table relationships and data flow
    Schema->>Orch: Return schema context
    
    Orch->>Debug: Validate query logic
    Debug->>Debug: Check for potential issues or improvements
    Debug->>Orch: Return validation results
    
    Orch->>Explain: Generate comprehensive explanation
    Explain->>Explain: Break down query into logical components
    Explain->>Orch: Return detailed explanation and documentation
    
    Orch->>UI: Return complete analysis
    UI->>User: Display explanation, documentation, and insights
```

### Detailed Explanation Flow

#### Step 1: Query Structure Analysis
```json
{
  "queryStructure": {
    "type": "complex_analytical",
    "components": {
      "ctes": 2,
      "subqueries": 3,
      "joins": 5,
      "windowFunctions": 2,
      "aggregations": 4
    },
    "complexity": "very_high",
    "estimatedReadingTime": "15 minutes"
  }
}
```

#### Step 2: Business Logic Identification
```json
{
  "businessLogic": {
    "purpose": "Customer lifetime value analysis with cohort segmentation",
    "keyMetrics": [
      "Customer acquisition cost",
      "Average order value",
      "Customer retention rate",
      "Lifetime value calculation"
    ],
    "businessRules": [
      "Customers segmented by acquisition quarter",
      "Revenue attributed to first-touch attribution",
      "Churn defined as no activity in 90 days"
    ]
  }
}
```

#### Step 3: Step-by-Step Breakdown
```json
{
  "stepByStepExplanation": [
    {
      "section": "CTE 1: Customer Cohorts",
      "purpose": "Group customers by acquisition quarter",
      "logic": "Identifies when each customer first made a purchase",
      "outputColumns": ["customer_id", "acquisition_quarter", "first_order_date"]
    },
    {
      "section": "CTE 2: Revenue Attribution",
      "purpose": "Calculate revenue per customer per quarter",
      "logic": "Sums order values and applies first-touch attribution",
      "outputColumns": ["customer_id", "quarter", "attributed_revenue"]
    },
    {
      "section": "Main Query",
      "purpose": "Calculate lifetime value metrics by cohort",
      "logic": "Aggregates revenue data and calculates retention rates",
      "outputColumns": ["cohort", "customers", "avg_ltv", "retention_rate"]
    }
  ]
}
```

This comprehensive workflow documentation ensures that engineering teams understand exactly how agents collaborate to deliver intelligent SQL development assistance across all major use cases.
