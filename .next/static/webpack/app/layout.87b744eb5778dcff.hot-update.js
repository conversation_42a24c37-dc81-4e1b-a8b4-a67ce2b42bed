"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"3ff1f3dc77fd\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/ZjVlZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjNmZjFmM2RjNzdmZFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/providers/database-provider.tsx":
/*!********************************************************!*\
  !*** ./src/components/providers/database-provider.tsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DatabaseProvider: function() { return /* binding */ DatabaseProvider; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _stores_query_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/stores/query-store */ \"(app-pages-browser)/./src/stores/query-store.ts\");\n/* __next_internal_client_entry_do_not_use__ DatabaseProvider auto */ \nvar _s = $RefreshSig$();\n\n\nfunction DatabaseProvider(param) {\n    let { children } = param;\n    _s();\n    const { setCurrentUser, loadUserConnections, loadUserSessions, createNewSession, currentUser, connections, currentSession } = (0,_stores_query_store__WEBPACK_IMPORTED_MODULE_2__.useQueryStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Initialize with demo user from database\n        const initializeDemoUser = async ()=>{\n            if (!currentUser) {\n                try {\n                    // Fetch the demo user from the database\n                    const response = await fetch(\"/api/users?email=<EMAIL>\");\n                    if (response.ok) {\n                        const demoUser = await response.json();\n                        setCurrentUser({\n                            id: demoUser.id,\n                            name: demoUser.name,\n                            email: demoUser.email\n                        });\n                        // Load user data\n                        await loadUserConnections(demoUser.id);\n                        await loadUserSessions(demoUser.id);\n                        // Set the first connection as current if available\n                        const state = _stores_query_store__WEBPACK_IMPORTED_MODULE_2__.useQueryStore.getState();\n                        if (state.connections.length > 0) {\n                            _stores_query_store__WEBPACK_IMPORTED_MODULE_2__.useQueryStore.getState().setCurrentDatabase(state.connections[0]);\n                        }\n                    } else {\n                        console.error(\"Demo user not found in database\");\n                    }\n                } catch (error) {\n                    console.error(\"Failed to initialize demo user:\", error);\n                }\n            }\n        };\n        initializeDemoUser();\n    }, [\n        currentUser,\n        setCurrentUser,\n        loadUserConnections,\n        loadUserSessions\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n_s(DatabaseProvider, \"kbTGIBL5hx4gqmWtHO2veDFIIRY=\", false, function() {\n    return [\n        _stores_query_store__WEBPACK_IMPORTED_MODULE_2__.useQueryStore\n    ];\n});\n_c = DatabaseProvider;\nvar _c;\n$RefreshReg$(_c, \"DatabaseProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/providers/database-provider.tsx\n"));

/***/ })

});