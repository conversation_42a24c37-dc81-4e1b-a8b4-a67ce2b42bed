"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/query-history.tsx":
/*!****************************************************!*\
  !*** ./src/components/dashboard/query-history.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryHistory: function() { return /* binding */ QueryHistory; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_CopyIcon_DatabaseIcon_HistoryIcon_PlayIcon_ThumbsUpIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,CopyIcon,DatabaseIcon,HistoryIcon,PlayIcon,ThumbsUpIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/history.js\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_CopyIcon_DatabaseIcon_HistoryIcon_PlayIcon_ThumbsUpIcon_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,CopyIcon,DatabaseIcon,HistoryIcon,PlayIcon,ThumbsUpIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_CopyIcon_DatabaseIcon_HistoryIcon_PlayIcon_ThumbsUpIcon_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,CopyIcon,DatabaseIcon,HistoryIcon,PlayIcon,ThumbsUpIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_CopyIcon_DatabaseIcon_HistoryIcon_PlayIcon_ThumbsUpIcon_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,CopyIcon,DatabaseIcon,HistoryIcon,PlayIcon,ThumbsUpIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/thumbs-up.js\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_CopyIcon_DatabaseIcon_HistoryIcon_PlayIcon_ThumbsUpIcon_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,CopyIcon,DatabaseIcon,HistoryIcon,PlayIcon,ThumbsUpIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_CopyIcon_DatabaseIcon_HistoryIcon_PlayIcon_ThumbsUpIcon_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,CopyIcon,DatabaseIcon,HistoryIcon,PlayIcon,ThumbsUpIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _stores_query_store__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/stores/query-store */ \"(app-pages-browser)/./src/stores/query-store.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ QueryHistory auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction QueryHistory(param) {\n    let { userId } = param;\n    _s();\n    const [queries, setQueries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const { setCurrentQuery, setActiveTab, currentUser } = (0,_stores_query_store__WEBPACK_IMPORTED_MODULE_6__.useQueryStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadQueryHistory = async ()=>{\n            if (!userId && !currentUser) return;\n            const userIdToUse = userId || (currentUser === null || currentUser === void 0 ? void 0 : currentUser.id);\n            if (!userIdToUse) return;\n            try {\n                setLoading(true);\n                const response = await fetch(\"/api/queries?type=history&userId=\".concat(userIdToUse, \"&limit=50\"));\n                if (response.ok) {\n                    const data = await response.json();\n                    setQueries(data);\n                }\n            } catch (error) {\n                console.error(\"Failed to load query history:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        loadQueryHistory();\n    }, [\n        userId,\n        currentUser\n    ]);\n    const handleUseQuery = (sql)=>{\n        setCurrentQuery(sql);\n        setActiveTab(\"editor\");\n    };\n    const handleCopyQuery = (sql)=>{\n        navigator.clipboard.writeText(sql);\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"EXECUTED\":\n                return \"bg-green-100 text-green-800\";\n            case \"FAILED\":\n                return \"bg-red-100 text-red-800\";\n            case \"GENERATED\":\n                return \"bg-blue-100 text-blue-800\";\n            case \"OPTIMIZED\":\n                return \"bg-purple-100 text-purple-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_CopyIcon_DatabaseIcon_HistoryIcon_PlayIcon_ThumbsUpIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 13\n                            }, this),\n                            \"Query History\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"loading-spinner\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-sm text-muted-foreground\",\n                                children: \"Loading history...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_CopyIcon_DatabaseIcon_HistoryIcon_PlayIcon_ThumbsUpIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, this),\n                        \"Query History\",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                            variant: \"secondary\",\n                            className: \"ml-auto\",\n                            children: [\n                                queries.length,\n                                \" queries\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__.ScrollArea, {\n                    className: \"h-[600px]\",\n                    children: queries.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8 text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_CopyIcon_DatabaseIcon_HistoryIcon_PlayIcon_ThumbsUpIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"No queries in history yet\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                children: \"Start generating queries to see them here\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: queries.map((query)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"border-l-4 border-l-primary/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-foreground\",\n                                                            children: query.userInput\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                                                            lineNumber: 124,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2 mt-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    className: getStatusColor(query.status),\n                                                                    children: query.status\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                                                                    lineNumber: 128,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-1 text-xs text-muted-foreground\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_CopyIcon_DatabaseIcon_HistoryIcon_PlayIcon_ThumbsUpIcon_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                                                                            lineNumber: 132,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        query.databaseType\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                                                                    lineNumber: 131,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-1 text-xs text-muted-foreground\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_CopyIcon_DatabaseIcon_HistoryIcon_PlayIcon_ThumbsUpIcon_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                                                                            lineNumber: 136,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatRelativeTime)(new Date(query.createdAt))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                                                                    lineNumber: 135,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                                                            lineNumber: 127,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-muted rounded-lg p-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                    className: \"text-xs font-mono overflow-x-auto whitespace-pre-wrap\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                        children: query.generatedSQL\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 23\n                                            }, this),\n                                            query.explanation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Explanation:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    \" \",\n                                                    query.explanation\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 25\n                                            }, this),\n                                            query.executionTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-4 text-xs text-muted-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Execution: \",\n                                                            query.executionTime,\n                                                            \"ms\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    query.rowsAffected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Rows: \",\n                                                            query.rowsAffected\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    query.userFeedback && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Rating:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                                                                lineNumber: 166,\n                                                                columnNumber: 31\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex\",\n                                                                children: [\n                                                                    ...Array(5)\n                                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_CopyIcon_DatabaseIcon_HistoryIcon_PlayIcon_ThumbsUpIcon_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"h-3 w-3 \".concat(i < query.userFeedback ? \"text-yellow-500 fill-current\" : \"text-gray-300\")\n                                                                    }, i, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                                                                        lineNumber: 169,\n                                                                        columnNumber: 35\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                                                                lineNumber: 167,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 29\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>handleUseQuery(query.generatedSQL),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_CopyIcon_DatabaseIcon_HistoryIcon_PlayIcon_ThumbsUpIcon_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                                                                lineNumber: 191,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"Use\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>handleCopyQuery(query.generatedSQL),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_CopyIcon_DatabaseIcon_HistoryIcon_PlayIcon_ThumbsUpIcon_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                                                                lineNumber: 199,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"Copy\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 19\n                                }, this)\n                            }, query.id, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-history.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, this);\n}\n_s(QueryHistory, \"tJEQz9E8aHTgkxD86CDCL8DoWto=\", false, function() {\n    return [\n        _stores_query_store__WEBPACK_IMPORTED_MODULE_6__.useQueryStore\n    ];\n});\n_c = QueryHistory;\nvar _c;\n$RefreshReg$(_c, \"QueryHistory\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/query-history.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/dashboard/query-workspace.tsx":
/*!******************************************************!*\
  !*** ./src/components/dashboard/query-workspace.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryWorkspace: function() { return /* binding */ QueryWorkspace; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_chat_chat_interface__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/chat/chat-interface */ \"(app-pages-browser)/./src/components/chat/chat-interface.tsx\");\n/* harmony import */ var _components_editor_sql_editor__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/editor/sql-editor */ \"(app-pages-browser)/./src/components/editor/sql-editor.tsx\");\n/* harmony import */ var _components_dashboard_schema_explorer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard/schema-explorer */ \"(app-pages-browser)/./src/components/dashboard/schema-explorer.tsx\");\n/* harmony import */ var _components_dashboard_query_results__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/dashboard/query-results */ \"(app-pages-browser)/./src/components/dashboard/query-results.tsx\");\n/* harmony import */ var _components_dashboard_query_history__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/dashboard/query-history */ \"(app-pages-browser)/./src/components/dashboard/query-history.tsx\");\n/* harmony import */ var _stores_query_store__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/stores/query-store */ \"(app-pages-browser)/./src/stores/query-store.ts\");\n/* harmony import */ var _barrel_optimize_names_BrainCircuitIcon_DatabaseIcon_HistoryIcon_PlayIcon_SaveIcon_ShareIcon_TrendingUpIcon_ZapIcon_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BrainCircuitIcon,DatabaseIcon,HistoryIcon,PlayIcon,SaveIcon,ShareIcon,TrendingUpIcon,ZapIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain-circuit.js\");\n/* harmony import */ var _barrel_optimize_names_BrainCircuitIcon_DatabaseIcon_HistoryIcon_PlayIcon_SaveIcon_ShareIcon_TrendingUpIcon_ZapIcon_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BrainCircuitIcon,DatabaseIcon,HistoryIcon,PlayIcon,SaveIcon,ShareIcon,TrendingUpIcon,ZapIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_BrainCircuitIcon_DatabaseIcon_HistoryIcon_PlayIcon_SaveIcon_ShareIcon_TrendingUpIcon_ZapIcon_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BrainCircuitIcon,DatabaseIcon,HistoryIcon,PlayIcon,SaveIcon,ShareIcon,TrendingUpIcon,ZapIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BrainCircuitIcon_DatabaseIcon_HistoryIcon_PlayIcon_SaveIcon_ShareIcon_TrendingUpIcon_ZapIcon_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BrainCircuitIcon,DatabaseIcon,HistoryIcon,PlayIcon,SaveIcon,ShareIcon,TrendingUpIcon,ZapIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/history.js\");\n/* harmony import */ var _barrel_optimize_names_BrainCircuitIcon_DatabaseIcon_HistoryIcon_PlayIcon_SaveIcon_ShareIcon_TrendingUpIcon_ZapIcon_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BrainCircuitIcon,DatabaseIcon,HistoryIcon,PlayIcon,SaveIcon,ShareIcon,TrendingUpIcon,ZapIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_BrainCircuitIcon_DatabaseIcon_HistoryIcon_PlayIcon_SaveIcon_ShareIcon_TrendingUpIcon_ZapIcon_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BrainCircuitIcon,DatabaseIcon,HistoryIcon,PlayIcon,SaveIcon,ShareIcon,TrendingUpIcon,ZapIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share.js\");\n/* harmony import */ var _barrel_optimize_names_BrainCircuitIcon_DatabaseIcon_HistoryIcon_PlayIcon_SaveIcon_ShareIcon_TrendingUpIcon_ZapIcon_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BrainCircuitIcon,DatabaseIcon,HistoryIcon,PlayIcon,SaveIcon,ShareIcon,TrendingUpIcon,ZapIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_BrainCircuitIcon_DatabaseIcon_HistoryIcon_PlayIcon_SaveIcon_ShareIcon_TrendingUpIcon_ZapIcon_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BrainCircuitIcon,DatabaseIcon,HistoryIcon,PlayIcon,SaveIcon,ShareIcon,TrendingUpIcon,ZapIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* __next_internal_client_entry_do_not_use__ QueryWorkspace auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction QueryWorkspace() {\n    _s();\n    const { activeTab, setActiveTab, currentQuery, setCurrentQuery, currentDatabase } = (0,_stores_query_store__WEBPACK_IMPORTED_MODULE_10__.useQueryStore)();\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const tabs = [\n        {\n            id: \"chat\",\n            label: \"AI Assistant\",\n            icon: _barrel_optimize_names_BrainCircuitIcon_DatabaseIcon_HistoryIcon_PlayIcon_SaveIcon_ShareIcon_TrendingUpIcon_ZapIcon_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n        },\n        {\n            id: \"editor\",\n            label: \"SQL Editor\",\n            icon: _barrel_optimize_names_BrainCircuitIcon_DatabaseIcon_HistoryIcon_PlayIcon_SaveIcon_ShareIcon_TrendingUpIcon_ZapIcon_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n        },\n        {\n            id: \"results\",\n            label: \"Results\",\n            icon: _barrel_optimize_names_BrainCircuitIcon_DatabaseIcon_HistoryIcon_PlayIcon_SaveIcon_ShareIcon_TrendingUpIcon_ZapIcon_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n        },\n        {\n            id: \"history\",\n            label: \"History\",\n            icon: _barrel_optimize_names_BrainCircuitIcon_DatabaseIcon_HistoryIcon_PlayIcon_SaveIcon_ShareIcon_TrendingUpIcon_ZapIcon_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-80 border-r border-border bg-card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-border\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold\",\n                                        children: \"Database Schema\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        variant: isConnected ? \"default\" : \"secondary\",\n                                        children: isConnected ? \"Connected\" : \"Not Connected\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 11\n                            }, this),\n                            !isConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                className: \"w-full\",\n                                onClick: ()=>setIsConnected(true),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BrainCircuitIcon_DatabaseIcon_HistoryIcon_PlayIcon_SaveIcon_ShareIcon_TrendingUpIcon_ZapIcon_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Connect Database\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_schema_explorer__WEBPACK_IMPORTED_MODULE_7__.SchemaExplorer, {\n                            isConnected: isConnected\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-b border-border bg-card\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between px-6 py-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-1\",\n                                    children: tabs.map((tab)=>{\n                                        const Icon = tab.icon;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab(tab.id),\n                                            className: \"flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors \".concat(activeTab === tab.id ? \"bg-primary text-primary-foreground\" : \"text-muted-foreground hover:text-foreground hover:bg-muted\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 21\n                                                }, this),\n                                                tab.label\n                                            ]\n                                        }, tab.id, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BrainCircuitIcon_DatabaseIcon_HistoryIcon_PlayIcon_SaveIcon_ShareIcon_TrendingUpIcon_ZapIcon_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"History\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BrainCircuitIcon_DatabaseIcon_HistoryIcon_PlayIcon_SaveIcon_ShareIcon_TrendingUpIcon_ZapIcon_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Save\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BrainCircuitIcon_DatabaseIcon_HistoryIcon_PlayIcon_SaveIcon_ShareIcon_TrendingUpIcon_ZapIcon_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Share\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 15\n                                        }, this),\n                                        activeTab === \"editor\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            size: \"sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BrainCircuitIcon_DatabaseIcon_HistoryIcon_PlayIcon_SaveIcon_ShareIcon_TrendingUpIcon_ZapIcon_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Run Query\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-hidden\",\n                        children: [\n                            activeTab === \"chat\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_chat_interface__WEBPACK_IMPORTED_MODULE_5__.ChatInterface, {\n                                onQueryGenerated: (query)=>{\n                                    setCurrentQuery(query);\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === \"editor\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full flex flex-col\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_editor_sql_editor__WEBPACK_IMPORTED_MODULE_6__.SQLEditor, {\n                                        value: currentQuery,\n                                        onChange: setCurrentQuery,\n                                        language: (currentDatabase === null || currentDatabase === void 0 ? void 0 : currentDatabase.databaseType) || \"postgresql\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === \"results\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_query_results__WEBPACK_IMPORTED_MODULE_8__.QueryResults, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === \"history\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_query_history__WEBPACK_IMPORTED_MODULE_9__.QueryHistory, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-80 border-l border-border bg-card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-border\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-semibold flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BrainCircuitIcon_DatabaseIcon_HistoryIcon_PlayIcon_SaveIcon_ShareIcon_TrendingUpIcon_ZapIcon_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this),\n                                \"AI Insights\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        className: \"pb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-sm\",\n                                            children: \"Query Suggestions\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"space-y-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"No active query to analyze. Start by describing what you want to query in the AI Assistant.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        className: \"pb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-sm\",\n                                            children: \"Performance Tips\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"space-y-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"Connect to a database to get performance recommendations.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        className: \"pb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-sm\",\n                                            children: \"Recent Activity\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"space-y-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"No recent queries found.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n_s(QueryWorkspace, \"JpwAvjrBl+GkUUfHVX1KgJuZ35g=\", false, function() {\n    return [\n        _stores_query_store__WEBPACK_IMPORTED_MODULE_10__.useQueryStore\n    ];\n});\n_c = QueryWorkspace;\nvar _c;\n$RefreshReg$(_c, \"QueryWorkspace\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/query-workspace.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/scroll-area.tsx":
/*!*******************************************!*\
  !*** ./src/components/ui/scroll-area.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ScrollArea: function() { return /* binding */ ScrollArea; },\n/* harmony export */   ScrollBar: function() { return /* binding */ ScrollBar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-scroll-area */ \"(app-pages-browser)/./node_modules/@radix-ui/react-scroll-area/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ScrollArea,ScrollBar auto */ \n\n\n\nconst ScrollArea = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, children, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative overflow-hidden\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Viewport, {\n                className: \"h-full w-full rounded-[inherit]\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/ui/scroll-area.tsx\",\n                lineNumber: 17,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScrollBar, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/ui/scroll-area.tsx\",\n                lineNumber: 20,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Corner, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/ui/scroll-area.tsx\",\n                lineNumber: 21,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/ui/scroll-area.tsx\",\n        lineNumber: 12,\n        columnNumber: 3\n    }, undefined);\n});\n_c1 = ScrollArea;\nScrollArea.displayName = _radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Root.displayName;\nconst ScrollBar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef((param, ref)=>{\n    let { className, orientation = \"vertical\", ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollAreaScrollbar, {\n        ref: ref,\n        orientation: orientation,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex touch-none select-none transition-colors\", orientation === \"vertical\" && \"h-full w-2.5 border-l border-l-transparent p-[1px]\", orientation === \"horizontal\" && \"h-2.5 flex-col border-t border-t-transparent p-[1px]\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollAreaThumb, {\n            className: \"relative flex-1 rounded-full bg-border\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/ui/scroll-area.tsx\",\n            lineNumber: 43,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/ui/scroll-area.tsx\",\n        lineNumber: 30,\n        columnNumber: 3\n    }, undefined);\n});\n_c2 = ScrollBar;\nScrollBar.displayName = _radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollAreaScrollbar.displayName;\n\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ScrollArea$React.forwardRef\");\n$RefreshReg$(_c1, \"ScrollArea\");\n$RefreshReg$(_c2, \"ScrollBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/scroll-area.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@radix-ui/number/dist/index.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@radix-ui/number/dist/index.mjs ***!
  \******************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clamp: function() { return /* binding */ clamp; }\n/* harmony export */ });\n// packages/core/number/src/number.ts\nfunction clamp(value, [min, max]) {\n  return Math.min(max, Math.max(min, value));\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvbnVtYmVyL2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUdFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9udW1iZXIvZGlzdC9pbmRleC5tanM/YmEzZCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9jb3JlL251bWJlci9zcmMvbnVtYmVyLnRzXG5mdW5jdGlvbiBjbGFtcCh2YWx1ZSwgW21pbiwgbWF4XSkge1xuICByZXR1cm4gTWF0aC5taW4obWF4LCBNYXRoLm1heChtaW4sIHZhbHVlKSk7XG59XG5leHBvcnQge1xuICBjbGFtcFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@radix-ui/number/dist/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@radix-ui/primitive/dist/index.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@radix-ui/primitive/dist/index.mjs ***!
  \*********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeEventHandlers: function() { return /* binding */ composeEventHandlers; }\n/* harmony export */ });\n// packages/core/primitive/src/primitive.tsx\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {\n  return function handleEvent(event) {\n    originalEventHandler?.(event);\n    if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcHJpbWl0aXZlL2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLHVFQUF1RSxrQ0FBa0MsSUFBSTtBQUM3RztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUdFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9wcmltaXRpdmUvZGlzdC9pbmRleC5tanM/MWFlNyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9jb3JlL3ByaW1pdGl2ZS9zcmMvcHJpbWl0aXZlLnRzeFxuZnVuY3Rpb24gY29tcG9zZUV2ZW50SGFuZGxlcnMob3JpZ2luYWxFdmVudEhhbmRsZXIsIG91ckV2ZW50SGFuZGxlciwgeyBjaGVja0ZvckRlZmF1bHRQcmV2ZW50ZWQgPSB0cnVlIH0gPSB7fSkge1xuICByZXR1cm4gZnVuY3Rpb24gaGFuZGxlRXZlbnQoZXZlbnQpIHtcbiAgICBvcmlnaW5hbEV2ZW50SGFuZGxlcj8uKGV2ZW50KTtcbiAgICBpZiAoY2hlY2tGb3JEZWZhdWx0UHJldmVudGVkID09PSBmYWxzZSB8fCAhZXZlbnQuZGVmYXVsdFByZXZlbnRlZCkge1xuICAgICAgcmV0dXJuIG91ckV2ZW50SGFuZGxlcj8uKGV2ZW50KTtcbiAgICB9XG4gIH07XG59XG5leHBvcnQge1xuICBjb21wb3NlRXZlbnRIYW5kbGVyc1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@radix-ui/primitive/dist/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@radix-ui/react-context/dist/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@radix-ui/react-context/dist/index.mjs ***!
  \*************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createContext: function() { return /* binding */ createContext2; },\n/* harmony export */   createContextScope: function() { return /* binding */ createContextScope; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n// packages/react/context/src/create-context.tsx\n\n\nfunction createContext2(rootComponentName, defaultContext) {\n  const Context = react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n  const Provider = (props) => {\n    const { children, ...context } = props;\n    const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => context, Object.values(context));\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value, children });\n  };\n  Provider.displayName = rootComponentName + \"Provider\";\n  function useContext2(consumerName) {\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== void 0) return defaultContext;\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n  return [Provider, useContext2];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n  let defaultContexts = [];\n  function createContext3(rootComponentName, defaultContext) {\n    const BaseContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n    const Provider = (props) => {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => context, Object.values(context));\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value, children });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName, scope) {\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== void 0) return defaultContext;\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [Provider, useContext2];\n  }\n  const createScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    });\n    return function useScope(scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n  createScope.scopeName = scopeName;\n  return [createContext3, composeContextScopes(createScope, ...createContextScopeDeps)];\n}\nfunction composeContextScopes(...scopes) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n  const createScope = () => {\n    const scopeHooks = scopes.map((createScope2) => ({\n      useScope: createScope2(),\n      scopeName: createScope2.scopeName\n    }));\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName }) => {\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes2, ...currentScope };\n      }, {});\n      return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@radix-ui/react-context/dist/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@radix-ui/react-direction/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-direction/dist/index.mjs ***!
  \***************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DirectionProvider: function() { return /* binding */ DirectionProvider; },\n/* harmony export */   Provider: function() { return /* binding */ Provider; },\n/* harmony export */   useDirection: function() { return /* binding */ useDirection; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n// packages/react/direction/src/direction.tsx\n\n\nvar DirectionContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(void 0);\nvar DirectionProvider = (props) => {\n  const { dir, children } = props;\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DirectionContext.Provider, { value: dir, children });\n};\nfunction useDirection(localDir) {\n  const globalDir = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DirectionContext);\n  return localDir || globalDir || \"ltr\";\n}\nvar Provider = DirectionProvider;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtZGlyZWN0aW9uL2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUE7QUFDK0I7QUFDUztBQUN4Qyx1QkFBdUIsZ0RBQW1CO0FBQzFDO0FBQ0EsVUFBVSxnQkFBZ0I7QUFDMUIseUJBQXlCLHNEQUFHLDhCQUE4QixzQkFBc0I7QUFDaEY7QUFDQTtBQUNBLG9CQUFvQiw2Q0FBZ0I7QUFDcEM7QUFDQTtBQUNBO0FBS0U7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWRpcmVjdGlvbi9kaXN0L2luZGV4Lm1qcz9lMzczIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL3JlYWN0L2RpcmVjdGlvbi9zcmMvZGlyZWN0aW9uLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyBqc3ggfSBmcm9tIFwicmVhY3QvanN4LXJ1bnRpbWVcIjtcbnZhciBEaXJlY3Rpb25Db250ZXh0ID0gUmVhY3QuY3JlYXRlQ29udGV4dCh2b2lkIDApO1xudmFyIERpcmVjdGlvblByb3ZpZGVyID0gKHByb3BzKSA9PiB7XG4gIGNvbnN0IHsgZGlyLCBjaGlsZHJlbiB9ID0gcHJvcHM7XG4gIHJldHVybiAvKiBAX19QVVJFX18gKi8ganN4KERpcmVjdGlvbkNvbnRleHQuUHJvdmlkZXIsIHsgdmFsdWU6IGRpciwgY2hpbGRyZW4gfSk7XG59O1xuZnVuY3Rpb24gdXNlRGlyZWN0aW9uKGxvY2FsRGlyKSB7XG4gIGNvbnN0IGdsb2JhbERpciA9IFJlYWN0LnVzZUNvbnRleHQoRGlyZWN0aW9uQ29udGV4dCk7XG4gIHJldHVybiBsb2NhbERpciB8fCBnbG9iYWxEaXIgfHwgXCJsdHJcIjtcbn1cbnZhciBQcm92aWRlciA9IERpcmVjdGlvblByb3ZpZGVyO1xuZXhwb3J0IHtcbiAgRGlyZWN0aW9uUHJvdmlkZXIsXG4gIFByb3ZpZGVyLFxuICB1c2VEaXJlY3Rpb25cbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@radix-ui/react-direction/dist/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@radix-ui/react-presence/dist/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@radix-ui/react-presence/dist/index.mjs ***!
  \**************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Presence: function() { return /* binding */ Presence; },\n/* harmony export */   Root: function() { return /* binding */ Root; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(app-pages-browser)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(app-pages-browser)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n\"use client\";\n\n// src/presence.tsx\n\n\n\n\n// src/use-state-machine.tsx\n\nfunction useStateMachine(initialState, machine) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useReducer((state, event) => {\n    const nextState = machine[state][event];\n    return nextState ?? state;\n  }, initialState);\n}\n\n// src/presence.tsx\nvar Presence = (props) => {\n  const { present, children } = props;\n  const presence = usePresence(present);\n  const child = typeof children === \"function\" ? children({ present: presence.isPresent }) : react__WEBPACK_IMPORTED_MODULE_0__.Children.only(children);\n  const ref = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__.useComposedRefs)(presence.ref, getElementRef(child));\n  const forceMount = typeof children === \"function\";\n  return forceMount || presence.isPresent ? react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(child, { ref }) : null;\n};\nPresence.displayName = \"Presence\";\nfunction usePresence(present) {\n  const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n  const stylesRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  const prevPresentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(present);\n  const prevAnimationNameRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"none\");\n  const initialState = present ? \"mounted\" : \"unmounted\";\n  const [state, send] = useStateMachine(initialState, {\n    mounted: {\n      UNMOUNT: \"unmounted\",\n      ANIMATION_OUT: \"unmountSuspended\"\n    },\n    unmountSuspended: {\n      MOUNT: \"mounted\",\n      ANIMATION_END: \"unmounted\"\n    },\n    unmounted: {\n      MOUNT: \"mounted\"\n    }\n  });\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    const currentAnimationName = getAnimationName(stylesRef.current);\n    prevAnimationNameRef.current = state === \"mounted\" ? currentAnimationName : \"none\";\n  }, [state]);\n  (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)(() => {\n    const styles = stylesRef.current;\n    const wasPresent = prevPresentRef.current;\n    const hasPresentChanged = wasPresent !== present;\n    if (hasPresentChanged) {\n      const prevAnimationName = prevAnimationNameRef.current;\n      const currentAnimationName = getAnimationName(styles);\n      if (present) {\n        send(\"MOUNT\");\n      } else if (currentAnimationName === \"none\" || styles?.display === \"none\") {\n        send(\"UNMOUNT\");\n      } else {\n        const isAnimating = prevAnimationName !== currentAnimationName;\n        if (wasPresent && isAnimating) {\n          send(\"ANIMATION_OUT\");\n        } else {\n          send(\"UNMOUNT\");\n        }\n      }\n      prevPresentRef.current = present;\n    }\n  }, [present, send]);\n  (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)(() => {\n    if (node) {\n      let timeoutId;\n      const ownerWindow = node.ownerDocument.defaultView ?? window;\n      const handleAnimationEnd = (event) => {\n        const currentAnimationName = getAnimationName(stylesRef.current);\n        const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n        if (event.target === node && isCurrentAnimation) {\n          send(\"ANIMATION_END\");\n          if (!prevPresentRef.current) {\n            const currentFillMode = node.style.animationFillMode;\n            node.style.animationFillMode = \"forwards\";\n            timeoutId = ownerWindow.setTimeout(() => {\n              if (node.style.animationFillMode === \"forwards\") {\n                node.style.animationFillMode = currentFillMode;\n              }\n            });\n          }\n        }\n      };\n      const handleAnimationStart = (event) => {\n        if (event.target === node) {\n          prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n        }\n      };\n      node.addEventListener(\"animationstart\", handleAnimationStart);\n      node.addEventListener(\"animationcancel\", handleAnimationEnd);\n      node.addEventListener(\"animationend\", handleAnimationEnd);\n      return () => {\n        ownerWindow.clearTimeout(timeoutId);\n        node.removeEventListener(\"animationstart\", handleAnimationStart);\n        node.removeEventListener(\"animationcancel\", handleAnimationEnd);\n        node.removeEventListener(\"animationend\", handleAnimationEnd);\n      };\n    } else {\n      send(\"ANIMATION_END\");\n    }\n  }, [node, send]);\n  return {\n    isPresent: [\"mounted\", \"unmountSuspended\"].includes(state),\n    ref: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node2) => {\n      stylesRef.current = node2 ? getComputedStyle(node2) : null;\n      setNode(node2);\n    }, [])\n  };\n}\nfunction getAnimationName(styles) {\n  return styles?.animationName || \"none\";\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\nvar Root = Presence;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@radix-ui/react-presence/dist/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@radix-ui/react-primitive/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-primitive/dist/index.mjs ***!
  \***************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Primitive: function() { return /* binding */ Primitive; },\n/* harmony export */   Root: function() { return /* binding */ Root; },\n/* harmony export */   dispatchDiscreteCustomEvent: function() { return /* binding */ dispatchDiscreteCustomEvent; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(app-pages-browser)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n// src/primitive.tsx\n\n\n\n\nvar NODES = [\n  \"a\",\n  \"button\",\n  \"div\",\n  \"form\",\n  \"h2\",\n  \"h3\",\n  \"img\",\n  \"input\",\n  \"label\",\n  \"li\",\n  \"nav\",\n  \"ol\",\n  \"p\",\n  \"select\",\n  \"span\",\n  \"svg\",\n  \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node) => {\n  const Slot = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(`Primitive.${node}`);\n  const Node = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp = asChild ? Slot : node;\n    if (typeof window !== \"undefined\") {\n      window[Symbol.for(\"radix-ui\")] = true;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Comp, { ...primitiveProps, ref: forwardedRef });\n  });\n  Node.displayName = `Primitive.${node}`;\n  return { ...primitive, [node]: Node };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n  if (target) react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(() => target.dispatchEvent(event));\n}\nvar Root = Primitive;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@radix-ui/react-scroll-area/dist/index.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/@radix-ui/react-scroll-area/dist/index.mjs ***!
  \*****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Corner: function() { return /* binding */ Corner; },\n/* harmony export */   Root: function() { return /* binding */ Root; },\n/* harmony export */   ScrollArea: function() { return /* binding */ ScrollArea; },\n/* harmony export */   ScrollAreaCorner: function() { return /* binding */ ScrollAreaCorner; },\n/* harmony export */   ScrollAreaScrollbar: function() { return /* binding */ ScrollAreaScrollbar; },\n/* harmony export */   ScrollAreaThumb: function() { return /* binding */ ScrollAreaThumb; },\n/* harmony export */   ScrollAreaViewport: function() { return /* binding */ ScrollAreaViewport; },\n/* harmony export */   Scrollbar: function() { return /* binding */ Scrollbar; },\n/* harmony export */   Thumb: function() { return /* binding */ Thumb; },\n/* harmony export */   Viewport: function() { return /* binding */ Viewport; },\n/* harmony export */   createScrollAreaScope: function() { return /* binding */ createScrollAreaScope; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(app-pages-browser)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(app-pages-browser)/./node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(app-pages-browser)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(app-pages-browser)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(app-pages-browser)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(app-pages-browser)/./node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(app-pages-browser)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_number__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/number */ \"(app-pages-browser)/./node_modules/@radix-ui/number/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/primitive */ \"(app-pages-browser)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n\"use client\";\n\n// src/scroll-area.tsx\n\n\n\n\n\n\n\n\n\n\n\n// src/use-state-machine.ts\n\nfunction useStateMachine(initialState, machine) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useReducer((state, event) => {\n    const nextState = machine[state][event];\n    return nextState ?? state;\n  }, initialState);\n}\n\n// src/scroll-area.tsx\n\nvar SCROLL_AREA_NAME = \"ScrollArea\";\nvar [createScrollAreaContext, createScrollAreaScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(SCROLL_AREA_NAME);\nvar [ScrollAreaProvider, useScrollAreaContext] = createScrollAreaContext(SCROLL_AREA_NAME);\nvar ScrollArea = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeScrollArea,\n      type = \"hover\",\n      dir,\n      scrollHideDelay = 600,\n      ...scrollAreaProps\n    } = props;\n    const [scrollArea, setScrollArea] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [viewport, setViewport] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [scrollbarX, setScrollbarX] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [scrollbarY, setScrollbarY] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [cornerWidth, setCornerWidth] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [cornerHeight, setCornerHeight] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [scrollbarXEnabled, setScrollbarXEnabled] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [scrollbarYEnabled, setScrollbarYEnabled] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node) => setScrollArea(node));\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_4__.useDirection)(dir);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      ScrollAreaProvider,\n      {\n        scope: __scopeScrollArea,\n        type,\n        dir: direction,\n        scrollHideDelay,\n        scrollArea,\n        viewport,\n        onViewportChange: setViewport,\n        content,\n        onContentChange: setContent,\n        scrollbarX,\n        onScrollbarXChange: setScrollbarX,\n        scrollbarXEnabled,\n        onScrollbarXEnabledChange: setScrollbarXEnabled,\n        scrollbarY,\n        onScrollbarYChange: setScrollbarY,\n        scrollbarYEnabled,\n        onScrollbarYEnabledChange: setScrollbarYEnabled,\n        onCornerWidthChange: setCornerWidth,\n        onCornerHeightChange: setCornerHeight,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n          _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div,\n          {\n            dir: direction,\n            ...scrollAreaProps,\n            ref: composedRefs,\n            style: {\n              position: \"relative\",\n              // Pass corner sizes as CSS vars to reduce re-renders of context consumers\n              [\"--radix-scroll-area-corner-width\"]: cornerWidth + \"px\",\n              [\"--radix-scroll-area-corner-height\"]: cornerHeight + \"px\",\n              ...props.style\n            }\n          }\n        )\n      }\n    );\n  }\n);\nScrollArea.displayName = SCROLL_AREA_NAME;\nvar VIEWPORT_NAME = \"ScrollAreaViewport\";\nvar ScrollAreaViewport = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeScrollArea, children, nonce, ...viewportProps } = props;\n    const context = useScrollAreaContext(VIEWPORT_NAME, __scopeScrollArea);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref, context.onViewportChange);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, { children: [\n      /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n        \"style\",\n        {\n          dangerouslySetInnerHTML: {\n            __html: `[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}`\n          },\n          nonce\n        }\n      ),\n      /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n        _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div,\n        {\n          \"data-radix-scroll-area-viewport\": \"\",\n          ...viewportProps,\n          ref: composedRefs,\n          style: {\n            /**\n             * We don't support `visible` because the intention is to have at least one scrollbar\n             * if this component is used and `visible` will behave like `auto` in that case\n             * https://developer.mozilla.org/en-US/docs/Web/CSS/overflow#description\n             *\n             * We don't handle `auto` because the intention is for the native implementation\n             * to be hidden if using this component. We just want to ensure the node is scrollable\n             * so could have used either `scroll` or `auto` here. We picked `scroll` to prevent\n             * the browser from having to work out whether to render native scrollbars or not,\n             * we tell it to with the intention of hiding them in CSS.\n             */\n            overflowX: context.scrollbarXEnabled ? \"scroll\" : \"hidden\",\n            overflowY: context.scrollbarYEnabled ? \"scroll\" : \"hidden\",\n            ...props.style\n          },\n          children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", { ref: context.onContentChange, style: { minWidth: \"100%\", display: \"table\" }, children })\n        }\n      )\n    ] });\n  }\n);\nScrollAreaViewport.displayName = VIEWPORT_NAME;\nvar SCROLLBAR_NAME = \"ScrollAreaScrollbar\";\nvar ScrollAreaScrollbar = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const { forceMount, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const { onScrollbarXEnabledChange, onScrollbarYEnabledChange } = context;\n    const isHorizontal = props.orientation === \"horizontal\";\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      isHorizontal ? onScrollbarXEnabledChange(true) : onScrollbarYEnabledChange(true);\n      return () => {\n        isHorizontal ? onScrollbarXEnabledChange(false) : onScrollbarYEnabledChange(false);\n      };\n    }, [isHorizontal, onScrollbarXEnabledChange, onScrollbarYEnabledChange]);\n    return context.type === \"hover\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarHover, { ...scrollbarProps, ref: forwardedRef, forceMount }) : context.type === \"scroll\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarScroll, { ...scrollbarProps, ref: forwardedRef, forceMount }) : context.type === \"auto\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarAuto, { ...scrollbarProps, ref: forwardedRef, forceMount }) : context.type === \"always\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarVisible, { ...scrollbarProps, ref: forwardedRef }) : null;\n  }\n);\nScrollAreaScrollbar.displayName = SCROLLBAR_NAME;\nvar ScrollAreaScrollbarHover = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { forceMount, ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const [visible, setVisible] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    const scrollArea = context.scrollArea;\n    let hideTimer = 0;\n    if (scrollArea) {\n      const handlePointerEnter = () => {\n        window.clearTimeout(hideTimer);\n        setVisible(true);\n      };\n      const handlePointerLeave = () => {\n        hideTimer = window.setTimeout(() => setVisible(false), context.scrollHideDelay);\n      };\n      scrollArea.addEventListener(\"pointerenter\", handlePointerEnter);\n      scrollArea.addEventListener(\"pointerleave\", handlePointerLeave);\n      return () => {\n        window.clearTimeout(hideTimer);\n        scrollArea.removeEventListener(\"pointerenter\", handlePointerEnter);\n        scrollArea.removeEventListener(\"pointerleave\", handlePointerLeave);\n      };\n    }\n  }, [context.scrollArea, context.scrollHideDelay]);\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, { present: forceMount || visible, children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n    ScrollAreaScrollbarAuto,\n    {\n      \"data-state\": visible ? \"visible\" : \"hidden\",\n      ...scrollbarProps,\n      ref: forwardedRef\n    }\n  ) });\n});\nvar ScrollAreaScrollbarScroll = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { forceMount, ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const isHorizontal = props.orientation === \"horizontal\";\n  const debounceScrollEnd = useDebounceCallback(() => send(\"SCROLL_END\"), 100);\n  const [state, send] = useStateMachine(\"hidden\", {\n    hidden: {\n      SCROLL: \"scrolling\"\n    },\n    scrolling: {\n      SCROLL_END: \"idle\",\n      POINTER_ENTER: \"interacting\"\n    },\n    interacting: {\n      SCROLL: \"interacting\",\n      POINTER_LEAVE: \"idle\"\n    },\n    idle: {\n      HIDE: \"hidden\",\n      SCROLL: \"scrolling\",\n      POINTER_ENTER: \"interacting\"\n    }\n  });\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (state === \"idle\") {\n      const hideTimer = window.setTimeout(() => send(\"HIDE\"), context.scrollHideDelay);\n      return () => window.clearTimeout(hideTimer);\n    }\n  }, [state, context.scrollHideDelay, send]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    const viewport = context.viewport;\n    const scrollDirection = isHorizontal ? \"scrollLeft\" : \"scrollTop\";\n    if (viewport) {\n      let prevScrollPos = viewport[scrollDirection];\n      const handleScroll = () => {\n        const scrollPos = viewport[scrollDirection];\n        const hasScrollInDirectionChanged = prevScrollPos !== scrollPos;\n        if (hasScrollInDirectionChanged) {\n          send(\"SCROLL\");\n          debounceScrollEnd();\n        }\n        prevScrollPos = scrollPos;\n      };\n      viewport.addEventListener(\"scroll\", handleScroll);\n      return () => viewport.removeEventListener(\"scroll\", handleScroll);\n    }\n  }, [context.viewport, isHorizontal, send, debounceScrollEnd]);\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, { present: forceMount || state !== \"hidden\", children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n    ScrollAreaScrollbarVisible,\n    {\n      \"data-state\": state === \"hidden\" ? \"hidden\" : \"visible\",\n      ...scrollbarProps,\n      ref: forwardedRef,\n      onPointerEnter: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerEnter, () => send(\"POINTER_ENTER\")),\n      onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerLeave, () => send(\"POINTER_LEAVE\"))\n    }\n  ) });\n});\nvar ScrollAreaScrollbarAuto = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const { forceMount, ...scrollbarProps } = props;\n  const [visible, setVisible] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n  const isHorizontal = props.orientation === \"horizontal\";\n  const handleResize = useDebounceCallback(() => {\n    if (context.viewport) {\n      const isOverflowX = context.viewport.offsetWidth < context.viewport.scrollWidth;\n      const isOverflowY = context.viewport.offsetHeight < context.viewport.scrollHeight;\n      setVisible(isHorizontal ? isOverflowX : isOverflowY);\n    }\n  }, 10);\n  useResizeObserver(context.viewport, handleResize);\n  useResizeObserver(context.content, handleResize);\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, { present: forceMount || visible, children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n    ScrollAreaScrollbarVisible,\n    {\n      \"data-state\": visible ? \"visible\" : \"hidden\",\n      ...scrollbarProps,\n      ref: forwardedRef\n    }\n  ) });\n});\nvar ScrollAreaScrollbarVisible = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { orientation = \"vertical\", ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const thumbRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  const pointerOffsetRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n  const [sizes, setSizes] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n    content: 0,\n    viewport: 0,\n    scrollbar: { size: 0, paddingStart: 0, paddingEnd: 0 }\n  });\n  const thumbRatio = getThumbRatio(sizes.viewport, sizes.content);\n  const commonProps = {\n    ...scrollbarProps,\n    sizes,\n    onSizesChange: setSizes,\n    hasThumb: Boolean(thumbRatio > 0 && thumbRatio < 1),\n    onThumbChange: (thumb) => thumbRef.current = thumb,\n    onThumbPointerUp: () => pointerOffsetRef.current = 0,\n    onThumbPointerDown: (pointerPos) => pointerOffsetRef.current = pointerPos\n  };\n  function getScrollPosition(pointerPos, dir) {\n    return getScrollPositionFromPointer(pointerPos, pointerOffsetRef.current, sizes, dir);\n  }\n  if (orientation === \"horizontal\") {\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      ScrollAreaScrollbarX,\n      {\n        ...commonProps,\n        ref: forwardedRef,\n        onThumbPositionChange: () => {\n          if (context.viewport && thumbRef.current) {\n            const scrollPos = context.viewport.scrollLeft;\n            const offset = getThumbOffsetFromScroll(scrollPos, sizes, context.dir);\n            thumbRef.current.style.transform = `translate3d(${offset}px, 0, 0)`;\n          }\n        },\n        onWheelScroll: (scrollPos) => {\n          if (context.viewport) context.viewport.scrollLeft = scrollPos;\n        },\n        onDragScroll: (pointerPos) => {\n          if (context.viewport) {\n            context.viewport.scrollLeft = getScrollPosition(pointerPos, context.dir);\n          }\n        }\n      }\n    );\n  }\n  if (orientation === \"vertical\") {\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      ScrollAreaScrollbarY,\n      {\n        ...commonProps,\n        ref: forwardedRef,\n        onThumbPositionChange: () => {\n          if (context.viewport && thumbRef.current) {\n            const scrollPos = context.viewport.scrollTop;\n            const offset = getThumbOffsetFromScroll(scrollPos, sizes);\n            thumbRef.current.style.transform = `translate3d(0, ${offset}px, 0)`;\n          }\n        },\n        onWheelScroll: (scrollPos) => {\n          if (context.viewport) context.viewport.scrollTop = scrollPos;\n        },\n        onDragScroll: (pointerPos) => {\n          if (context.viewport) context.viewport.scrollTop = getScrollPosition(pointerPos);\n        }\n      }\n    );\n  }\n  return null;\n});\nvar ScrollAreaScrollbarX = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { sizes, onSizesChange, ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const [computedStyle, setComputedStyle] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n  const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref, context.onScrollbarXChange);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (ref.current) setComputedStyle(getComputedStyle(ref.current));\n  }, [ref]);\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n    ScrollAreaScrollbarImpl,\n    {\n      \"data-orientation\": \"horizontal\",\n      ...scrollbarProps,\n      ref: composeRefs,\n      sizes,\n      style: {\n        bottom: 0,\n        left: context.dir === \"rtl\" ? \"var(--radix-scroll-area-corner-width)\" : 0,\n        right: context.dir === \"ltr\" ? \"var(--radix-scroll-area-corner-width)\" : 0,\n        [\"--radix-scroll-area-thumb-width\"]: getThumbSize(sizes) + \"px\",\n        ...props.style\n      },\n      onThumbPointerDown: (pointerPos) => props.onThumbPointerDown(pointerPos.x),\n      onDragScroll: (pointerPos) => props.onDragScroll(pointerPos.x),\n      onWheelScroll: (event, maxScrollPos) => {\n        if (context.viewport) {\n          const scrollPos = context.viewport.scrollLeft + event.deltaX;\n          props.onWheelScroll(scrollPos);\n          if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {\n            event.preventDefault();\n          }\n        }\n      },\n      onResize: () => {\n        if (ref.current && context.viewport && computedStyle) {\n          onSizesChange({\n            content: context.viewport.scrollWidth,\n            viewport: context.viewport.offsetWidth,\n            scrollbar: {\n              size: ref.current.clientWidth,\n              paddingStart: toInt(computedStyle.paddingLeft),\n              paddingEnd: toInt(computedStyle.paddingRight)\n            }\n          });\n        }\n      }\n    }\n  );\n});\nvar ScrollAreaScrollbarY = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { sizes, onSizesChange, ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const [computedStyle, setComputedStyle] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n  const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref, context.onScrollbarYChange);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (ref.current) setComputedStyle(getComputedStyle(ref.current));\n  }, [ref]);\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n    ScrollAreaScrollbarImpl,\n    {\n      \"data-orientation\": \"vertical\",\n      ...scrollbarProps,\n      ref: composeRefs,\n      sizes,\n      style: {\n        top: 0,\n        right: context.dir === \"ltr\" ? 0 : void 0,\n        left: context.dir === \"rtl\" ? 0 : void 0,\n        bottom: \"var(--radix-scroll-area-corner-height)\",\n        [\"--radix-scroll-area-thumb-height\"]: getThumbSize(sizes) + \"px\",\n        ...props.style\n      },\n      onThumbPointerDown: (pointerPos) => props.onThumbPointerDown(pointerPos.y),\n      onDragScroll: (pointerPos) => props.onDragScroll(pointerPos.y),\n      onWheelScroll: (event, maxScrollPos) => {\n        if (context.viewport) {\n          const scrollPos = context.viewport.scrollTop + event.deltaY;\n          props.onWheelScroll(scrollPos);\n          if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {\n            event.preventDefault();\n          }\n        }\n      },\n      onResize: () => {\n        if (ref.current && context.viewport && computedStyle) {\n          onSizesChange({\n            content: context.viewport.scrollHeight,\n            viewport: context.viewport.offsetHeight,\n            scrollbar: {\n              size: ref.current.clientHeight,\n              paddingStart: toInt(computedStyle.paddingTop),\n              paddingEnd: toInt(computedStyle.paddingBottom)\n            }\n          });\n        }\n      }\n    }\n  );\n});\nvar [ScrollbarProvider, useScrollbarContext] = createScrollAreaContext(SCROLLBAR_NAME);\nvar ScrollAreaScrollbarImpl = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const {\n    __scopeScrollArea,\n    sizes,\n    hasThumb,\n    onThumbChange,\n    onThumbPointerUp,\n    onThumbPointerDown,\n    onThumbPositionChange,\n    onDragScroll,\n    onWheelScroll,\n    onResize,\n    ...scrollbarProps\n  } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, __scopeScrollArea);\n  const [scrollbar, setScrollbar] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n  const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node) => setScrollbar(node));\n  const rectRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  const prevWebkitUserSelectRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"\");\n  const viewport = context.viewport;\n  const maxScrollPos = sizes.content - sizes.viewport;\n  const handleWheelScroll = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onWheelScroll);\n  const handleThumbPositionChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbPositionChange);\n  const handleResize = useDebounceCallback(onResize, 10);\n  function handleDragScroll(event) {\n    if (rectRef.current) {\n      const x = event.clientX - rectRef.current.left;\n      const y = event.clientY - rectRef.current.top;\n      onDragScroll({ x, y });\n    }\n  }\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    const handleWheel = (event) => {\n      const element = event.target;\n      const isScrollbarWheel = scrollbar?.contains(element);\n      if (isScrollbarWheel) handleWheelScroll(event, maxScrollPos);\n    };\n    document.addEventListener(\"wheel\", handleWheel, { passive: false });\n    return () => document.removeEventListener(\"wheel\", handleWheel, { passive: false });\n  }, [viewport, scrollbar, maxScrollPos, handleWheelScroll]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(handleThumbPositionChange, [sizes, handleThumbPositionChange]);\n  useResizeObserver(scrollbar, handleResize);\n  useResizeObserver(context.content, handleResize);\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n    ScrollbarProvider,\n    {\n      scope: __scopeScrollArea,\n      scrollbar,\n      hasThumb,\n      onThumbChange: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbChange),\n      onThumbPointerUp: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbPointerUp),\n      onThumbPositionChange: handleThumbPositionChange,\n      onThumbPointerDown: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbPointerDown),\n      children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n        _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div,\n        {\n          ...scrollbarProps,\n          ref: composeRefs,\n          style: { position: \"absolute\", ...scrollbarProps.style },\n          onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerDown, (event) => {\n            const mainPointer = 0;\n            if (event.button === mainPointer) {\n              const element = event.target;\n              element.setPointerCapture(event.pointerId);\n              rectRef.current = scrollbar.getBoundingClientRect();\n              prevWebkitUserSelectRef.current = document.body.style.webkitUserSelect;\n              document.body.style.webkitUserSelect = \"none\";\n              if (context.viewport) context.viewport.style.scrollBehavior = \"auto\";\n              handleDragScroll(event);\n            }\n          }),\n          onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerMove, handleDragScroll),\n          onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerUp, (event) => {\n            const element = event.target;\n            if (element.hasPointerCapture(event.pointerId)) {\n              element.releasePointerCapture(event.pointerId);\n            }\n            document.body.style.webkitUserSelect = prevWebkitUserSelectRef.current;\n            if (context.viewport) context.viewport.style.scrollBehavior = \"\";\n            rectRef.current = null;\n          })\n        }\n      )\n    }\n  );\n});\nvar THUMB_NAME = \"ScrollAreaThumb\";\nvar ScrollAreaThumb = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const { forceMount, ...thumbProps } = props;\n    const scrollbarContext = useScrollbarContext(THUMB_NAME, props.__scopeScrollArea);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, { present: forceMount || scrollbarContext.hasThumb, children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaThumbImpl, { ref: forwardedRef, ...thumbProps }) });\n  }\n);\nvar ScrollAreaThumbImpl = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeScrollArea, style, ...thumbProps } = props;\n    const scrollAreaContext = useScrollAreaContext(THUMB_NAME, __scopeScrollArea);\n    const scrollbarContext = useScrollbarContext(THUMB_NAME, __scopeScrollArea);\n    const { onThumbPositionChange } = scrollbarContext;\n    const composedRef = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(\n      forwardedRef,\n      (node) => scrollbarContext.onThumbChange(node)\n    );\n    const removeUnlinkedScrollListenerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(void 0);\n    const debounceScrollEnd = useDebounceCallback(() => {\n      if (removeUnlinkedScrollListenerRef.current) {\n        removeUnlinkedScrollListenerRef.current();\n        removeUnlinkedScrollListenerRef.current = void 0;\n      }\n    }, 100);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      const viewport = scrollAreaContext.viewport;\n      if (viewport) {\n        const handleScroll = () => {\n          debounceScrollEnd();\n          if (!removeUnlinkedScrollListenerRef.current) {\n            const listener = addUnlinkedScrollListener(viewport, onThumbPositionChange);\n            removeUnlinkedScrollListenerRef.current = listener;\n            onThumbPositionChange();\n          }\n        };\n        onThumbPositionChange();\n        viewport.addEventListener(\"scroll\", handleScroll);\n        return () => viewport.removeEventListener(\"scroll\", handleScroll);\n      }\n    }, [scrollAreaContext.viewport, debounceScrollEnd, onThumbPositionChange]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div,\n      {\n        \"data-state\": scrollbarContext.hasThumb ? \"visible\" : \"hidden\",\n        ...thumbProps,\n        ref: composedRef,\n        style: {\n          width: \"var(--radix-scroll-area-thumb-width)\",\n          height: \"var(--radix-scroll-area-thumb-height)\",\n          ...style\n        },\n        onPointerDownCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerDownCapture, (event) => {\n          const thumb = event.target;\n          const thumbRect = thumb.getBoundingClientRect();\n          const x = event.clientX - thumbRect.left;\n          const y = event.clientY - thumbRect.top;\n          scrollbarContext.onThumbPointerDown({ x, y });\n        }),\n        onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerUp, scrollbarContext.onThumbPointerUp)\n      }\n    );\n  }\n);\nScrollAreaThumb.displayName = THUMB_NAME;\nvar CORNER_NAME = \"ScrollAreaCorner\";\nvar ScrollAreaCorner = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const context = useScrollAreaContext(CORNER_NAME, props.__scopeScrollArea);\n    const hasBothScrollbarsVisible = Boolean(context.scrollbarX && context.scrollbarY);\n    const hasCorner = context.type !== \"scroll\" && hasBothScrollbarsVisible;\n    return hasCorner ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaCornerImpl, { ...props, ref: forwardedRef }) : null;\n  }\n);\nScrollAreaCorner.displayName = CORNER_NAME;\nvar ScrollAreaCornerImpl = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { __scopeScrollArea, ...cornerProps } = props;\n  const context = useScrollAreaContext(CORNER_NAME, __scopeScrollArea);\n  const [width, setWidth] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n  const [height, setHeight] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n  const hasSize = Boolean(width && height);\n  useResizeObserver(context.scrollbarX, () => {\n    const height2 = context.scrollbarX?.offsetHeight || 0;\n    context.onCornerHeightChange(height2);\n    setHeight(height2);\n  });\n  useResizeObserver(context.scrollbarY, () => {\n    const width2 = context.scrollbarY?.offsetWidth || 0;\n    context.onCornerWidthChange(width2);\n    setWidth(width2);\n  });\n  return hasSize ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n    _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div,\n    {\n      ...cornerProps,\n      ref: forwardedRef,\n      style: {\n        width,\n        height,\n        position: \"absolute\",\n        right: context.dir === \"ltr\" ? 0 : void 0,\n        left: context.dir === \"rtl\" ? 0 : void 0,\n        bottom: 0,\n        ...props.style\n      }\n    }\n  ) : null;\n});\nfunction toInt(value) {\n  return value ? parseInt(value, 10) : 0;\n}\nfunction getThumbRatio(viewportSize, contentSize) {\n  const ratio = viewportSize / contentSize;\n  return isNaN(ratio) ? 0 : ratio;\n}\nfunction getThumbSize(sizes) {\n  const ratio = getThumbRatio(sizes.viewport, sizes.content);\n  const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n  const thumbSize = (sizes.scrollbar.size - scrollbarPadding) * ratio;\n  return Math.max(thumbSize, 18);\n}\nfunction getScrollPositionFromPointer(pointerPos, pointerOffset, sizes, dir = \"ltr\") {\n  const thumbSizePx = getThumbSize(sizes);\n  const thumbCenter = thumbSizePx / 2;\n  const offset = pointerOffset || thumbCenter;\n  const thumbOffsetFromEnd = thumbSizePx - offset;\n  const minPointerPos = sizes.scrollbar.paddingStart + offset;\n  const maxPointerPos = sizes.scrollbar.size - sizes.scrollbar.paddingEnd - thumbOffsetFromEnd;\n  const maxScrollPos = sizes.content - sizes.viewport;\n  const scrollRange = dir === \"ltr\" ? [0, maxScrollPos] : [maxScrollPos * -1, 0];\n  const interpolate = linearScale([minPointerPos, maxPointerPos], scrollRange);\n  return interpolate(pointerPos);\n}\nfunction getThumbOffsetFromScroll(scrollPos, sizes, dir = \"ltr\") {\n  const thumbSizePx = getThumbSize(sizes);\n  const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n  const scrollbar = sizes.scrollbar.size - scrollbarPadding;\n  const maxScrollPos = sizes.content - sizes.viewport;\n  const maxThumbPos = scrollbar - thumbSizePx;\n  const scrollClampRange = dir === \"ltr\" ? [0, maxScrollPos] : [maxScrollPos * -1, 0];\n  const scrollWithoutMomentum = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_9__.clamp)(scrollPos, scrollClampRange);\n  const interpolate = linearScale([0, maxScrollPos], [0, maxThumbPos]);\n  return interpolate(scrollWithoutMomentum);\n}\nfunction linearScale(input, output) {\n  return (value) => {\n    if (input[0] === input[1] || output[0] === output[1]) return output[0];\n    const ratio = (output[1] - output[0]) / (input[1] - input[0]);\n    return output[0] + ratio * (value - input[0]);\n  };\n}\nfunction isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos) {\n  return scrollPos > 0 && scrollPos < maxScrollPos;\n}\nvar addUnlinkedScrollListener = (node, handler = () => {\n}) => {\n  let prevPosition = { left: node.scrollLeft, top: node.scrollTop };\n  let rAF = 0;\n  (function loop() {\n    const position = { left: node.scrollLeft, top: node.scrollTop };\n    const isHorizontalScroll = prevPosition.left !== position.left;\n    const isVerticalScroll = prevPosition.top !== position.top;\n    if (isHorizontalScroll || isVerticalScroll) handler();\n    prevPosition = position;\n    rAF = window.requestAnimationFrame(loop);\n  })();\n  return () => window.cancelAnimationFrame(rAF);\n};\nfunction useDebounceCallback(callback, delay) {\n  const handleCallback = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(callback);\n  const debounceTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => () => window.clearTimeout(debounceTimerRef.current), []);\n  return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n    window.clearTimeout(debounceTimerRef.current);\n    debounceTimerRef.current = window.setTimeout(handleCallback, delay);\n  }, [handleCallback, delay]);\n}\nfunction useResizeObserver(element, onResize) {\n  const handleResize = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onResize);\n  (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_10__.useLayoutEffect)(() => {\n    let rAF = 0;\n    if (element) {\n      const resizeObserver = new ResizeObserver(() => {\n        cancelAnimationFrame(rAF);\n        rAF = window.requestAnimationFrame(handleResize);\n      });\n      resizeObserver.observe(element);\n      return () => {\n        window.cancelAnimationFrame(rAF);\n        resizeObserver.unobserve(element);\n      };\n    }\n  }, [element, handleResize]);\n}\nvar Root = ScrollArea;\nvar Viewport = ScrollAreaViewport;\nvar Scrollbar = ScrollAreaScrollbar;\nvar Thumb = ScrollAreaThumb;\nvar Corner = ScrollAreaCorner;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@radix-ui/react-scroll-area/dist/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs ***!
  \**********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCallbackRef: function() { return /* binding */ useCallbackRef; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n// packages/react/use-callback-ref/src/use-callback-ref.tsx\n\nfunction useCallbackRef(callback) {\n  const callbackRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(callback);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    callbackRef.current = callback;\n  });\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => (...args) => callbackRef.current?.(...args), []);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtdXNlLWNhbGxiYWNrLXJlZi9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQytCO0FBQy9CO0FBQ0Esc0JBQXNCLHlDQUFZO0FBQ2xDLEVBQUUsNENBQWU7QUFDakI7QUFDQSxHQUFHO0FBQ0gsU0FBUywwQ0FBYTtBQUN0QjtBQUdFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC11c2UtY2FsbGJhY2stcmVmL2Rpc3QvaW5kZXgubWpzP2E1YTMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvdXNlLWNhbGxiYWNrLXJlZi9zcmMvdXNlLWNhbGxiYWNrLXJlZi50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuZnVuY3Rpb24gdXNlQ2FsbGJhY2tSZWYoY2FsbGJhY2spIHtcbiAgY29uc3QgY2FsbGJhY2tSZWYgPSBSZWFjdC51c2VSZWYoY2FsbGJhY2spO1xuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNhbGxiYWNrUmVmLmN1cnJlbnQgPSBjYWxsYmFjaztcbiAgfSk7XG4gIHJldHVybiBSZWFjdC51c2VNZW1vKCgpID0+ICguLi5hcmdzKSA9PiBjYWxsYmFja1JlZi5jdXJyZW50Py4oLi4uYXJncyksIFtdKTtcbn1cbmV4cG9ydCB7XG4gIHVzZUNhbGxiYWNrUmVmXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs ***!
  \***********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLayoutEffect: function() { return /* binding */ useLayoutEffect2; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n// packages/react/use-layout-effect/src/use-layout-effect.tsx\n\nvar useLayoutEffect2 = globalThis?.document ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : () => {\n};\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtdXNlLWxheW91dC1lZmZlY3QvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUMrQjtBQUMvQiw4Q0FBOEMsa0RBQXFCO0FBQ25FO0FBR0U7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0L2Rpc3QvaW5kZXgubWpzP2FlNGYiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvdXNlLWxheW91dC1lZmZlY3Qvc3JjL3VzZS1sYXlvdXQtZWZmZWN0LnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG52YXIgdXNlTGF5b3V0RWZmZWN0MiA9IGdsb2JhbFRoaXM/LmRvY3VtZW50ID8gUmVhY3QudXNlTGF5b3V0RWZmZWN0IDogKCkgPT4ge1xufTtcbmV4cG9ydCB7XG4gIHVzZUxheW91dEVmZmVjdDIgYXMgdXNlTGF5b3V0RWZmZWN0XG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\n"));

/***/ })

});