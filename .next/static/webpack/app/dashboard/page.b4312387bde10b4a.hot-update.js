"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/query-workspace.tsx":
/*!******************************************************!*\
  !*** ./src/components/dashboard/query-workspace.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryWorkspace: function() { return /* binding */ QueryWorkspace; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_chat_chat_interface__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/chat/chat-interface */ \"(app-pages-browser)/./src/components/chat/chat-interface.tsx\");\n/* harmony import */ var _components_editor_sql_editor__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/editor/sql-editor */ \"(app-pages-browser)/./src/components/editor/sql-editor.tsx\");\n/* harmony import */ var _components_dashboard_schema_explorer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard/schema-explorer */ \"(app-pages-browser)/./src/components/dashboard/schema-explorer.tsx\");\n/* harmony import */ var _components_dashboard_query_results__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/dashboard/query-results */ \"(app-pages-browser)/./src/components/dashboard/query-results.tsx\");\n/* harmony import */ var _stores_query_store__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/stores/query-store */ \"(app-pages-browser)/./src/stores/query-store.ts\");\n/* harmony import */ var _barrel_optimize_names_BrainCircuitIcon_DatabaseIcon_HistoryIcon_PlayIcon_SaveIcon_ShareIcon_TrendingUpIcon_ZapIcon_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BrainCircuitIcon,DatabaseIcon,HistoryIcon,PlayIcon,SaveIcon,ShareIcon,TrendingUpIcon,ZapIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain-circuit.js\");\n/* harmony import */ var _barrel_optimize_names_BrainCircuitIcon_DatabaseIcon_HistoryIcon_PlayIcon_SaveIcon_ShareIcon_TrendingUpIcon_ZapIcon_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BrainCircuitIcon,DatabaseIcon,HistoryIcon,PlayIcon,SaveIcon,ShareIcon,TrendingUpIcon,ZapIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_BrainCircuitIcon_DatabaseIcon_HistoryIcon_PlayIcon_SaveIcon_ShareIcon_TrendingUpIcon_ZapIcon_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BrainCircuitIcon,DatabaseIcon,HistoryIcon,PlayIcon,SaveIcon,ShareIcon,TrendingUpIcon,ZapIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BrainCircuitIcon_DatabaseIcon_HistoryIcon_PlayIcon_SaveIcon_ShareIcon_TrendingUpIcon_ZapIcon_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BrainCircuitIcon,DatabaseIcon,HistoryIcon,PlayIcon,SaveIcon,ShareIcon,TrendingUpIcon,ZapIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/history.js\");\n/* harmony import */ var _barrel_optimize_names_BrainCircuitIcon_DatabaseIcon_HistoryIcon_PlayIcon_SaveIcon_ShareIcon_TrendingUpIcon_ZapIcon_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BrainCircuitIcon,DatabaseIcon,HistoryIcon,PlayIcon,SaveIcon,ShareIcon,TrendingUpIcon,ZapIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_BrainCircuitIcon_DatabaseIcon_HistoryIcon_PlayIcon_SaveIcon_ShareIcon_TrendingUpIcon_ZapIcon_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BrainCircuitIcon,DatabaseIcon,HistoryIcon,PlayIcon,SaveIcon,ShareIcon,TrendingUpIcon,ZapIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share.js\");\n/* harmony import */ var _barrel_optimize_names_BrainCircuitIcon_DatabaseIcon_HistoryIcon_PlayIcon_SaveIcon_ShareIcon_TrendingUpIcon_ZapIcon_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BrainCircuitIcon,DatabaseIcon,HistoryIcon,PlayIcon,SaveIcon,ShareIcon,TrendingUpIcon,ZapIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_BrainCircuitIcon_DatabaseIcon_HistoryIcon_PlayIcon_SaveIcon_ShareIcon_TrendingUpIcon_ZapIcon_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BrainCircuitIcon,DatabaseIcon,HistoryIcon,PlayIcon,SaveIcon,ShareIcon,TrendingUpIcon,ZapIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* __next_internal_client_entry_do_not_use__ QueryWorkspace auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction QueryWorkspace() {\n    _s();\n    const { activeTab, setActiveTab, currentQuery, setCurrentQuery, currentDatabase } = (0,_stores_query_store__WEBPACK_IMPORTED_MODULE_9__.useQueryStore)();\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const tabs = [\n        {\n            id: \"chat\",\n            label: \"AI Assistant\",\n            icon: _barrel_optimize_names_BrainCircuitIcon_DatabaseIcon_HistoryIcon_PlayIcon_SaveIcon_ShareIcon_TrendingUpIcon_ZapIcon_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            id: \"editor\",\n            label: \"SQL Editor\",\n            icon: _barrel_optimize_names_BrainCircuitIcon_DatabaseIcon_HistoryIcon_PlayIcon_SaveIcon_ShareIcon_TrendingUpIcon_ZapIcon_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n        },\n        {\n            id: \"results\",\n            label: \"Results\",\n            icon: _barrel_optimize_names_BrainCircuitIcon_DatabaseIcon_HistoryIcon_PlayIcon_SaveIcon_ShareIcon_TrendingUpIcon_ZapIcon_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-80 border-r border-border bg-card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-border\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold\",\n                                        children: \"Database Schema\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        variant: isConnected ? \"default\" : \"secondary\",\n                                        children: isConnected ? \"Connected\" : \"Not Connected\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 11\n                            }, this),\n                            !isConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                className: \"w-full\",\n                                onClick: ()=>setIsConnected(true),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BrainCircuitIcon_DatabaseIcon_HistoryIcon_PlayIcon_SaveIcon_ShareIcon_TrendingUpIcon_ZapIcon_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Connect Database\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_schema_explorer__WEBPACK_IMPORTED_MODULE_7__.SchemaExplorer, {\n                            isConnected: isConnected\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-b border-border bg-card\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between px-6 py-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-1\",\n                                    children: tabs.map((tab)=>{\n                                        const Icon = tab.icon;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab(tab.id),\n                                            className: \"flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors \".concat(activeTab === tab.id ? \"bg-primary text-primary-foreground\" : \"text-muted-foreground hover:text-foreground hover:bg-muted\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                                    lineNumber: 86,\n                                                    columnNumber: 21\n                                                }, this),\n                                                tab.label\n                                            ]\n                                        }, tab.id, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BrainCircuitIcon_DatabaseIcon_HistoryIcon_PlayIcon_SaveIcon_ShareIcon_TrendingUpIcon_ZapIcon_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"History\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BrainCircuitIcon_DatabaseIcon_HistoryIcon_PlayIcon_SaveIcon_ShareIcon_TrendingUpIcon_ZapIcon_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Save\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BrainCircuitIcon_DatabaseIcon_HistoryIcon_PlayIcon_SaveIcon_ShareIcon_TrendingUpIcon_ZapIcon_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Share\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, this),\n                                        activeTab === \"editor\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            size: \"sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BrainCircuitIcon_DatabaseIcon_HistoryIcon_PlayIcon_SaveIcon_ShareIcon_TrendingUpIcon_ZapIcon_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Run Query\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-hidden\",\n                        children: [\n                            activeTab === \"chat\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_chat_interface__WEBPACK_IMPORTED_MODULE_5__.ChatInterface, {\n                                onQueryGenerated: (query)=>{\n                                    setCurrentQuery(query);\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === \"editor\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full flex flex-col\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_editor_sql_editor__WEBPACK_IMPORTED_MODULE_6__.SQLEditor, {\n                                        value: currentQuery,\n                                        onChange: setCurrentQuery,\n                                        language: (currentDatabase === null || currentDatabase === void 0 ? void 0 : currentDatabase.databaseType) || \"postgresql\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === \"results\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_query_results__WEBPACK_IMPORTED_MODULE_8__.QueryResults, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-80 border-l border-border bg-card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-border\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-semibold flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BrainCircuitIcon_DatabaseIcon_HistoryIcon_PlayIcon_SaveIcon_ShareIcon_TrendingUpIcon_ZapIcon_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this),\n                                \"AI Insights\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        className: \"pb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-sm\",\n                                            children: \"Query Suggestions\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"space-y-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"No active query to analyze. Start by describing what you want to query in the AI Assistant.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        className: \"pb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-sm\",\n                                            children: \"Performance Tips\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"space-y-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"Connect to a database to get performance recommendations.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        className: \"pb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-sm\",\n                                            children: \"Recent Activity\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"space-y-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"No recent queries found.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/dashboard/query-workspace.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n_s(QueryWorkspace, \"JpwAvjrBl+GkUUfHVX1KgJuZ35g=\", false, function() {\n    return [\n        _stores_query_store__WEBPACK_IMPORTED_MODULE_9__.useQueryStore\n    ];\n});\n_c = QueryWorkspace;\nvar _c;\n$RefreshReg$(_c, \"QueryWorkspace\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/query-workspace.tsx\n"));

/***/ })

});