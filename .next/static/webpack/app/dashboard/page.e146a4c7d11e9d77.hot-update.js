"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/stores/query-store.ts":
/*!***********************************!*\
  !*** ./src/stores/query-store.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useQueryStore: function() { return /* binding */ useQueryStore; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n\n\nconst useQueryStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.devtools)((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        // Initial state\n        currentSession: null,\n        currentQuery: \"\",\n        currentDatabase: null,\n        messages: [\n            {\n                id: \"1\",\n                role: \"assistant\",\n                content: \"Hello! I'm your AI SQL assistant. I can help you write, debug, and optimize SQL queries. What would you like to work on today?\",\n                timestamp: new Date(),\n                type: \"text\"\n            }\n        ],\n        isAILoading: false,\n        queryHistory: [],\n        connections: [],\n        activeTab: \"chat\",\n        sidebarOpen: true,\n        currentUser: null,\n        // Actions\n        setCurrentQuery: (query)=>set({\n                currentQuery: query\n            }),\n        setCurrentDatabase: (database)=>set({\n                currentDatabase: database\n            }),\n        addMessage: (message)=>set((state)=>({\n                    messages: [\n                        ...state.messages,\n                        message\n                    ]\n                })),\n        setAILoading: (loading)=>set({\n                isAILoading: loading\n            }),\n        addToHistory: (query)=>set((state)=>({\n                    queryHistory: [\n                        query,\n                        ...state.queryHistory.slice(0, 49)\n                    ] // Keep last 50\n                })),\n        setActiveTab: (tab)=>set({\n                activeTab: tab\n            }),\n        setSidebarOpen: (open)=>set({\n                sidebarOpen: open\n            }),\n        addConnection: (connection)=>set((state)=>({\n                    connections: [\n                        ...state.connections,\n                        connection\n                    ]\n                })),\n        removeConnection: (id)=>set((state)=>({\n                    connections: state.connections.filter((conn)=>conn.id !== id)\n                })),\n        clearMessages: ()=>set({\n                messages: [\n                    {\n                        id: \"1\",\n                        role: \"assistant\",\n                        content: \"Hello! I'm your AI SQL assistant. I can help you write, debug, and optimize SQL queries. What would you like to work on today?\",\n                        timestamp: new Date(),\n                        type: \"text\"\n                    }\n                ]\n            }),\n        // AI Actions\n        generateQuery: async (userInput)=>{\n            const { addMessage, setAILoading, setCurrentQuery, setActiveTab, currentDatabase } = get();\n            // Add user message\n            const userMessage = {\n                id: Date.now().toString(),\n                role: \"user\",\n                content: userInput,\n                timestamp: new Date(),\n                type: \"text\"\n            };\n            addMessage(userMessage);\n            setAILoading(true);\n            try {\n                // Simulate AI processing\n                await new Promise((resolve)=>setTimeout(resolve, 1500));\n                // Generate mock SQL based on user input\n                const mockSQL = generateMockSQL(userInput, (currentDatabase === null || currentDatabase === void 0 ? void 0 : currentDatabase.databaseType) || \"postgresql\");\n                const assistantMessage = {\n                    id: (Date.now() + 1).toString(),\n                    role: \"assistant\",\n                    content: \"I've generated a SQL query based on your request. Here's what I came up with:\",\n                    timestamp: new Date(),\n                    type: \"query\",\n                    metadata: {\n                        sql: mockSQL,\n                        explanation: generateExplanation(userInput),\n                        userInput: userInput,\n                        suggestions: [\n                            \"Consider adding appropriate indexes for better performance\",\n                            \"Add error handling for edge cases\",\n                            \"Test with sample data before running on production\"\n                        ]\n                    }\n                };\n                addMessage(assistantMessage);\n                setCurrentQuery(mockSQL);\n                // Add to history\n                const historyEntry = {\n                    id: Date.now().toString(),\n                    sessionId: \"current\",\n                    userInput,\n                    generatedSQL: mockSQL,\n                    explanation: generateExplanation(userInput),\n                    createdAt: new Date()\n                };\n                get().addToHistory(historyEntry);\n            } catch (error) {\n                const errorMessage = {\n                    id: (Date.now() + 1).toString(),\n                    role: \"assistant\",\n                    content: \"I apologize, but I encountered an error while generating your query. Please try again or rephrase your request.\",\n                    timestamp: new Date(),\n                    type: \"text\"\n                };\n                addMessage(errorMessage);\n            } finally{\n                setAILoading(false);\n            }\n        },\n        optimizeQuery: async (query)=>{\n            // Mock optimization\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            const optimizedQuery = \"-- Optimized version\\n\".concat(query, \"\\n-- Added index hints and optimizations\");\n            return optimizedQuery;\n        },\n        explainQuery: async (query)=>{\n            // Mock explanation\n            await new Promise((resolve)=>setTimeout(resolve, 800));\n            return \"This query performs the following operations:\\n1. Selects data from the specified tables\\n2. Applies filtering conditions\\n3. Groups and aggregates results\\n4. Orders the output for better readability\";\n        },\n        // Database integration actions\n        setCurrentUser: (user)=>set({\n                currentUser: user\n            }),\n        loadUserSessions: async (userId)=>{\n            try {\n                const response = await fetch(\"/api/queries?type=sessions&userId=\".concat(userId));\n                if (response.ok) {\n                    const sessions = await response.json();\n                    // Update state with sessions if needed\n                    console.log(\"Loaded sessions:\", sessions);\n                }\n            } catch (error) {\n                console.error(\"Failed to load user sessions:\", error);\n            }\n        },\n        loadUserConnections: async (userId)=>{\n            try {\n                const response = await fetch(\"/api/connections?userId=\".concat(userId));\n                if (response.ok) {\n                    const connections = await response.json();\n                    set({\n                        connections\n                    });\n                }\n            } catch (error) {\n                console.error(\"Failed to load user connections:\", error);\n            }\n        },\n        createNewSession: async (userId, connectionId)=>{\n            try {\n                const response = await fetch(\"/api/queries?type=session\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        userId,\n                        databaseConnectionId: connectionId,\n                        name: \"Session \".concat(new Date().toLocaleString())\n                    })\n                });\n                if (response.ok) {\n                    const session = await response.json();\n                    set({\n                        currentSession: session\n                    });\n                    console.log(\"Created new session:\", session);\n                }\n            } catch (error) {\n                console.error(\"Failed to create new session:\", error);\n            }\n        },\n        saveQueryToDatabase: async (userInput, sql, explanation)=>{\n            const { currentSession, currentUser, currentDatabase } = get();\n            if (!currentSession || !currentUser || !currentDatabase) {\n                console.warn(\"Cannot save query: missing session, user, or database\");\n                return;\n            }\n            try {\n                const response = await fetch(\"/api/queries?type=query\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        sessionId: currentSession.id,\n                        userId: currentUser.id,\n                        userInput,\n                        generatedSQL: sql,\n                        explanation,\n                        databaseType: currentDatabase.databaseType\n                    })\n                });\n                if (response.ok) {\n                    const query = await response.json();\n                    console.log(\"Saved query to database:\", query);\n                }\n            } catch (error) {\n                console.error(\"Failed to save query to database:\", error);\n            }\n        }\n    }), {\n    name: \"querycraft-store\",\n    partialize: (state)=>({\n            queryHistory: state.queryHistory,\n            connections: state.connections,\n            sidebarOpen: state.sidebarOpen,\n            currentDatabase: state.currentDatabase\n        })\n}), {\n    name: \"querycraft-store\"\n}));\n// Helper functions\nfunction generateMockSQL(userInput, dbType) {\n    const input = userInput.toLowerCase();\n    if (input.includes(\"customer\") && input.includes(\"revenue\")) {\n        return \"SELECT \\n  c.customer_id,\\n  c.name,\\n  c.email,\\n  COUNT(o.order_id) as total_orders,\\n  SUM(o.total_amount) as total_revenue\\nFROM customers c\\nLEFT JOIN orders o ON c.customer_id = o.customer_id\\nWHERE o.order_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)\\nGROUP BY c.customer_id, c.name, c.email\\nORDER BY total_revenue DESC\\nLIMIT 10;\";\n    }\n    if (input.includes(\"product\") && input.includes(\"sales\")) {\n        return \"SELECT \\n  p.product_id,\\n  p.name as product_name,\\n  COUNT(oi.item_id) as items_sold,\\n  SUM(oi.quantity * oi.unit_price) as total_sales\\nFROM products p\\nJOIN order_items oi ON p.product_id = oi.product_id\\nJOIN orders o ON oi.order_id = o.order_id\\nWHERE o.order_date >= DATE_SUB(NOW(), INTERVAL 7 DAY)\\nGROUP BY p.product_id, p.name\\nORDER BY total_sales DESC;\";\n    }\n    if (input.includes(\"order\") && input.includes(\"status\")) {\n        return \"SELECT \\n  status,\\n  COUNT(*) as order_count,\\n  AVG(total_amount) as avg_order_value\\nFROM orders\\nWHERE order_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)\\nGROUP BY status\\nORDER BY order_count DESC;\";\n    }\n    // Default query\n    return \"SELECT *\\nFROM your_table\\nWHERE condition = 'value'\\nORDER BY created_at DESC\\nLIMIT 10;\";\n}\nfunction generateExplanation(userInput) {\n    const input = userInput.toLowerCase();\n    if (input.includes(\"customer\") && input.includes(\"revenue\")) {\n        return \"This query retrieves the top customers by revenue in the last 30 days, including their contact information and order statistics.\";\n    }\n    if (input.includes(\"product\") && input.includes(\"sales\")) {\n        return \"This query analyzes product sales performance over the last 7 days, showing which products are selling best.\";\n    }\n    if (input.includes(\"order\") && input.includes(\"status\")) {\n        return \"This query provides an overview of order statuses and their distribution over the last 30 days.\";\n    }\n    return \"This query retrieves data based on your specified criteria with appropriate filtering and sorting.\";\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/stores/query-store.ts\n"));

/***/ })

});