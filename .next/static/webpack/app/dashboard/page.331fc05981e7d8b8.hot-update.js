"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/stores/query-store.ts":
/*!***********************************!*\
  !*** ./src/stores/query-store.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useQueryStore: function() { return /* binding */ useQueryStore; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n\n\nconst useQueryStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.devtools)((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        // Initial state\n        currentSession: null,\n        currentQuery: \"\",\n        currentDatabase: null,\n        messages: [\n            {\n                id: \"1\",\n                role: \"assistant\",\n                content: \"Hello! I'm your AI SQL assistant. I can help you write, debug, and optimize SQL queries. What would you like to work on today?\",\n                timestamp: new Date(),\n                type: \"text\"\n            }\n        ],\n        isAILoading: false,\n        queryHistory: [],\n        connections: [],\n        activeTab: \"chat\",\n        sidebarOpen: true,\n        currentUser: null,\n        // Actions\n        setCurrentQuery: (query)=>set({\n                currentQuery: query\n            }),\n        setCurrentDatabase: (database)=>set({\n                currentDatabase: database\n            }),\n        addMessage: (message)=>set((state)=>({\n                    messages: [\n                        ...state.messages,\n                        message\n                    ]\n                })),\n        setAILoading: (loading)=>set({\n                isAILoading: loading\n            }),\n        addToHistory: (query)=>set((state)=>({\n                    queryHistory: [\n                        query,\n                        ...state.queryHistory.slice(0, 49)\n                    ] // Keep last 50\n                })),\n        setActiveTab: (tab)=>set({\n                activeTab: tab\n            }),\n        setSidebarOpen: (open)=>set({\n                sidebarOpen: open\n            }),\n        addConnection: (connection)=>set((state)=>({\n                    connections: [\n                        ...state.connections,\n                        connection\n                    ]\n                })),\n        removeConnection: (id)=>set((state)=>({\n                    connections: state.connections.filter((conn)=>conn.id !== id)\n                })),\n        clearMessages: ()=>set({\n                messages: [\n                    {\n                        id: \"1\",\n                        role: \"assistant\",\n                        content: \"Hello! I'm your AI SQL assistant. I can help you write, debug, and optimize SQL queries. What would you like to work on today?\",\n                        timestamp: new Date(),\n                        type: \"text\"\n                    }\n                ]\n            }),\n        // AI Actions\n        generateQuery: async (userInput)=>{\n            const { addMessage, setAILoading, setCurrentQuery, setActiveTab, currentDatabase } = get();\n            // Add user message\n            const userMessage = {\n                id: Date.now().toString(),\n                role: \"user\",\n                content: userInput,\n                timestamp: new Date(),\n                type: \"text\"\n            };\n            addMessage(userMessage);\n            setAILoading(true);\n            try {\n                // Simulate AI processing\n                await new Promise((resolve)=>setTimeout(resolve, 1500));\n                // Generate mock SQL based on user input\n                const mockSQL = generateMockSQL(userInput, (currentDatabase === null || currentDatabase === void 0 ? void 0 : currentDatabase.databaseType) || \"postgresql\");\n                const assistantMessage = {\n                    id: (Date.now() + 1).toString(),\n                    role: \"assistant\",\n                    content: \"I've generated a SQL query based on your request. Here's what I came up with:\",\n                    timestamp: new Date(),\n                    type: \"query\",\n                    metadata: {\n                        sql: mockSQL,\n                        explanation: generateExplanation(userInput),\n                        suggestions: [\n                            \"Consider adding appropriate indexes for better performance\",\n                            \"Add error handling for edge cases\",\n                            \"Test with sample data before running on production\"\n                        ]\n                    }\n                };\n                addMessage(assistantMessage);\n                setCurrentQuery(mockSQL);\n                // Add to history\n                const historyEntry = {\n                    id: Date.now().toString(),\n                    sessionId: \"current\",\n                    userInput,\n                    generatedSQL: mockSQL,\n                    explanation: generateExplanation(userInput),\n                    createdAt: new Date()\n                };\n                get().addToHistory(historyEntry);\n            } catch (error) {\n                const errorMessage = {\n                    id: (Date.now() + 1).toString(),\n                    role: \"assistant\",\n                    content: \"I apologize, but I encountered an error while generating your query. Please try again or rephrase your request.\",\n                    timestamp: new Date(),\n                    type: \"text\"\n                };\n                addMessage(errorMessage);\n            } finally{\n                setAILoading(false);\n            }\n        },\n        optimizeQuery: async (query)=>{\n            // Mock optimization\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            const optimizedQuery = \"-- Optimized version\\n\".concat(query, \"\\n-- Added index hints and optimizations\");\n            return optimizedQuery;\n        },\n        explainQuery: async (query)=>{\n            // Mock explanation\n            await new Promise((resolve)=>setTimeout(resolve, 800));\n            return \"This query performs the following operations:\\n1. Selects data from the specified tables\\n2. Applies filtering conditions\\n3. Groups and aggregates results\\n4. Orders the output for better readability\";\n        }\n    }), {\n    name: \"querycraft-store\",\n    partialize: (state)=>({\n            queryHistory: state.queryHistory,\n            connections: state.connections,\n            sidebarOpen: state.sidebarOpen,\n            currentDatabase: state.currentDatabase\n        })\n}), {\n    name: \"querycraft-store\"\n}));\n// Helper functions\nfunction generateMockSQL(userInput, dbType) {\n    const input = userInput.toLowerCase();\n    if (input.includes(\"customer\") && input.includes(\"revenue\")) {\n        return \"SELECT \\n  c.customer_id,\\n  c.name,\\n  c.email,\\n  COUNT(o.order_id) as total_orders,\\n  SUM(o.total_amount) as total_revenue\\nFROM customers c\\nLEFT JOIN orders o ON c.customer_id = o.customer_id\\nWHERE o.order_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)\\nGROUP BY c.customer_id, c.name, c.email\\nORDER BY total_revenue DESC\\nLIMIT 10;\";\n    }\n    if (input.includes(\"product\") && input.includes(\"sales\")) {\n        return \"SELECT \\n  p.product_id,\\n  p.name as product_name,\\n  COUNT(oi.item_id) as items_sold,\\n  SUM(oi.quantity * oi.unit_price) as total_sales\\nFROM products p\\nJOIN order_items oi ON p.product_id = oi.product_id\\nJOIN orders o ON oi.order_id = o.order_id\\nWHERE o.order_date >= DATE_SUB(NOW(), INTERVAL 7 DAY)\\nGROUP BY p.product_id, p.name\\nORDER BY total_sales DESC;\";\n    }\n    if (input.includes(\"order\") && input.includes(\"status\")) {\n        return \"SELECT \\n  status,\\n  COUNT(*) as order_count,\\n  AVG(total_amount) as avg_order_value\\nFROM orders\\nWHERE order_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)\\nGROUP BY status\\nORDER BY order_count DESC;\";\n    }\n    // Default query\n    return \"SELECT *\\nFROM your_table\\nWHERE condition = 'value'\\nORDER BY created_at DESC\\nLIMIT 10;\";\n}\nfunction generateExplanation(userInput) {\n    const input = userInput.toLowerCase();\n    if (input.includes(\"customer\") && input.includes(\"revenue\")) {\n        return \"This query retrieves the top customers by revenue in the last 30 days, including their contact information and order statistics.\";\n    }\n    if (input.includes(\"product\") && input.includes(\"sales\")) {\n        return \"This query analyzes product sales performance over the last 7 days, showing which products are selling best.\";\n    }\n    if (input.includes(\"order\") && input.includes(\"status\")) {\n        return \"This query provides an overview of order statuses and their distribution over the last 30 days.\";\n    }\n    return \"This query retrieves data based on your specified criteria with appropriate filtering and sorting.\";\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/stores/query-store.ts\n"));

/***/ })

});