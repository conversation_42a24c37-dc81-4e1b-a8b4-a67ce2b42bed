"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/stores/query-store.ts":
/*!***********************************!*\
  !*** ./src/stores/query-store.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useQueryStore: function() { return /* binding */ useQueryStore; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n\n\nconst useQueryStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.devtools)((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        // Initial state\n        currentSession: null,\n        currentQuery: \"\",\n        currentDatabase: null,\n        messages: [\n            {\n                id: \"1\",\n                role: \"assistant\",\n                content: \"Hello! I'm your AI SQL assistant. I can help you write, debug, and optimize SQL queries. What would you like to work on today?\",\n                timestamp: new Date(),\n                type: \"text\"\n            }\n        ],\n        isAILoading: false,\n        queryHistory: [],\n        connections: [],\n        activeTab: \"chat\",\n        sidebarOpen: true,\n        // Actions\n        setCurrentQuery: (query)=>set({\n                currentQuery: query\n            }),\n        setCurrentDatabase: (database)=>set({\n                currentDatabase: database\n            }),\n        addMessage: (message)=>set((state)=>({\n                    messages: [\n                        ...state.messages,\n                        message\n                    ]\n                })),\n        setAILoading: (loading)=>set({\n                isAILoading: loading\n            }),\n        addToHistory: (query)=>set((state)=>({\n                    queryHistory: [\n                        query,\n                        ...state.queryHistory.slice(0, 49)\n                    ] // Keep last 50\n                })),\n        setActiveTab: (tab)=>set({\n                activeTab: tab\n            }),\n        setSidebarOpen: (open)=>set({\n                sidebarOpen: open\n            }),\n        addConnection: (connection)=>set((state)=>({\n                    connections: [\n                        ...state.connections,\n                        connection\n                    ]\n                })),\n        removeConnection: (id)=>set((state)=>({\n                    connections: state.connections.filter((conn)=>conn.id !== id)\n                })),\n        clearMessages: ()=>set({\n                messages: [\n                    {\n                        id: \"1\",\n                        role: \"assistant\",\n                        content: \"Hello! I'm your AI SQL assistant. I can help you write, debug, and optimize SQL queries. What would you like to work on today?\",\n                        timestamp: new Date(),\n                        type: \"text\"\n                    }\n                ]\n            }),\n        // AI Actions\n        generateQuery: async (userInput)=>{\n            const { addMessage, setAILoading, setCurrentQuery, setActiveTab, currentDatabase } = get();\n            // Add user message\n            const userMessage = {\n                id: Date.now().toString(),\n                role: \"user\",\n                content: userInput,\n                timestamp: new Date(),\n                type: \"text\"\n            };\n            addMessage(userMessage);\n            setAILoading(true);\n            try {\n                // Simulate AI processing\n                await new Promise((resolve)=>setTimeout(resolve, 1500));\n                // Generate mock SQL based on user input\n                const mockSQL = generateMockSQL(userInput, (currentDatabase === null || currentDatabase === void 0 ? void 0 : currentDatabase.databaseType) || \"postgresql\");\n                const assistantMessage = {\n                    id: (Date.now() + 1).toString(),\n                    role: \"assistant\",\n                    content: \"I've generated a SQL query based on your request. Here's what I came up with:\",\n                    timestamp: new Date(),\n                    type: \"query\",\n                    metadata: {\n                        sql: mockSQL,\n                        explanation: generateExplanation(userInput),\n                        suggestions: [\n                            \"Consider adding appropriate indexes for better performance\",\n                            \"Add error handling for edge cases\",\n                            \"Test with sample data before running on production\"\n                        ]\n                    }\n                };\n                addMessage(assistantMessage);\n                setCurrentQuery(mockSQL);\n                // Add to history\n                const historyEntry = {\n                    id: Date.now().toString(),\n                    sessionId: \"current\",\n                    userInput,\n                    generatedSQL: mockSQL,\n                    explanation: generateExplanation(userInput),\n                    createdAt: new Date()\n                };\n                get().addToHistory(historyEntry);\n            } catch (error) {\n                const errorMessage = {\n                    id: (Date.now() + 1).toString(),\n                    role: \"assistant\",\n                    content: \"I apologize, but I encountered an error while generating your query. Please try again or rephrase your request.\",\n                    timestamp: new Date(),\n                    type: \"text\"\n                };\n                addMessage(errorMessage);\n            } finally{\n                setAILoading(false);\n            }\n        },\n        optimizeQuery: async (query)=>{\n            // Mock optimization\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            const optimizedQuery = \"-- Optimized version\\n\".concat(query, \"\\n-- Added index hints and optimizations\");\n            return optimizedQuery;\n        },\n        explainQuery: async (query)=>{\n            // Mock explanation\n            await new Promise((resolve)=>setTimeout(resolve, 800));\n            return \"This query performs the following operations:\\n1. Selects data from the specified tables\\n2. Applies filtering conditions\\n3. Groups and aggregates results\\n4. Orders the output for better readability\";\n        }\n    }), {\n    name: \"querycraft-store\",\n    partialize: (state)=>({\n            queryHistory: state.queryHistory,\n            connections: state.connections,\n            sidebarOpen: state.sidebarOpen,\n            currentDatabase: state.currentDatabase\n        })\n}), {\n    name: \"querycraft-store\"\n}));\n// Helper functions\nfunction generateMockSQL(userInput, dbType) {\n    const input = userInput.toLowerCase();\n    if (input.includes(\"customer\") && input.includes(\"revenue\")) {\n        return \"SELECT \\n  c.customer_id,\\n  c.name,\\n  c.email,\\n  COUNT(o.order_id) as total_orders,\\n  SUM(o.total_amount) as total_revenue\\nFROM customers c\\nLEFT JOIN orders o ON c.customer_id = o.customer_id\\nWHERE o.order_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)\\nGROUP BY c.customer_id, c.name, c.email\\nORDER BY total_revenue DESC\\nLIMIT 10;\";\n    }\n    if (input.includes(\"product\") && input.includes(\"sales\")) {\n        return \"SELECT \\n  p.product_id,\\n  p.name as product_name,\\n  COUNT(oi.item_id) as items_sold,\\n  SUM(oi.quantity * oi.unit_price) as total_sales\\nFROM products p\\nJOIN order_items oi ON p.product_id = oi.product_id\\nJOIN orders o ON oi.order_id = o.order_id\\nWHERE o.order_date >= DATE_SUB(NOW(), INTERVAL 7 DAY)\\nGROUP BY p.product_id, p.name\\nORDER BY total_sales DESC;\";\n    }\n    if (input.includes(\"order\") && input.includes(\"status\")) {\n        return \"SELECT \\n  status,\\n  COUNT(*) as order_count,\\n  AVG(total_amount) as avg_order_value\\nFROM orders\\nWHERE order_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)\\nGROUP BY status\\nORDER BY order_count DESC;\";\n    }\n    // Default query\n    return \"SELECT *\\nFROM your_table\\nWHERE condition = 'value'\\nORDER BY created_at DESC\\nLIMIT 10;\";\n}\nfunction generateExplanation(userInput) {\n    const input = userInput.toLowerCase();\n    if (input.includes(\"customer\") && input.includes(\"revenue\")) {\n        return \"This query retrieves the top customers by revenue in the last 30 days, including their contact information and order statistics.\";\n    }\n    if (input.includes(\"product\") && input.includes(\"sales\")) {\n        return \"This query analyzes product sales performance over the last 7 days, showing which products are selling best.\";\n    }\n    if (input.includes(\"order\") && input.includes(\"status\")) {\n        return \"This query provides an overview of order statuses and their distribution over the last 30 days.\";\n    }\n    return \"This query retrieves data based on your specified criteria with appropriate filtering and sorting.\";\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/stores/query-store.ts\n"));

/***/ })

});