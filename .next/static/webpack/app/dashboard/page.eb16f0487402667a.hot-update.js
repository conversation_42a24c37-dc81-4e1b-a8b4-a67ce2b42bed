"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/chat/chat-interface.tsx":
/*!************************************************!*\
  !*** ./src/components/chat/chat-interface.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInterface: function() { return /* binding */ ChatInterface; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_BrainCircuitIcon_CopyIcon_SendIcon_SparklesIcon_ThumbsDownIcon_ThumbsUpIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BrainCircuitIcon,CopyIcon,SendIcon,SparklesIcon,ThumbsDownIcon,ThumbsUpIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain-circuit.js\");\n/* harmony import */ var _barrel_optimize_names_BrainCircuitIcon_CopyIcon_SendIcon_SparklesIcon_ThumbsDownIcon_ThumbsUpIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BrainCircuitIcon,CopyIcon,SendIcon,SparklesIcon,ThumbsDownIcon,ThumbsUpIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_BrainCircuitIcon_CopyIcon_SendIcon_SparklesIcon_ThumbsDownIcon_ThumbsUpIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BrainCircuitIcon,CopyIcon,SendIcon,SparklesIcon,ThumbsDownIcon,ThumbsUpIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_BrainCircuitIcon_CopyIcon_SendIcon_SparklesIcon_ThumbsDownIcon_ThumbsUpIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BrainCircuitIcon,CopyIcon,SendIcon,SparklesIcon,ThumbsDownIcon,ThumbsUpIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/thumbs-up.js\");\n/* harmony import */ var _barrel_optimize_names_BrainCircuitIcon_CopyIcon_SendIcon_SparklesIcon_ThumbsDownIcon_ThumbsUpIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BrainCircuitIcon,CopyIcon,SendIcon,SparklesIcon,ThumbsDownIcon,ThumbsUpIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/thumbs-down.js\");\n/* harmony import */ var _barrel_optimize_names_BrainCircuitIcon_CopyIcon_SendIcon_SparklesIcon_ThumbsDownIcon_ThumbsUpIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BrainCircuitIcon,CopyIcon,SendIcon,SparklesIcon,ThumbsDownIcon,ThumbsUpIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BrainCircuitIcon_CopyIcon_SendIcon_SparklesIcon_ThumbsDownIcon_ThumbsUpIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BrainCircuitIcon,CopyIcon,SendIcon,SparklesIcon,ThumbsDownIcon,ThumbsUpIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _stores_query_store__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/stores/query-store */ \"(app-pages-browser)/./src/stores/query-store.ts\");\n/* __next_internal_client_entry_do_not_use__ ChatInterface auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ChatInterface(param) {\n    let { onQueryGenerated } = param;\n    _s();\n    const { messages, isAILoading, generateQuery, setActiveTab, saveQueryToDatabase } = (0,_stores_query_store__WEBPACK_IMPORTED_MODULE_6__.useQueryStore)();\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages\n    ]);\n    const handleSendMessage = async ()=>{\n        if (!inputValue.trim() || isAILoading) return;\n        const input = inputValue;\n        setInputValue(\"\");\n        await generateQuery(input);\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    const handleCopyQuery = (sql)=>{\n        navigator.clipboard.writeText(sql);\n    };\n    const handleUseQuery = async (sql, userInput, explanation)=>{\n        onQueryGenerated(sql);\n        setActiveTab(\"editor\");\n        // Save to database if we have the required data\n        if (userInput && explanation) {\n            try {\n                await saveQueryToDatabase(userInput, sql, explanation);\n            } catch (error) {\n                console.error(\"Failed to save query to database:\", error);\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-6 space-y-4\",\n                children: [\n                    messages.map((message)=>{\n                        var _message_metadata;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3 \".concat(message.role === \"user\" ? \"justify-end\" : \"justify-start\"),\n                            children: [\n                                message.role === \"assistant\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 w-8 bg-primary rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BrainCircuitIcon_CopyIcon_SendIcon_SparklesIcon_ThumbsDownIcon_ThumbsUpIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4 text-primary-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-2xl \".concat(message.role === \"user\" ? \"order-first\" : \"\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"p-4 \".concat(message.role === \"user\" ? \"bg-primary text-primary-foreground\" : \"\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm\",\n                                                        children: message.content\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                        lineNumber: 97,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    message.type === \"query\" && ((_message_metadata = message.metadata) === null || _message_metadata === void 0 ? void 0 : _message_metadata.sql) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-muted rounded-lg p-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between mb-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                variant: \"secondary\",\n                                                                                className: \"text-xs\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BrainCircuitIcon_CopyIcon_SendIcon_SparklesIcon_ThumbsDownIcon_ThumbsUpIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                                        className: \"h-3 w-3 mr-1\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                                                        lineNumber: 104,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    \"Generated SQL\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                                                lineNumber: 103,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex gap-1\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                    variant: \"ghost\",\n                                                                                    size: \"sm\",\n                                                                                    onClick: ()=>handleCopyQuery(message.metadata.sql),\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BrainCircuitIcon_CopyIcon_SendIcon_SparklesIcon_ThumbsDownIcon_ThumbsUpIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                        className: \"h-3 w-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                                                        lineNumber: 113,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                                                    lineNumber: 108,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                                                lineNumber: 107,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                                        lineNumber: 102,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                                        className: \"text-xs font-mono overflow-x-auto\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                            children: message.metadata.sql\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                                            lineNumber: 118,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                                        lineNumber: 117,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                                lineNumber: 101,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            message.metadata.explanation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Explanation:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                                        lineNumber: 124,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \" \",\n                                                                    message.metadata.explanation\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                                lineNumber: 123,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        size: \"sm\",\n                                                                        onClick: ()=>{\n                                                                            var _message_metadata, _message_metadata1;\n                                                                            return handleUseQuery(message.metadata.sql, (_message_metadata = message.metadata) === null || _message_metadata === void 0 ? void 0 : _message_metadata.userInput, (_message_metadata1 = message.metadata) === null || _message_metadata1 === void 0 ? void 0 : _message_metadata1.explanation);\n                                                                        },\n                                                                        children: \"Use in Editor\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                                        lineNumber: 129,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BrainCircuitIcon_CopyIcon_SendIcon_SparklesIcon_ThumbsDownIcon_ThumbsUpIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                className: \"h-3 w-3 mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                                                lineNumber: 140,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \"Good\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                                        lineNumber: 139,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BrainCircuitIcon_CopyIcon_SendIcon_SparklesIcon_ThumbsDownIcon_ThumbsUpIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                className: \"h-3 w-3 mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                                                lineNumber: 144,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \"Improve\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                                        lineNumber: 143,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                                lineNumber: 128,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                        lineNumber: 100,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-muted-foreground mt-2\",\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatRelativeTime)(message.timestamp)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, this),\n                                message.role === \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 w-8 bg-muted rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BrainCircuitIcon_CopyIcon_SendIcon_SparklesIcon_ThumbsDownIcon_ThumbsUpIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, message.id, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this);\n                    }),\n                    isAILoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-8 bg-primary rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BrainCircuitIcon_CopyIcon_SendIcon_SparklesIcon_ThumbsDownIcon_ThumbsUpIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4 text-primary-foreground animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"loading-spinner\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"AI is thinking...\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-border p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    ref: textareaRef,\n                                    value: inputValue,\n                                    onChange: (e)=>setInputValue(e.target.value),\n                                    onKeyPress: handleKeyPress,\n                                    placeholder: \"Describe the data you want to query... (e.g., 'Show me the top 10 customers by revenue this month')\",\n                                    className: \"querycraft-textarea resize-none\",\n                                    rows: 3,\n                                    disabled: isAILoading\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: handleSendMessage,\n                                disabled: !inputValue.trim() || isAILoading,\n                                size: \"lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BrainCircuitIcon_CopyIcon_SendIcon_SparklesIcon_ThumbsDownIcon_ThumbsUpIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-muted-foreground mt-2\",\n                        children: \"Press Enter to send, Shift+Enter for new line\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatInterface, \"MsN/VlKb5jNeiIOxxDL2BpT020w=\", false, function() {\n    return [\n        _stores_query_store__WEBPACK_IMPORTED_MODULE_6__.useQueryStore\n    ];\n});\n_c = ChatInterface;\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2NoYXQvY2hhdC1pbnRlcmZhY2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVvRDtBQUNKO0FBQ0o7QUFDRTtBQVN4QjtBQUMyQjtBQUNJO0FBTTlDLFNBQVNlLGNBQWMsS0FBd0M7UUFBeEMsRUFBRUMsZ0JBQWdCLEVBQXNCLEdBQXhDOztJQUM1QixNQUFNLEVBQ0pDLFFBQVEsRUFDUkMsV0FBVyxFQUNYQyxhQUFhLEVBQ2JDLFlBQVksRUFDWkMsbUJBQW1CLEVBQ3BCLEdBQUdQLGtFQUFhQTtJQUNqQixNQUFNLENBQUNRLFlBQVlDLGNBQWMsR0FBR3ZCLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU13QixpQkFBaUJ2Qiw2Q0FBTUEsQ0FBaUI7SUFDOUMsTUFBTXdCLGNBQWN4Qiw2Q0FBTUEsQ0FBc0I7SUFFaEQsTUFBTXlCLGlCQUFpQjtZQUNyQkY7U0FBQUEsMEJBQUFBLGVBQWVHLE9BQU8sY0FBdEJILDhDQUFBQSx3QkFBd0JJLGNBQWMsQ0FBQztZQUFFQyxVQUFVO1FBQVM7SUFDOUQ7SUFFQTNCLGdEQUFTQSxDQUFDO1FBQ1J3QjtJQUNGLEdBQUc7UUFBQ1Q7S0FBUztJQUViLE1BQU1hLG9CQUFvQjtRQUN4QixJQUFJLENBQUNSLFdBQVdTLElBQUksTUFBTWIsYUFBYTtRQUV2QyxNQUFNYyxRQUFRVjtRQUNkQyxjQUFjO1FBRWQsTUFBTUosY0FBY2E7SUFDdEI7SUFFQSxNQUFNQyxpQkFBaUIsQ0FBQ0M7UUFDdEIsSUFBSUEsRUFBRUMsR0FBRyxLQUFLLFdBQVcsQ0FBQ0QsRUFBRUUsUUFBUSxFQUFFO1lBQ3BDRixFQUFFRyxjQUFjO1lBQ2hCUDtRQUNGO0lBQ0Y7SUFFQSxNQUFNUSxrQkFBa0IsQ0FBQ0M7UUFDdkJDLFVBQVVDLFNBQVMsQ0FBQ0MsU0FBUyxDQUFDSDtJQUNoQztJQUVBLE1BQU1JLGlCQUFpQixPQUFPSixLQUFhSyxXQUFvQkM7UUFDN0Q3QixpQkFBaUJ1QjtRQUNqQm5CLGFBQWE7UUFFYixnREFBZ0Q7UUFDaEQsSUFBSXdCLGFBQWFDLGFBQWE7WUFDNUIsSUFBSTtnQkFDRixNQUFNeEIsb0JBQW9CdUIsV0FBV0wsS0FBS007WUFDNUMsRUFBRSxPQUFPQyxPQUFPO2dCQUNkQyxRQUFRRCxLQUFLLENBQUMscUNBQXFDQTtZQUNyRDtRQUNGO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ0U7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNEO2dCQUFJQyxXQUFVOztvQkFDWmhDLFNBQVNpQyxHQUFHLENBQUMsQ0FBQ0M7NEJBa0J3QkE7NkNBakJyQyw4REFBQ0g7NEJBRUNDLFdBQVcsY0FBd0UsT0FBMURFLFFBQVFDLElBQUksS0FBSyxTQUFTLGdCQUFnQjs7Z0NBRWxFRCxRQUFRQyxJQUFJLEtBQUssNkJBQ2hCLDhEQUFDSjtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUMxQyxnS0FBZ0JBOzRDQUFDMEMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FLbEMsOERBQUNEO29DQUFJQyxXQUFXLGFBQTBELE9BQTdDRSxRQUFRQyxJQUFJLEtBQUssU0FBUyxnQkFBZ0I7OENBQ3JFLDRFQUFDaEQscURBQUlBO3dDQUFDNkMsV0FBVyxPQUEyRSxPQUFwRUUsUUFBUUMsSUFBSSxLQUFLLFNBQVMsdUNBQXVDOzswREFDdkYsOERBQUNKO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7d0RBQUlDLFdBQVU7a0VBQVdFLFFBQVFFLE9BQU87Ozs7OztvREFFeENGLFFBQVFHLElBQUksS0FBSyxhQUFXSCxvQkFBQUEsUUFBUUksUUFBUSxjQUFoQkosd0NBQUFBLGtCQUFrQlosR0FBRyxtQkFDaEQsOERBQUNTO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVU7O2tGQUNiLDhEQUFDRDt3RUFBSUMsV0FBVTs7MEZBQ2IsOERBQUM1Qyx1REFBS0E7Z0ZBQUNtRCxTQUFRO2dGQUFZUCxXQUFVOztrR0FDbkMsOERBQUNyQyxnS0FBWUE7d0ZBQUNxQyxXQUFVOzs7Ozs7b0ZBQWlCOzs7Ozs7OzBGQUczQyw4REFBQ0Q7Z0ZBQUlDLFdBQVU7MEZBQ2IsNEVBQUM5Qyx5REFBTUE7b0ZBQ0xxRCxTQUFRO29GQUNSQyxNQUFLO29GQUNMQyxTQUFTLElBQU1wQixnQkFBZ0JhLFFBQVFJLFFBQVEsQ0FBRWhCLEdBQUc7OEZBRXBELDRFQUFDOUIsZ0tBQVFBO3dGQUFDd0MsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrRkFJMUIsOERBQUNVO3dFQUFJVixXQUFVO2tGQUNiLDRFQUFDVztzRkFBTVQsUUFBUUksUUFBUSxDQUFDaEIsR0FBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7NERBSTlCWSxRQUFRSSxRQUFRLENBQUNWLFdBQVcsa0JBQzNCLDhEQUFDRztnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUNZO2tGQUFPOzs7Ozs7b0VBQXFCO29FQUFFVixRQUFRSSxRQUFRLENBQUNWLFdBQVc7Ozs7Ozs7MEVBSS9ELDhEQUFDRztnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUM5Qyx5REFBTUE7d0VBQ0xzRCxNQUFLO3dFQUNMQyxTQUFTO2dGQUVQUCxtQkFDQUE7bUZBSGFSLGVBQ2JRLFFBQVFJLFFBQVEsQ0FBRWhCLEdBQUcsR0FDckJZLG9CQUFBQSxRQUFRSSxRQUFRLGNBQWhCSix3Q0FBQUEsa0JBQWtCUCxTQUFTLEdBQzNCTyxxQkFBQUEsUUFBUUksUUFBUSxjQUFoQkoseUNBQUFBLG1CQUFrQk4sV0FBVzs7a0ZBRWhDOzs7Ozs7a0ZBR0QsOERBQUMxQyx5REFBTUE7d0VBQUNxRCxTQUFRO3dFQUFVQyxNQUFLOzswRkFDN0IsOERBQUMvQyxpS0FBWUE7Z0ZBQUN1QyxXQUFVOzs7Ozs7NEVBQWlCOzs7Ozs7O2tGQUczQyw4REFBQzlDLHlEQUFNQTt3RUFBQ3FELFNBQVE7d0VBQVVDLE1BQUs7OzBGQUM3Qiw4REFBQzlDLGlLQUFjQTtnRkFBQ3NDLFdBQVU7Ozs7Ozs0RUFBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBUXJELDhEQUFDRDtnREFBSUMsV0FBVTswREFDWnBDLDhEQUFrQkEsQ0FBQ3NDLFFBQVFXLFNBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7O2dDQUsxQ1gsUUFBUUMsSUFBSSxLQUFLLHdCQUNoQiw4REFBQ0o7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNEO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDekMsaUtBQVFBOzRDQUFDeUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7MkJBOUVyQkUsUUFBUVksRUFBRTs7Ozs7O29CQXFGbEI3Qyw2QkFDQyw4REFBQzhCO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDMUMsZ0tBQWdCQTt3Q0FBQzBDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7MENBR2hDLDhEQUFDN0MscURBQUlBO2dDQUFDNkMsV0FBVTswQ0FDZCw0RUFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTs7Ozs7O3NEQUNmLDhEQUFDZTs0Q0FBS2YsV0FBVTtzREFBZ0M7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU14RCw4REFBQ0Q7d0JBQUlpQixLQUFLekM7Ozs7Ozs7Ozs7OzswQkFJWiw4REFBQ3dCO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ2lCO29DQUNDRCxLQUFLeEM7b0NBQ0wwQyxPQUFPN0M7b0NBQ1A4QyxVQUFVLENBQUNsQyxJQUFNWCxjQUFjVyxFQUFFbUMsTUFBTSxDQUFDRixLQUFLO29DQUM3Q0csWUFBWXJDO29DQUNac0MsYUFBWTtvQ0FDWnRCLFdBQVU7b0NBQ1Z1QixNQUFNO29DQUNOQyxVQUFVdkQ7Ozs7Ozs7Ozs7OzBDQUdkLDhEQUFDZix5REFBTUE7Z0NBQ0x1RCxTQUFTNUI7Z0NBQ1QyQyxVQUFVLENBQUNuRCxXQUFXUyxJQUFJLE1BQU1iO2dDQUNoQ3VDLE1BQUs7MENBRUwsNEVBQUNuRCxpS0FBUUE7b0NBQUMyQyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FHeEIsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUFxQzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTTVEO0dBak1nQmxDOztRQU9WRCw4REFBYUE7OztLQVBIQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9jaGF0L2NoYXQtaW50ZXJmYWNlLnRzeD9kMTEzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZVJlZiwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2J1dHRvbic7XG5pbXBvcnQgeyBDYXJkIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2NhcmQnO1xuaW1wb3J0IHsgQmFkZ2UgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYmFkZ2UnO1xuaW1wb3J0IHtcbiAgU2VuZEljb24sXG4gIEJyYWluQ2lyY3VpdEljb24sXG4gIFVzZXJJY29uLFxuICBDb3B5SWNvbixcbiAgVGh1bWJzVXBJY29uLFxuICBUaHVtYnNEb3duSWNvbixcbiAgU3BhcmtsZXNJY29uXG59IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XG5pbXBvcnQgeyBmb3JtYXRSZWxhdGl2ZVRpbWUgfSBmcm9tICdAL2xpYi91dGlscyc7XG5pbXBvcnQgeyB1c2VRdWVyeVN0b3JlIH0gZnJvbSAnQC9zdG9yZXMvcXVlcnktc3RvcmUnO1xuXG5pbnRlcmZhY2UgQ2hhdEludGVyZmFjZVByb3BzIHtcbiAgb25RdWVyeUdlbmVyYXRlZDogKHF1ZXJ5OiBzdHJpbmcpID0+IHZvaWQ7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBDaGF0SW50ZXJmYWNlKHsgb25RdWVyeUdlbmVyYXRlZCB9OiBDaGF0SW50ZXJmYWNlUHJvcHMpIHtcbiAgY29uc3Qge1xuICAgIG1lc3NhZ2VzLFxuICAgIGlzQUlMb2FkaW5nLFxuICAgIGdlbmVyYXRlUXVlcnksXG4gICAgc2V0QWN0aXZlVGFiLFxuICAgIHNhdmVRdWVyeVRvRGF0YWJhc2VcbiAgfSA9IHVzZVF1ZXJ5U3RvcmUoKTtcbiAgY29uc3QgW2lucHV0VmFsdWUsIHNldElucHV0VmFsdWVdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBtZXNzYWdlc0VuZFJlZiA9IHVzZVJlZjxIVE1MRGl2RWxlbWVudD4obnVsbCk7XG4gIGNvbnN0IHRleHRhcmVhUmVmID0gdXNlUmVmPEhUTUxUZXh0QXJlYUVsZW1lbnQ+KG51bGwpO1xuXG4gIGNvbnN0IHNjcm9sbFRvQm90dG9tID0gKCkgPT4ge1xuICAgIG1lc3NhZ2VzRW5kUmVmLmN1cnJlbnQ/LnNjcm9sbEludG9WaWV3KHsgYmVoYXZpb3I6ICdzbW9vdGgnIH0pO1xuICB9O1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgc2Nyb2xsVG9Cb3R0b20oKTtcbiAgfSwgW21lc3NhZ2VzXSk7XG5cbiAgY29uc3QgaGFuZGxlU2VuZE1lc3NhZ2UgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFpbnB1dFZhbHVlLnRyaW0oKSB8fCBpc0FJTG9hZGluZykgcmV0dXJuO1xuXG4gICAgY29uc3QgaW5wdXQgPSBpbnB1dFZhbHVlO1xuICAgIHNldElucHV0VmFsdWUoJycpO1xuXG4gICAgYXdhaXQgZ2VuZXJhdGVRdWVyeShpbnB1dCk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlS2V5UHJlc3MgPSAoZTogUmVhY3QuS2V5Ym9hcmRFdmVudCkgPT4ge1xuICAgIGlmIChlLmtleSA9PT0gJ0VudGVyJyAmJiAhZS5zaGlmdEtleSkge1xuICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgaGFuZGxlU2VuZE1lc3NhZ2UoKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlQ29weVF1ZXJ5ID0gKHNxbDogc3RyaW5nKSA9PiB7XG4gICAgbmF2aWdhdG9yLmNsaXBib2FyZC53cml0ZVRleHQoc3FsKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVVc2VRdWVyeSA9IGFzeW5jIChzcWw6IHN0cmluZywgdXNlcklucHV0Pzogc3RyaW5nLCBleHBsYW5hdGlvbj86IHN0cmluZykgPT4ge1xuICAgIG9uUXVlcnlHZW5lcmF0ZWQoc3FsKTtcbiAgICBzZXRBY3RpdmVUYWIoJ2VkaXRvcicpO1xuXG4gICAgLy8gU2F2ZSB0byBkYXRhYmFzZSBpZiB3ZSBoYXZlIHRoZSByZXF1aXJlZCBkYXRhXG4gICAgaWYgKHVzZXJJbnB1dCAmJiBleHBsYW5hdGlvbikge1xuICAgICAgdHJ5IHtcbiAgICAgICAgYXdhaXQgc2F2ZVF1ZXJ5VG9EYXRhYmFzZSh1c2VySW5wdXQsIHNxbCwgZXhwbGFuYXRpb24pO1xuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIHNhdmUgcXVlcnkgdG8gZGF0YWJhc2U6JywgZXJyb3IpO1xuICAgICAgfVxuICAgIH1cbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBoLWZ1bGxcIj5cbiAgICAgIHsvKiBNZXNzYWdlcyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG92ZXJmbG93LXktYXV0byBwLTYgc3BhY2UteS00XCI+XG4gICAgICAgIHttZXNzYWdlcy5tYXAoKG1lc3NhZ2UpID0+IChcbiAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICBrZXk9e21lc3NhZ2UuaWR9XG4gICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4IGdhcC0zICR7bWVzc2FnZS5yb2xlID09PSAndXNlcicgPyAnanVzdGlmeS1lbmQnIDogJ2p1c3RpZnktc3RhcnQnfWB9XG4gICAgICAgICAgPlxuICAgICAgICAgICAge21lc3NhZ2Uucm9sZSA9PT0gJ2Fzc2lzdGFudCcgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtc2hyaW5rLTBcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtOCB3LTggYmctcHJpbWFyeSByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxCcmFpbkNpcmN1aXRJY29uIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1wcmltYXJ5LWZvcmVncm91bmRcIiAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgbWF4LXctMnhsICR7bWVzc2FnZS5yb2xlID09PSAndXNlcicgPyAnb3JkZXItZmlyc3QnIDogJyd9YH0+XG4gICAgICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT17YHAtNCAke21lc3NhZ2Uucm9sZSA9PT0gJ3VzZXInID8gJ2JnLXByaW1hcnkgdGV4dC1wcmltYXJ5LWZvcmVncm91bmQnIDogJyd9YH0+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbVwiPnttZXNzYWdlLmNvbnRlbnR9PC9kaXY+XG4gICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgIHttZXNzYWdlLnR5cGUgPT09ICdxdWVyeScgJiYgbWVzc2FnZS5tZXRhZGF0YT8uc3FsICYmIChcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLW11dGVkIHJvdW5kZWQtbGcgcC0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxCYWRnZSB2YXJpYW50PVwic2Vjb25kYXJ5XCIgY2xhc3NOYW1lPVwidGV4dC14c1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTcGFya2xlc0ljb24gY2xhc3NOYW1lPVwiaC0zIHctMyBtci0xXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBHZW5lcmF0ZWQgU1FMXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlQ29weVF1ZXJ5KG1lc3NhZ2UubWV0YWRhdGEhLnNxbCEpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDb3B5SWNvbiBjbGFzc05hbWU9XCJoLTMgdy0zXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwcmUgY2xhc3NOYW1lPVwidGV4dC14cyBmb250LW1vbm8gb3ZlcmZsb3cteC1hdXRvXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxjb2RlPnttZXNzYWdlLm1ldGFkYXRhLnNxbH08L2NvZGU+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3ByZT5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgICB7bWVzc2FnZS5tZXRhZGF0YS5leHBsYW5hdGlvbiAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzdHJvbmc+RXhwbGFuYXRpb246PC9zdHJvbmc+IHttZXNzYWdlLm1ldGFkYXRhLmV4cGxhbmF0aW9ufVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlVXNlUXVlcnkoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZS5tZXRhZGF0YSEuc3FsISxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlLm1ldGFkYXRhPy51c2VySW5wdXQsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZS5tZXRhZGF0YT8uZXhwbGFuYXRpb25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgVXNlIGluIEVkaXRvclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgc2l6ZT1cInNtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUaHVtYnNVcEljb24gY2xhc3NOYW1lPVwiaC0zIHctMyBtci0xXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgR29vZFxuICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgc2l6ZT1cInNtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUaHVtYnNEb3duSWNvbiBjbGFzc05hbWU9XCJoLTMgdy0zIG1yLTFcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICBJbXByb3ZlXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LW11dGVkLWZvcmVncm91bmQgbXQtMlwiPlxuICAgICAgICAgICAgICAgICAge2Zvcm1hdFJlbGF0aXZlVGltZShtZXNzYWdlLnRpbWVzdGFtcCl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgXG4gICAgICAgICAgICB7bWVzc2FnZS5yb2xlID09PSAndXNlcicgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtc2hyaW5rLTBcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtOCB3LTggYmctbXV0ZWQgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8VXNlckljb24gY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LW11dGVkLWZvcmVncm91bmRcIiAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICkpfVxuICAgICAgICBcbiAgICAgICAge2lzQUlMb2FkaW5nICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTNcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC1zaHJpbmstMFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtOCB3LTggYmctcHJpbWFyeSByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8QnJhaW5DaXJjdWl0SWNvbiBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtcHJpbWFyeS1mb3JlZ3JvdW5kIGFuaW1hdGUtcHVsc2VcIiAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwicC00XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxvYWRpbmctc3Bpbm5lclwiIC8+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5BSSBpcyB0aGlua2luZy4uLjwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICAgIFxuICAgICAgICA8ZGl2IHJlZj17bWVzc2FnZXNFbmRSZWZ9IC8+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIElucHV0ICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJib3JkZXItdCBib3JkZXItYm9yZGVyIHAtNFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTNcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgPHRleHRhcmVhXG4gICAgICAgICAgICAgIHJlZj17dGV4dGFyZWFSZWZ9XG4gICAgICAgICAgICAgIHZhbHVlPXtpbnB1dFZhbHVlfVxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldElucHV0VmFsdWUoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICBvbktleVByZXNzPXtoYW5kbGVLZXlQcmVzc31cbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJEZXNjcmliZSB0aGUgZGF0YSB5b3Ugd2FudCB0byBxdWVyeS4uLiAoZS5nLiwgJ1Nob3cgbWUgdGhlIHRvcCAxMCBjdXN0b21lcnMgYnkgcmV2ZW51ZSB0aGlzIG1vbnRoJylcIlxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJxdWVyeWNyYWZ0LXRleHRhcmVhIHJlc2l6ZS1ub25lXCJcbiAgICAgICAgICAgICAgcm93cz17M31cbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzQUlMb2FkaW5nfVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVTZW5kTWVzc2FnZX1cbiAgICAgICAgICAgIGRpc2FibGVkPXshaW5wdXRWYWx1ZS50cmltKCkgfHwgaXNBSUxvYWRpbmd9XG4gICAgICAgICAgICBzaXplPVwibGdcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxTZW5kSWNvbiBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LW11dGVkLWZvcmVncm91bmQgbXQtMlwiPlxuICAgICAgICAgIFByZXNzIEVudGVyIHRvIHNlbmQsIFNoaWZ0K0VudGVyIGZvciBuZXcgbGluZVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlUmVmIiwidXNlRWZmZWN0IiwiQnV0dG9uIiwiQ2FyZCIsIkJhZGdlIiwiU2VuZEljb24iLCJCcmFpbkNpcmN1aXRJY29uIiwiVXNlckljb24iLCJDb3B5SWNvbiIsIlRodW1ic1VwSWNvbiIsIlRodW1ic0Rvd25JY29uIiwiU3BhcmtsZXNJY29uIiwiZm9ybWF0UmVsYXRpdmVUaW1lIiwidXNlUXVlcnlTdG9yZSIsIkNoYXRJbnRlcmZhY2UiLCJvblF1ZXJ5R2VuZXJhdGVkIiwibWVzc2FnZXMiLCJpc0FJTG9hZGluZyIsImdlbmVyYXRlUXVlcnkiLCJzZXRBY3RpdmVUYWIiLCJzYXZlUXVlcnlUb0RhdGFiYXNlIiwiaW5wdXRWYWx1ZSIsInNldElucHV0VmFsdWUiLCJtZXNzYWdlc0VuZFJlZiIsInRleHRhcmVhUmVmIiwic2Nyb2xsVG9Cb3R0b20iLCJjdXJyZW50Iiwic2Nyb2xsSW50b1ZpZXciLCJiZWhhdmlvciIsImhhbmRsZVNlbmRNZXNzYWdlIiwidHJpbSIsImlucHV0IiwiaGFuZGxlS2V5UHJlc3MiLCJlIiwia2V5Iiwic2hpZnRLZXkiLCJwcmV2ZW50RGVmYXVsdCIsImhhbmRsZUNvcHlRdWVyeSIsInNxbCIsIm5hdmlnYXRvciIsImNsaXBib2FyZCIsIndyaXRlVGV4dCIsImhhbmRsZVVzZVF1ZXJ5IiwidXNlcklucHV0IiwiZXhwbGFuYXRpb24iLCJlcnJvciIsImNvbnNvbGUiLCJkaXYiLCJjbGFzc05hbWUiLCJtYXAiLCJtZXNzYWdlIiwicm9sZSIsImNvbnRlbnQiLCJ0eXBlIiwibWV0YWRhdGEiLCJ2YXJpYW50Iiwic2l6ZSIsIm9uQ2xpY2siLCJwcmUiLCJjb2RlIiwic3Ryb25nIiwidGltZXN0YW1wIiwiaWQiLCJzcGFuIiwicmVmIiwidGV4dGFyZWEiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwidGFyZ2V0Iiwib25LZXlQcmVzcyIsInBsYWNlaG9sZGVyIiwicm93cyIsImRpc2FibGVkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/chat-interface.tsx\n"));

/***/ })

});