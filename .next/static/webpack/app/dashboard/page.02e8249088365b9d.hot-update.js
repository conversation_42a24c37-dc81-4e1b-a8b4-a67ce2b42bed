"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/chat/chat-interface.tsx":
/*!************************************************!*\
  !*** ./src/components/chat/chat-interface.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInterface: function() { return /* binding */ ChatInterface; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_BrainCircuitIcon_CopyIcon_SendIcon_SparklesIcon_ThumbsDownIcon_ThumbsUpIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BrainCircuitIcon,CopyIcon,SendIcon,SparklesIcon,ThumbsDownIcon,ThumbsUpIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain-circuit.js\");\n/* harmony import */ var _barrel_optimize_names_BrainCircuitIcon_CopyIcon_SendIcon_SparklesIcon_ThumbsDownIcon_ThumbsUpIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BrainCircuitIcon,CopyIcon,SendIcon,SparklesIcon,ThumbsDownIcon,ThumbsUpIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_BrainCircuitIcon_CopyIcon_SendIcon_SparklesIcon_ThumbsDownIcon_ThumbsUpIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BrainCircuitIcon,CopyIcon,SendIcon,SparklesIcon,ThumbsDownIcon,ThumbsUpIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_BrainCircuitIcon_CopyIcon_SendIcon_SparklesIcon_ThumbsDownIcon_ThumbsUpIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BrainCircuitIcon,CopyIcon,SendIcon,SparklesIcon,ThumbsDownIcon,ThumbsUpIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/thumbs-up.js\");\n/* harmony import */ var _barrel_optimize_names_BrainCircuitIcon_CopyIcon_SendIcon_SparklesIcon_ThumbsDownIcon_ThumbsUpIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BrainCircuitIcon,CopyIcon,SendIcon,SparklesIcon,ThumbsDownIcon,ThumbsUpIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/thumbs-down.js\");\n/* harmony import */ var _barrel_optimize_names_BrainCircuitIcon_CopyIcon_SendIcon_SparklesIcon_ThumbsDownIcon_ThumbsUpIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BrainCircuitIcon,CopyIcon,SendIcon,SparklesIcon,ThumbsDownIcon,ThumbsUpIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BrainCircuitIcon_CopyIcon_SendIcon_SparklesIcon_ThumbsDownIcon_ThumbsUpIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BrainCircuitIcon,CopyIcon,SendIcon,SparklesIcon,ThumbsDownIcon,ThumbsUpIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _stores_query_store__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/stores/query-store */ \"(app-pages-browser)/./src/stores/query-store.ts\");\n/* __next_internal_client_entry_do_not_use__ ChatInterface auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ChatInterface(param) {\n    let { onQueryGenerated } = param;\n    _s();\n    const { messages, isAILoading, generateQuery, setActiveTab, saveQueryToDatabase } = (0,_stores_query_store__WEBPACK_IMPORTED_MODULE_6__.useQueryStore)();\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages\n    ]);\n    const handleSendMessage = async ()=>{\n        if (!inputValue.trim() || isAILoading) return;\n        const input = inputValue;\n        setInputValue(\"\");\n        await generateQuery(input);\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    const handleCopyQuery = (sql)=>{\n        navigator.clipboard.writeText(sql);\n    };\n    const handleUseQuery = (sql)=>{\n        onQueryGenerated(sql);\n        setActiveTab(\"editor\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-6 space-y-4\",\n                children: [\n                    messages.map((message)=>{\n                        var _message_metadata;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3 \".concat(message.role === \"user\" ? \"justify-end\" : \"justify-start\"),\n                            children: [\n                                message.role === \"assistant\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 w-8 bg-primary rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BrainCircuitIcon_CopyIcon_SendIcon_SparklesIcon_ThumbsDownIcon_ThumbsUpIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4 text-primary-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-2xl \".concat(message.role === \"user\" ? \"order-first\" : \"\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"p-4 \".concat(message.role === \"user\" ? \"bg-primary text-primary-foreground\" : \"\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm\",\n                                                        children: message.content\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                        lineNumber: 88,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    message.type === \"query\" && ((_message_metadata = message.metadata) === null || _message_metadata === void 0 ? void 0 : _message_metadata.sql) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-muted rounded-lg p-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between mb-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                variant: \"secondary\",\n                                                                                className: \"text-xs\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BrainCircuitIcon_CopyIcon_SendIcon_SparklesIcon_ThumbsDownIcon_ThumbsUpIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                                        className: \"h-3 w-3 mr-1\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                                                        lineNumber: 95,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    \"Generated SQL\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                                                lineNumber: 94,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex gap-1\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                    variant: \"ghost\",\n                                                                                    size: \"sm\",\n                                                                                    onClick: ()=>handleCopyQuery(message.metadata.sql),\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BrainCircuitIcon_CopyIcon_SendIcon_SparklesIcon_ThumbsDownIcon_ThumbsUpIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                        className: \"h-3 w-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                                                        lineNumber: 104,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                                                    lineNumber: 99,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                                                lineNumber: 98,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                                        lineNumber: 93,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                                        className: \"text-xs font-mono overflow-x-auto\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                            children: message.metadata.sql\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                                            lineNumber: 109,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                                        lineNumber: 108,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                                lineNumber: 92,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            message.metadata.explanation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Explanation:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                                        lineNumber: 115,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \" \",\n                                                                    message.metadata.explanation\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                                lineNumber: 114,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        size: \"sm\",\n                                                                        onClick: ()=>handleUseQuery(message.metadata.sql),\n                                                                        children: \"Use in Editor\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                                        lineNumber: 120,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BrainCircuitIcon_CopyIcon_SendIcon_SparklesIcon_ThumbsDownIcon_ThumbsUpIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                className: \"h-3 w-3 mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                                                lineNumber: 127,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \"Good\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                                        lineNumber: 126,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BrainCircuitIcon_CopyIcon_SendIcon_SparklesIcon_ThumbsDownIcon_ThumbsUpIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                className: \"h-3 w-3 mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                                                lineNumber: 131,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \"Improve\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                                        lineNumber: 130,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                                lineNumber: 119,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-muted-foreground mt-2\",\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatRelativeTime)(message.timestamp)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, this),\n                                message.role === \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 w-8 bg-muted rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BrainCircuitIcon_CopyIcon_SendIcon_SparklesIcon_ThumbsDownIcon_ThumbsUpIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, message.id, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this);\n                    }),\n                    isAILoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-8 bg-primary rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BrainCircuitIcon_CopyIcon_SendIcon_SparklesIcon_ThumbsDownIcon_ThumbsUpIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4 text-primary-foreground animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"loading-spinner\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"AI is thinking...\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-border p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    ref: textareaRef,\n                                    value: inputValue,\n                                    onChange: (e)=>setInputValue(e.target.value),\n                                    onKeyPress: handleKeyPress,\n                                    placeholder: \"Describe the data you want to query... (e.g., 'Show me the top 10 customers by revenue this month')\",\n                                    className: \"querycraft-textarea resize-none\",\n                                    rows: 3,\n                                    disabled: isAILoading\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: handleSendMessage,\n                                disabled: !inputValue.trim() || isAILoading,\n                                size: \"lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BrainCircuitIcon_CopyIcon_SendIcon_SparklesIcon_ThumbsDownIcon_ThumbsUpIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-muted-foreground mt-2\",\n                        children: \"Press Enter to send, Shift+Enter for new line\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/components/chat/chat-interface.tsx\",\n        lineNumber: 69,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatInterface, \"MsN/VlKb5jNeiIOxxDL2BpT020w=\", false, function() {\n    return [\n        _stores_query_store__WEBPACK_IMPORTED_MODULE_6__.useQueryStore\n    ];\n});\n_c = ChatInterface;\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/chat-interface.tsx\n"));

/***/ })

});