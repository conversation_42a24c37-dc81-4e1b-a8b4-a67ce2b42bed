"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/users/route";
exports.ids = ["app/api/users/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fusers%2Froute&page=%2Fapi%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2Froute.ts&appDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fusers%2Froute&page=%2Fapi%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2Froute.ts&appDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_alexandresimasmaciel_Documents_ia_sistemas_query_craft_studio_2_src_app_api_users_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/users/route.ts */ \"(rsc)/./src/app/api/users/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/users/route\",\n        pathname: \"/api/users\",\n        filename: \"route\",\n        bundlePath: \"app/api/users/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/api/users/route.ts\",\n    nextConfigOutput,\n    userland: _Users_alexandresimasmaciel_Documents_ia_sistemas_query_craft_studio_2_src_app_api_users_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/users/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fusers%2Froute&page=%2Fapi%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2Froute.ts&appDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/users/route.ts":
/*!************************************!*\
  !*** ./src/app/api/users/route.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_database_services_user_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database/services/user-service */ \"(rsc)/./src/lib/database/services/user-service.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\n\n\n// Validation schemas\nconst createUserSchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().email(),\n    name: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().min(1).max(255),\n    avatar: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().url().optional()\n});\nconst updateUserSchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().min(1).max(255).optional(),\n    avatar: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().url().optional()\n});\n// GET /api/users - Get current user or user by email\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const email = searchParams.get(\"email\");\n        const id = searchParams.get(\"id\");\n        if (email) {\n            const user = await _lib_database_services_user_service__WEBPACK_IMPORTED_MODULE_1__.UserService.getUserByEmail(email);\n            if (!user) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"User not found\"\n                }, {\n                    status: 404\n                });\n            }\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(user);\n        }\n        if (id) {\n            const user = await _lib_database_services_user_service__WEBPACK_IMPORTED_MODULE_1__.UserService.getUserById(id);\n            if (!user) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"User not found\"\n                }, {\n                    status: 404\n                });\n            }\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(user);\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Email or ID parameter required\"\n        }, {\n            status: 400\n        });\n    } catch (error) {\n        console.error(\"GET /api/users error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/users - Create a new user\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const validatedData = createUserSchema.parse(body);\n        // Check if user already exists\n        const existingUser = await _lib_database_services_user_service__WEBPACK_IMPORTED_MODULE_1__.UserService.getUserByEmail(validatedData.email);\n        if (existingUser) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"User with this email already exists\"\n            }, {\n                status: 409\n            });\n        }\n        const user = await _lib_database_services_user_service__WEBPACK_IMPORTED_MODULE_1__.UserService.createUser(validatedData);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(user, {\n            status: 201\n        });\n    } catch (error) {\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_2__.z.ZodError) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Validation failed\",\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        console.error(\"POST /api/users error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT /api/users - Update user\nasync function PUT(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get(\"id\");\n        if (!id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"User ID required\"\n            }, {\n                status: 400\n            });\n        }\n        const body = await request.json();\n        const validatedData = updateUserSchema.parse(body);\n        const user = await _lib_database_services_user_service__WEBPACK_IMPORTED_MODULE_1__.UserService.updateUser(id, validatedData);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(user);\n    } catch (error) {\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_2__.z.ZodError) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Validation failed\",\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        console.error(\"PUT /api/users error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE /api/users - Delete user\nasync function DELETE(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get(\"id\");\n        if (!id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"User ID required\"\n            }, {\n                status: 400\n            });\n        }\n        await _lib_database_services_user_service__WEBPACK_IMPORTED_MODULE_1__.UserService.deleteUser(id);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true\n        });\n    } catch (error) {\n        console.error(\"DELETE /api/users error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/users/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/prisma.ts":
/*!************************************!*\
  !*** ./src/lib/database/prisma.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   disconnectDatabase: () => (/* binding */ disconnectDatabase),\n/* harmony export */   handleDatabaseError: () => (/* binding */ handleDatabaseError),\n/* harmony export */   prisma: () => (/* binding */ prisma),\n/* harmony export */   testDatabaseConnection: () => (/* binding */ testDatabaseConnection)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        \"query\",\n        \"error\",\n        \"warn\"\n    ],\n    errorFormat: \"pretty\"\n});\nif (true) globalForPrisma.prisma = prisma;\n// Helper function to handle database errors\nfunction handleDatabaseError(error) {\n    console.error(\"Database error:\", error);\n    if (error.code === \"P2002\") {\n        throw new Error(\"A record with this information already exists\");\n    }\n    if (error.code === \"P2025\") {\n        throw new Error(\"Record not found\");\n    }\n    if (error.code === \"P2003\") {\n        throw new Error(\"Foreign key constraint failed\");\n    }\n    throw new Error(\"Database operation failed\");\n}\n// Connection test function\nasync function testDatabaseConnection() {\n    try {\n        await prisma.$connect();\n        await prisma.$queryRaw`SELECT 1`;\n        return true;\n    } catch (error) {\n        console.error(\"Database connection failed:\", error);\n        return false;\n    }\n}\n// Graceful shutdown\nasync function disconnectDatabase() {\n    await prisma.$disconnect();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/services/user-service.ts":
/*!***************************************************!*\
  !*** ./src/lib/database/services/user-service.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserService: () => (/* binding */ UserService)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../prisma */ \"(rsc)/./src/lib/database/prisma.ts\");\n\nclass UserService {\n    // Create a new user\n    static async createUser(data) {\n        try {\n            return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.create({\n                data\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Get user by ID\n    static async getUserById(id) {\n        try {\n            return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n                where: {\n                    id\n                },\n                include: {\n                    databaseConnections: true,\n                    _count: {\n                        select: {\n                            generatedQueries: true,\n                            querySessions: true\n                        }\n                    }\n                }\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Get user by email\n    static async getUserByEmail(email) {\n        try {\n            return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n                where: {\n                    email\n                }\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Update user\n    static async updateUser(id, data) {\n        try {\n            return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n                where: {\n                    id\n                },\n                data\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Delete user\n    static async deleteUser(id) {\n        try {\n            await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.delete({\n                where: {\n                    id\n                }\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Get user with full profile data\n    static async getUserProfile(id) {\n        try {\n            return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n                where: {\n                    id\n                },\n                include: {\n                    databaseConnections: {\n                        where: {\n                            isActive: true\n                        },\n                        select: {\n                            id: true,\n                            name: true,\n                            databaseType: true,\n                            lastConnectedAt: true\n                        }\n                    },\n                    teamMemberships: {\n                        include: {\n                            team: {\n                                select: {\n                                    id: true,\n                                    name: true,\n                                    description: true\n                                }\n                            }\n                        }\n                    },\n                    _count: {\n                        select: {\n                            generatedQueries: true,\n                            querySessions: true,\n                            databaseConnections: true\n                        }\n                    }\n                }\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Get user statistics\n    static async getUserStats(id) {\n        try {\n            const [totalQueries, totalSessions, recentQueries, topDatabases] = await Promise.all([\n                _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.generatedQuery.count({\n                    where: {\n                        userId: id\n                    }\n                }),\n                _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.querySession.count({\n                    where: {\n                        userId: id\n                    }\n                }),\n                _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.generatedQuery.count({\n                    where: {\n                        userId: id,\n                        createdAt: {\n                            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)\n                        }\n                    }\n                }),\n                _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.generatedQuery.groupBy({\n                    by: [\n                        \"databaseType\"\n                    ],\n                    where: {\n                        userId: id\n                    },\n                    _count: {\n                        id: true\n                    },\n                    orderBy: {\n                        _count: {\n                            id: \"desc\"\n                        }\n                    },\n                    take: 3\n                })\n            ]);\n            return {\n                totalQueries,\n                totalSessions,\n                recentQueries,\n                topDatabases\n            };\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/services/user-service.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fusers%2Froute&page=%2Fapi%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2Froute.ts&appDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();