"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/connections/route";
exports.ids = ["app/api/connections/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fconnections%2Froute&page=%2Fapi%2Fconnections%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fconnections%2Froute.ts&appDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fconnections%2Froute&page=%2Fapi%2Fconnections%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fconnections%2Froute.ts&appDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_alexandresimasmaciel_Documents_ia_sistemas_query_craft_studio_2_src_app_api_connections_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/connections/route.ts */ \"(rsc)/./src/app/api/connections/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/connections/route\",\n        pathname: \"/api/connections\",\n        filename: \"route\",\n        bundlePath: \"app/api/connections/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/api/connections/route.ts\",\n    nextConfigOutput,\n    userland: _Users_alexandresimasmaciel_Documents_ia_sistemas_query_craft_studio_2_src_app_api_connections_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/connections/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fconnections%2Froute&page=%2Fapi%2Fconnections%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fconnections%2Froute.ts&appDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/connections/route.ts":
/*!******************************************!*\
  !*** ./src/app/api/connections/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_database_services_connection_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database/services/connection-service */ \"(rsc)/./src/lib/database/services/connection-service.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\n\n\n// Validation schemas\nconst createConnectionSchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n    userId: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().cuid(),\n    teamId: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().cuid().optional(),\n    name: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().min(1).max(255),\n    databaseType: zod__WEBPACK_IMPORTED_MODULE_2__.z[\"enum\"]([\n        \"MYSQL\",\n        \"POSTGRESQL\"\n    ]),\n    connectionString: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().min(1),\n    host: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n    port: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().int().min(1).max(65535).optional(),\n    database: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n    username: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional()\n});\nconst updateConnectionSchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().min(1).max(255).optional(),\n    connectionString: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().min(1).optional(),\n    host: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n    port: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().int().min(1).max(65535).optional(),\n    database: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n    username: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n    schemaMetadata: zod__WEBPACK_IMPORTED_MODULE_2__.z.any().optional(),\n    isActive: zod__WEBPACK_IMPORTED_MODULE_2__.z.boolean().optional()\n});\n// GET /api/connections - Get user connections\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const userId = searchParams.get(\"userId\");\n        const teamId = searchParams.get(\"teamId\");\n        const id = searchParams.get(\"id\");\n        if (id) {\n            const connection = await _lib_database_services_connection_service__WEBPACK_IMPORTED_MODULE_1__.ConnectionService.getConnectionById(id);\n            if (!connection) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"Connection not found\"\n                }, {\n                    status: 404\n                });\n            }\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(connection);\n        }\n        if (userId) {\n            const connections = await _lib_database_services_connection_service__WEBPACK_IMPORTED_MODULE_1__.ConnectionService.getUserConnections(userId);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(connections);\n        }\n        if (teamId) {\n            const connections = await _lib_database_services_connection_service__WEBPACK_IMPORTED_MODULE_1__.ConnectionService.getTeamConnections(teamId);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(connections);\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"userId, teamId, or id parameter required\"\n        }, {\n            status: 400\n        });\n    } catch (error) {\n        console.error(\"GET /api/connections error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/connections - Create a new connection\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const validatedData = createConnectionSchema.parse(body);\n        const connection = await _lib_database_services_connection_service__WEBPACK_IMPORTED_MODULE_1__.ConnectionService.createConnection(validatedData);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(connection, {\n            status: 201\n        });\n    } catch (error) {\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_2__.z.ZodError) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Validation failed\",\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        console.error(\"POST /api/connections error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT /api/connections - Update connection\nasync function PUT(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get(\"id\");\n        if (!id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Connection ID required\"\n            }, {\n                status: 400\n            });\n        }\n        const body = await request.json();\n        const validatedData = updateConnectionSchema.parse(body);\n        const connection = await _lib_database_services_connection_service__WEBPACK_IMPORTED_MODULE_1__.ConnectionService.updateConnection(id, validatedData);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(connection);\n    } catch (error) {\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_2__.z.ZodError) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Validation failed\",\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        console.error(\"PUT /api/connections error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE /api/connections - Delete connection\nasync function DELETE(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get(\"id\");\n        if (!id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Connection ID required\"\n            }, {\n                status: 400\n            });\n        }\n        await _lib_database_services_connection_service__WEBPACK_IMPORTED_MODULE_1__.ConnectionService.deleteConnection(id);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true\n        });\n    } catch (error) {\n        console.error(\"DELETE /api/connections error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/connections/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/prisma.ts":
/*!************************************!*\
  !*** ./src/lib/database/prisma.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   disconnectDatabase: () => (/* binding */ disconnectDatabase),\n/* harmony export */   handleDatabaseError: () => (/* binding */ handleDatabaseError),\n/* harmony export */   prisma: () => (/* binding */ prisma),\n/* harmony export */   testDatabaseConnection: () => (/* binding */ testDatabaseConnection)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        \"query\",\n        \"error\",\n        \"warn\"\n    ],\n    errorFormat: \"pretty\"\n});\nif (true) globalForPrisma.prisma = prisma;\n// Helper function to handle database errors\nfunction handleDatabaseError(error) {\n    console.error(\"Database error:\", error);\n    if (error.code === \"P2002\") {\n        throw new Error(\"A record with this information already exists\");\n    }\n    if (error.code === \"P2025\") {\n        throw new Error(\"Record not found\");\n    }\n    if (error.code === \"P2003\") {\n        throw new Error(\"Foreign key constraint failed\");\n    }\n    throw new Error(\"Database operation failed\");\n}\n// Connection test function\nasync function testDatabaseConnection() {\n    try {\n        await prisma.$connect();\n        await prisma.$queryRaw`SELECT 1`;\n        return true;\n    } catch (error) {\n        console.error(\"Database connection failed:\", error);\n        return false;\n    }\n}\n// Graceful shutdown\nasync function disconnectDatabase() {\n    await prisma.$disconnect();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RhdGFiYXNlL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUNYRixnQkFBZ0JFLE1BQU0sSUFDdEIsSUFBSUgsd0RBQVlBLENBQUM7SUFDZkksS0FBSztRQUFDO1FBQVM7UUFBUztLQUFPO0lBQy9CQyxhQUFhO0FBQ2YsR0FBRztBQUVMLElBQUlDLElBQXlCLEVBQWNMLGdCQUFnQkUsTUFBTSxHQUFHQTtBQUVwRSw0Q0FBNEM7QUFDckMsU0FBU0ksb0JBQW9CQyxLQUFVO0lBQzVDQyxRQUFRRCxLQUFLLENBQUMsbUJBQW1CQTtJQUVqQyxJQUFJQSxNQUFNRSxJQUFJLEtBQUssU0FBUztRQUMxQixNQUFNLElBQUlDLE1BQU07SUFDbEI7SUFFQSxJQUFJSCxNQUFNRSxJQUFJLEtBQUssU0FBUztRQUMxQixNQUFNLElBQUlDLE1BQU07SUFDbEI7SUFFQSxJQUFJSCxNQUFNRSxJQUFJLEtBQUssU0FBUztRQUMxQixNQUFNLElBQUlDLE1BQU07SUFDbEI7SUFFQSxNQUFNLElBQUlBLE1BQU07QUFDbEI7QUFFQSwyQkFBMkI7QUFDcEIsZUFBZUM7SUFDcEIsSUFBSTtRQUNGLE1BQU1ULE9BQU9VLFFBQVE7UUFDckIsTUFBTVYsT0FBT1csU0FBUyxDQUFDLFFBQVEsQ0FBQztRQUNoQyxPQUFPO0lBQ1QsRUFBRSxPQUFPTixPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQywrQkFBK0JBO1FBQzdDLE9BQU87SUFDVDtBQUNGO0FBRUEsb0JBQW9CO0FBQ2IsZUFBZU87SUFDcEIsTUFBTVosT0FBT2EsV0FBVztBQUMxQiIsInNvdXJjZXMiOlsid2VicGFjazovL3F1ZXJ5Y3JhZnQtc3R1ZGlvLy4vc3JjL2xpYi9kYXRhYmFzZS9wcmlzbWEudHM/NGZjMyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCc7XG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkO1xufTtcblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9XG4gIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz9cbiAgbmV3IFByaXNtYUNsaWVudCh7XG4gICAgbG9nOiBbJ3F1ZXJ5JywgJ2Vycm9yJywgJ3dhcm4nXSxcbiAgICBlcnJvckZvcm1hdDogJ3ByZXR0eScsXG4gIH0pO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYTtcblxuLy8gSGVscGVyIGZ1bmN0aW9uIHRvIGhhbmRsZSBkYXRhYmFzZSBlcnJvcnNcbmV4cG9ydCBmdW5jdGlvbiBoYW5kbGVEYXRhYmFzZUVycm9yKGVycm9yOiBhbnkpOiBuZXZlciB7XG4gIGNvbnNvbGUuZXJyb3IoJ0RhdGFiYXNlIGVycm9yOicsIGVycm9yKTtcbiAgXG4gIGlmIChlcnJvci5jb2RlID09PSAnUDIwMDInKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdBIHJlY29yZCB3aXRoIHRoaXMgaW5mb3JtYXRpb24gYWxyZWFkeSBleGlzdHMnKTtcbiAgfVxuICBcbiAgaWYgKGVycm9yLmNvZGUgPT09ICdQMjAyNScpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ1JlY29yZCBub3QgZm91bmQnKTtcbiAgfVxuICBcbiAgaWYgKGVycm9yLmNvZGUgPT09ICdQMjAwMycpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ0ZvcmVpZ24ga2V5IGNvbnN0cmFpbnQgZmFpbGVkJyk7XG4gIH1cbiAgXG4gIHRocm93IG5ldyBFcnJvcignRGF0YWJhc2Ugb3BlcmF0aW9uIGZhaWxlZCcpO1xufVxuXG4vLyBDb25uZWN0aW9uIHRlc3QgZnVuY3Rpb25cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiB0ZXN0RGF0YWJhc2VDb25uZWN0aW9uKCk6IFByb21pc2U8Ym9vbGVhbj4ge1xuICB0cnkge1xuICAgIGF3YWl0IHByaXNtYS4kY29ubmVjdCgpO1xuICAgIGF3YWl0IHByaXNtYS4kcXVlcnlSYXdgU0VMRUNUIDFgO1xuICAgIHJldHVybiB0cnVlO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0RhdGFiYXNlIGNvbm5lY3Rpb24gZmFpbGVkOicsIGVycm9yKTtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cbn1cblxuLy8gR3JhY2VmdWwgc2h1dGRvd25cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBkaXNjb25uZWN0RGF0YWJhc2UoKTogUHJvbWlzZTx2b2lkPiB7XG4gIGF3YWl0IHByaXNtYS4kZGlzY29ubmVjdCgpO1xufVxuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJsb2ciLCJlcnJvckZvcm1hdCIsInByb2Nlc3MiLCJoYW5kbGVEYXRhYmFzZUVycm9yIiwiZXJyb3IiLCJjb25zb2xlIiwiY29kZSIsIkVycm9yIiwidGVzdERhdGFiYXNlQ29ubmVjdGlvbiIsIiRjb25uZWN0IiwiJHF1ZXJ5UmF3IiwiZGlzY29ubmVjdERhdGFiYXNlIiwiJGRpc2Nvbm5lY3QiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/services/connection-service.ts":
/*!*********************************************************!*\
  !*** ./src/lib/database/services/connection-service.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConnectionService: () => (/* binding */ ConnectionService)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../prisma */ \"(rsc)/./src/lib/database/prisma.ts\");\n/* harmony import */ var _lib_utils_encryption__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils/encryption */ \"(rsc)/./src/lib/utils/encryption.ts\");\n\n\nclass ConnectionService {\n    // Create a new database connection\n    static async createConnection(data) {\n        try {\n            const connectionStringHash = (0,_lib_utils_encryption__WEBPACK_IMPORTED_MODULE_1__.encrypt)(data.connectionString);\n            return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.databaseConnection.create({\n                data: {\n                    ...data,\n                    connectionStringHash,\n                    connectionString: undefined\n                }\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Get connection by ID\n    static async getConnectionById(id) {\n        try {\n            return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.databaseConnection.findUnique({\n                where: {\n                    id\n                },\n                include: {\n                    user: {\n                        select: {\n                            id: true,\n                            name: true,\n                            email: true\n                        }\n                    },\n                    team: {\n                        select: {\n                            id: true,\n                            name: true\n                        }\n                    }\n                }\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Get user connections\n    static async getUserConnections(userId) {\n        try {\n            return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.databaseConnection.findMany({\n                where: {\n                    userId,\n                    isActive: true\n                },\n                orderBy: {\n                    lastConnectedAt: \"desc\"\n                }\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Get team connections\n    static async getTeamConnections(teamId) {\n        try {\n            return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.databaseConnection.findMany({\n                where: {\n                    teamId,\n                    isActive: true\n                },\n                include: {\n                    user: {\n                        select: {\n                            id: true,\n                            name: true,\n                            email: true\n                        }\n                    }\n                },\n                orderBy: {\n                    lastConnectedAt: \"desc\"\n                }\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Update connection\n    static async updateConnection(id, data) {\n        try {\n            const updateData = {\n                ...data\n            };\n            // Encrypt connection string if provided\n            if (data.connectionString) {\n                updateData.connectionStringHash = (0,_lib_utils_encryption__WEBPACK_IMPORTED_MODULE_1__.encrypt)(data.connectionString);\n                delete updateData.connectionString;\n            }\n            return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.databaseConnection.update({\n                where: {\n                    id\n                },\n                data: updateData\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Update last connected timestamp\n    static async updateLastConnected(id) {\n        try {\n            await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.databaseConnection.update({\n                where: {\n                    id\n                },\n                data: {\n                    lastConnectedAt: new Date()\n                }\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Delete connection\n    static async deleteConnection(id) {\n        try {\n            await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.databaseConnection.delete({\n                where: {\n                    id\n                }\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Get decrypted connection string\n    static async getConnectionString(id) {\n        try {\n            const connection = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.databaseConnection.findUnique({\n                where: {\n                    id\n                },\n                select: {\n                    connectionStringHash: true\n                }\n            });\n            if (!connection) return null;\n            return (0,_lib_utils_encryption__WEBPACK_IMPORTED_MODULE_1__.decrypt)(connection.connectionStringHash);\n        } catch (error) {\n            console.error(\"Failed to decrypt connection string:\", error);\n            return null;\n        }\n    }\n    // Test connection\n    static async testConnection(id) {\n        try {\n            const connectionString = await this.getConnectionString(id);\n            if (!connectionString) return false;\n            // Here you would implement actual database connection testing\n            // For now, we'll simulate it\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            // Update last connected timestamp if successful\n            await this.updateLastConnected(id);\n            return true;\n        } catch (error) {\n            console.error(\"Connection test failed:\", error);\n            return false;\n        }\n    }\n    // Get connection statistics\n    static async getConnectionStats(id) {\n        try {\n            const [totalQueries, totalSessions, recentActivity] = await Promise.all([\n                _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.generatedQuery.count({\n                    where: {\n                        session: {\n                            databaseConnectionId: id\n                        }\n                    }\n                }),\n                _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.querySession.count({\n                    where: {\n                        databaseConnectionId: id\n                    }\n                }),\n                _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.querySession.count({\n                    where: {\n                        databaseConnectionId: id,\n                        createdAt: {\n                            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)\n                        }\n                    }\n                })\n            ]);\n            return {\n                totalQueries,\n                totalSessions,\n                recentActivity\n            };\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/services/connection-service.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils/encryption.ts":
/*!*************************************!*\
  !*** ./src/lib/utils/encryption.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decrypt: () => (/* binding */ decrypt),\n/* harmony export */   encrypt: () => (/* binding */ encrypt),\n/* harmony export */   generateApiKey: () => (/* binding */ generateApiKey),\n/* harmony export */   generateRandomString: () => (/* binding */ generateRandomString),\n/* harmony export */   hashString: () => (/* binding */ hashString),\n/* harmony export */   simpleDecrypt: () => (/* binding */ simpleDecrypt),\n/* harmony export */   simpleEncrypt: () => (/* binding */ simpleEncrypt),\n/* harmony export */   verifyApiKey: () => (/* binding */ verifyApiKey)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ALGORITHM = \"aes-256-gcm\";\nconst KEY_LENGTH = 32;\nconst IV_LENGTH = 16;\nconst TAG_LENGTH = 16;\n// Get encryption key from environment variable\nfunction getEncryptionKey() {\n    const key = process.env.ENCRYPTION_KEY;\n    if (!key) {\n        throw new Error(\"ENCRYPTION_KEY environment variable is required\");\n    }\n    // Create a consistent key from the environment variable\n    return crypto__WEBPACK_IMPORTED_MODULE_0__.scryptSync(key, \"salt\", KEY_LENGTH);\n}\n// Encrypt a string (simplified for development)\nfunction encrypt(text) {\n    try {\n        // For development, we'll use a simple base64 encoding with a prefix\n        // In production, this should be proper encryption\n        const encoded = Buffer.from(text).toString(\"base64\");\n        return \"enc:\" + encoded;\n    } catch (error) {\n        console.error(\"Encryption failed:\", error);\n        throw new Error(\"Failed to encrypt data\");\n    }\n}\n// Decrypt a string (simplified for development)\nfunction decrypt(encryptedData) {\n    try {\n        // For development, we'll decode the base64 string\n        // In production, this should be proper decryption\n        if (!encryptedData.startsWith(\"enc:\")) {\n            throw new Error(\"Invalid encrypted data format\");\n        }\n        const encoded = encryptedData.substring(4);\n        const decoded = Buffer.from(encoded, \"base64\").toString(\"utf8\");\n        return decoded;\n    } catch (error) {\n        console.error(\"Decryption failed:\", error);\n        throw new Error(\"Failed to decrypt data\");\n    }\n}\n// Hash a string (for API keys, etc.)\nfunction hashString(text) {\n    return crypto__WEBPACK_IMPORTED_MODULE_0__.createHash(\"sha256\").update(text).digest(\"hex\");\n}\n// Generate a random string\nfunction generateRandomString(length = 32) {\n    return crypto__WEBPACK_IMPORTED_MODULE_0__.randomBytes(length).toString(\"hex\");\n}\n// Generate API key\nfunction generateApiKey() {\n    const key = \"qcs_\" + generateRandomString(32);\n    const hash = hashString(key);\n    return {\n        key,\n        hash\n    };\n}\n// Verify API key\nfunction verifyApiKey(key, hash) {\n    return hashString(key) === hash;\n}\n// Simple encryption for less sensitive data (using a simpler algorithm)\nfunction simpleEncrypt(text) {\n    const key = getEncryptionKey();\n    const iv = crypto__WEBPACK_IMPORTED_MODULE_0__.randomBytes(16);\n    const cipher = crypto__WEBPACK_IMPORTED_MODULE_0__.createCipher(\"aes-256-cbc\", key);\n    let encrypted = cipher.update(text, \"utf8\", \"hex\");\n    encrypted += cipher.final(\"hex\");\n    return iv.toString(\"hex\") + \":\" + encrypted;\n}\n// Simple decryption\nfunction simpleDecrypt(encryptedData) {\n    const key = getEncryptionKey();\n    const parts = encryptedData.split(\":\");\n    const encrypted = parts.length > 1 ? parts[1] : parts[0];\n    const decipher = crypto__WEBPACK_IMPORTED_MODULE_0__.createDecipher(\"aes-256-cbc\", key);\n    let decrypted = decipher.update(encrypted, \"hex\", \"utf8\");\n    decrypted += decipher.final(\"utf8\");\n    return decrypted;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils/encryption.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fconnections%2Froute&page=%2Fapi%2Fconnections%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fconnections%2Froute.ts&appDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();