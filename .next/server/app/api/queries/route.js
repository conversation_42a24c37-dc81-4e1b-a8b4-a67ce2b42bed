"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/queries/route";
exports.ids = ["app/api/queries/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fqueries%2Froute&page=%2Fapi%2Fqueries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqueries%2Froute.ts&appDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fqueries%2Froute&page=%2Fapi%2Fqueries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqueries%2Froute.ts&appDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_alexandresimasmaciel_Documents_ia_sistemas_query_craft_studio_2_src_app_api_queries_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/queries/route.ts */ \"(rsc)/./src/app/api/queries/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/queries/route\",\n        pathname: \"/api/queries\",\n        filename: \"route\",\n        bundlePath: \"app/api/queries/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/ia-sistemas/query-craft-studio-2/src/app/api/queries/route.ts\",\n    nextConfigOutput,\n    userland: _Users_alexandresimasmaciel_Documents_ia_sistemas_query_craft_studio_2_src_app_api_queries_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/queries/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fqueries%2Froute&page=%2Fapi%2Fqueries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqueries%2Froute.ts&appDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/queries/route.ts":
/*!**************************************!*\
  !*** ./src/app/api/queries/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_database_services_query_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database/services/query-service */ \"(rsc)/./src/lib/database/services/query-service.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\n\n\n// Validation schemas\nconst createQuerySchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n    sessionId: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().cuid(),\n    userId: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().cuid(),\n    userInput: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().min(1),\n    generatedSQL: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().min(1),\n    explanation: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n    databaseType: zod__WEBPACK_IMPORTED_MODULE_2__.z[\"enum\"]([\n        \"MYSQL\",\n        \"POSTGRESQL\"\n    ]),\n    executionTime: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().int().min(0).optional(),\n    rowsAffected: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().int().min(0).optional(),\n    performanceData: zod__WEBPACK_IMPORTED_MODULE_2__.z.any().optional(),\n    optimizationTips: zod__WEBPACK_IMPORTED_MODULE_2__.z.any().optional()\n});\nconst updateQuerySchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n    status: zod__WEBPACK_IMPORTED_MODULE_2__.z[\"enum\"]([\n        \"GENERATED\",\n        \"EXECUTED\",\n        \"FAILED\",\n        \"OPTIMIZED\"\n    ]).optional(),\n    executionTime: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().int().min(0).optional(),\n    rowsAffected: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().int().min(0).optional(),\n    errorMessage: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n    performanceData: zod__WEBPACK_IMPORTED_MODULE_2__.z.any().optional(),\n    userFeedback: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().int().min(1).max(5).optional()\n});\nconst createSessionSchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n    userId: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().cuid(),\n    databaseConnectionId: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().cuid(),\n    name: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n    sessionData: zod__WEBPACK_IMPORTED_MODULE_2__.z.any().optional()\n});\n// GET /api/queries - Get queries or sessions\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const userId = searchParams.get(\"userId\");\n        const sessionId = searchParams.get(\"sessionId\");\n        const type = searchParams.get(\"type\"); // 'queries' or 'sessions'\n        const limit = parseInt(searchParams.get(\"limit\") || \"20\");\n        const databaseType = searchParams.get(\"databaseType\");\n        if (type === \"sessions\" && userId) {\n            const sessions = await _lib_database_services_query_service__WEBPACK_IMPORTED_MODULE_1__.QueryService.getUserSessions(userId, limit);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(sessions);\n        }\n        if (type === \"history\" && userId) {\n            const queries = await _lib_database_services_query_service__WEBPACK_IMPORTED_MODULE_1__.QueryService.getUserQueryHistory(userId, limit, databaseType || undefined);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(queries);\n        }\n        if (sessionId) {\n            const session = await _lib_database_services_query_service__WEBPACK_IMPORTED_MODULE_1__.QueryService.getSessionById(sessionId);\n            if (!session) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"Session not found\"\n                }, {\n                    status: 404\n                });\n            }\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(session);\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Invalid parameters. Use type=sessions&userId=X or type=history&userId=X or sessionId=X\"\n        }, {\n            status: 400\n        });\n    } catch (error) {\n        console.error(\"GET /api/queries error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/queries - Create a new query or session\nasync function POST(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const type = searchParams.get(\"type\"); // 'query' or 'session'\n        const body = await request.json();\n        if (type === \"session\") {\n            const validatedData = createSessionSchema.parse(body);\n            const session = await _lib_database_services_query_service__WEBPACK_IMPORTED_MODULE_1__.QueryService.createSession(validatedData);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(session, {\n                status: 201\n            });\n        }\n        if (type === \"query\") {\n            const validatedData = createQuerySchema.parse(body);\n            const query = await _lib_database_services_query_service__WEBPACK_IMPORTED_MODULE_1__.QueryService.createGeneratedQuery(validatedData);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(query, {\n                status: 201\n            });\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Type parameter required (query or session)\"\n        }, {\n            status: 400\n        });\n    } catch (error) {\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_2__.z.ZodError) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Validation failed\",\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        console.error(\"POST /api/queries error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT /api/queries - Update query or session\nasync function PUT(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get(\"id\");\n        const type = searchParams.get(\"type\"); // 'query' or 'session'\n        if (!id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"ID required\"\n            }, {\n                status: 400\n            });\n        }\n        const body = await request.json();\n        if (type === \"query\") {\n            const validatedData = updateQuerySchema.parse(body);\n            if (validatedData.userFeedback) {\n                const query = await _lib_database_services_query_service__WEBPACK_IMPORTED_MODULE_1__.QueryService.addQueryFeedback(id, validatedData.userFeedback);\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(query);\n            } else {\n                const query = await _lib_database_services_query_service__WEBPACK_IMPORTED_MODULE_1__.QueryService.updateQueryExecution(id, validatedData);\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(query);\n            }\n        }\n        if (type === \"session\") {\n            const sessionData = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n                name: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n                sessionData: zod__WEBPACK_IMPORTED_MODULE_2__.z.any().optional(),\n                isActive: zod__WEBPACK_IMPORTED_MODULE_2__.z.boolean().optional()\n            }).parse(body);\n            const session = await _lib_database_services_query_service__WEBPACK_IMPORTED_MODULE_1__.QueryService.updateSession(id, sessionData);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(session);\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Type parameter required (query or session)\"\n        }, {\n            status: 400\n        });\n    } catch (error) {\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_2__.z.ZodError) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Validation failed\",\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        console.error(\"PUT /api/queries error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE /api/queries - Delete session\nasync function DELETE(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get(\"id\");\n        if (!id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Session ID required\"\n            }, {\n                status: 400\n            });\n        }\n        await _lib_database_services_query_service__WEBPACK_IMPORTED_MODULE_1__.QueryService.deleteSession(id);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true\n        });\n    } catch (error) {\n        console.error(\"DELETE /api/queries error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/queries/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/prisma.ts":
/*!************************************!*\
  !*** ./src/lib/database/prisma.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   disconnectDatabase: () => (/* binding */ disconnectDatabase),\n/* harmony export */   handleDatabaseError: () => (/* binding */ handleDatabaseError),\n/* harmony export */   prisma: () => (/* binding */ prisma),\n/* harmony export */   testDatabaseConnection: () => (/* binding */ testDatabaseConnection)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        \"query\",\n        \"error\",\n        \"warn\"\n    ],\n    errorFormat: \"pretty\"\n});\nif (true) globalForPrisma.prisma = prisma;\n// Helper function to handle database errors\nfunction handleDatabaseError(error) {\n    console.error(\"Database error:\", error);\n    if (error.code === \"P2002\") {\n        throw new Error(\"A record with this information already exists\");\n    }\n    if (error.code === \"P2025\") {\n        throw new Error(\"Record not found\");\n    }\n    if (error.code === \"P2003\") {\n        throw new Error(\"Foreign key constraint failed\");\n    }\n    throw new Error(\"Database operation failed\");\n}\n// Connection test function\nasync function testDatabaseConnection() {\n    try {\n        await prisma.$connect();\n        await prisma.$queryRaw`SELECT 1`;\n        return true;\n    } catch (error) {\n        console.error(\"Database connection failed:\", error);\n        return false;\n    }\n}\n// Graceful shutdown\nasync function disconnectDatabase() {\n    await prisma.$disconnect();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/services/query-service.ts":
/*!****************************************************!*\
  !*** ./src/lib/database/services/query-service.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryService: () => (/* binding */ QueryService)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../prisma */ \"(rsc)/./src/lib/database/prisma.ts\");\n\nclass QueryService {\n    // Create a new query session\n    static async createSession(data) {\n        try {\n            return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.querySession.create({\n                data: {\n                    ...data,\n                    sessionData: data.sessionData || {}\n                }\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Get session by ID\n    static async getSessionById(id) {\n        try {\n            return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.querySession.findUnique({\n                where: {\n                    id\n                },\n                include: {\n                    user: {\n                        select: {\n                            id: true,\n                            name: true,\n                            email: true\n                        }\n                    },\n                    databaseConnection: {\n                        select: {\n                            id: true,\n                            name: true,\n                            databaseType: true\n                        }\n                    },\n                    generatedQueries: {\n                        orderBy: {\n                            createdAt: \"desc\"\n                        },\n                        take: 10\n                    },\n                    chatMessages: {\n                        orderBy: {\n                            createdAt: \"asc\"\n                        }\n                    }\n                }\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Get user sessions\n    static async getUserSessions(userId, limit = 20) {\n        try {\n            return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.querySession.findMany({\n                where: {\n                    userId,\n                    isActive: true\n                },\n                include: {\n                    databaseConnection: {\n                        select: {\n                            id: true,\n                            name: true,\n                            databaseType: true\n                        }\n                    },\n                    _count: {\n                        select: {\n                            generatedQueries: true,\n                            chatMessages: true\n                        }\n                    }\n                },\n                orderBy: {\n                    updatedAt: \"desc\"\n                },\n                take: limit\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Create a generated query\n    static async createGeneratedQuery(data) {\n        try {\n            return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.generatedQuery.create({\n                data\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Update query execution results\n    static async updateQueryExecution(id, data) {\n        try {\n            return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.generatedQuery.update({\n                where: {\n                    id\n                },\n                data\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Add user feedback to query\n    static async addQueryFeedback(id, feedback) {\n        try {\n            return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.generatedQuery.update({\n                where: {\n                    id\n                },\n                data: {\n                    userFeedback: feedback\n                }\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Get user query history\n    static async getUserQueryHistory(userId, limit = 50, databaseType) {\n        try {\n            return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.generatedQuery.findMany({\n                where: {\n                    userId,\n                    ...databaseType && {\n                        databaseType\n                    }\n                },\n                include: {\n                    session: {\n                        select: {\n                            id: true,\n                            name: true,\n                            databaseConnection: {\n                                select: {\n                                    id: true,\n                                    name: true\n                                }\n                            }\n                        }\n                    }\n                },\n                orderBy: {\n                    createdAt: \"desc\"\n                },\n                take: limit\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Add chat message\n    static async addChatMessage(data) {\n        try {\n            return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.chatMessage.create({\n                data\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Get session chat messages\n    static async getSessionMessages(sessionId) {\n        try {\n            return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.chatMessage.findMany({\n                where: {\n                    sessionId\n                },\n                orderBy: {\n                    createdAt: \"asc\"\n                }\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Update session\n    static async updateSession(id, data) {\n        try {\n            return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.querySession.update({\n                where: {\n                    id\n                },\n                data\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Delete session\n    static async deleteSession(id) {\n        try {\n            await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.querySession.delete({\n                where: {\n                    id\n                }\n            });\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n    // Get query analytics\n    static async getQueryAnalytics(userId, days = 30) {\n        try {\n            const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);\n            const [totalQueries, successfulQueries, averageExecutionTime, topDatabases, dailyActivity] = await Promise.all([\n                _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.generatedQuery.count({\n                    where: {\n                        userId,\n                        createdAt: {\n                            gte: startDate\n                        }\n                    }\n                }),\n                _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.generatedQuery.count({\n                    where: {\n                        userId,\n                        status: \"EXECUTED\",\n                        createdAt: {\n                            gte: startDate\n                        }\n                    }\n                }),\n                _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.generatedQuery.aggregate({\n                    where: {\n                        userId,\n                        executionTime: {\n                            not: null\n                        },\n                        createdAt: {\n                            gte: startDate\n                        }\n                    },\n                    _avg: {\n                        executionTime: true\n                    }\n                }),\n                _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.generatedQuery.groupBy({\n                    by: [\n                        \"databaseType\"\n                    ],\n                    where: {\n                        userId,\n                        createdAt: {\n                            gte: startDate\n                        }\n                    },\n                    _count: {\n                        id: true\n                    },\n                    orderBy: {\n                        _count: {\n                            id: \"desc\"\n                        }\n                    }\n                }),\n                _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.$queryRaw`\n          SELECT \n            DATE(created_at) as date,\n            COUNT(*) as count\n          FROM generated_queries \n          WHERE user_id = ${userId} \n            AND created_at >= ${startDate}\n          GROUP BY DATE(created_at)\n          ORDER BY date DESC\n        `\n            ]);\n            return {\n                totalQueries,\n                successfulQueries,\n                successRate: totalQueries > 0 ? successfulQueries / totalQueries * 100 : 0,\n                averageExecutionTime: averageExecutionTime._avg.executionTime || 0,\n                topDatabases,\n                dailyActivity\n            };\n        } catch (error) {\n            (0,_prisma__WEBPACK_IMPORTED_MODULE_0__.handleDatabaseError)(error);\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/services/query-service.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fqueries%2Froute&page=%2Fapi%2Fqueries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqueries%2Froute.ts&appDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fquery-craft-studio-2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();