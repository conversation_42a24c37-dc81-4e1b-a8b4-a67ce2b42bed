"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui";
exports.ids = ["vendor-chunks/@radix-ui"];
exports.modules = {

/***/ "(ssr)/./node_modules/@radix-ui/number/dist/index.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@radix-ui/number/dist/index.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clamp: () => (/* binding */ clamp)\n/* harmony export */ });\n// packages/core/number/src/number.ts\nfunction clamp(value, [min, max]) {\n    return Math.min(max, Math.max(min, value));\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL251bWJlci9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEscUNBQXFDO0FBQ3JDLFNBQVNBLE1BQU1DLEtBQUssRUFBRSxDQUFDQyxLQUFLQyxJQUFJO0lBQzlCLE9BQU9DLEtBQUtGLEdBQUcsQ0FBQ0MsS0FBS0MsS0FBS0QsR0FBRyxDQUFDRCxLQUFLRDtBQUNyQztBQUdFLENBQ0Ysa0NBQWtDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcXVlcnljcmFmdC1zdHVkaW8vLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL251bWJlci9kaXN0L2luZGV4Lm1qcz8wYTc3Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL2NvcmUvbnVtYmVyL3NyYy9udW1iZXIudHNcbmZ1bmN0aW9uIGNsYW1wKHZhbHVlLCBbbWluLCBtYXhdKSB7XG4gIHJldHVybiBNYXRoLm1pbihtYXgsIE1hdGgubWF4KG1pbiwgdmFsdWUpKTtcbn1cbmV4cG9ydCB7XG4gIGNsYW1wXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbImNsYW1wIiwidmFsdWUiLCJtaW4iLCJtYXgiLCJNYXRoIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/number/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@radix-ui/primitive/dist/index.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeEventHandlers: () => (/* binding */ composeEventHandlers)\n/* harmony export */ });\n// packages/core/primitive/src/primitive.tsx\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {\n    return function handleEvent(event) {\n        originalEventHandler?.(event);\n        if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n            return ourEventHandler?.(event);\n        }\n    };\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3ByaW1pdGl2ZS9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsNENBQTRDO0FBQzVDLFNBQVNBLHFCQUFxQkMsb0JBQW9CLEVBQUVDLGVBQWUsRUFBRSxFQUFFQywyQkFBMkIsSUFBSSxFQUFFLEdBQUcsQ0FBQyxDQUFDO0lBQzNHLE9BQU8sU0FBU0MsWUFBWUMsS0FBSztRQUMvQkosdUJBQXVCSTtRQUN2QixJQUFJRiw2QkFBNkIsU0FBUyxDQUFDRSxNQUFNQyxnQkFBZ0IsRUFBRTtZQUNqRSxPQUFPSixrQkFBa0JHO1FBQzNCO0lBQ0Y7QUFDRjtBQUdFLENBQ0Ysa0NBQWtDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcXVlcnljcmFmdC1zdHVkaW8vLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3ByaW1pdGl2ZS9kaXN0L2luZGV4Lm1qcz8xODY4Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL2NvcmUvcHJpbWl0aXZlL3NyYy9wcmltaXRpdmUudHN4XG5mdW5jdGlvbiBjb21wb3NlRXZlbnRIYW5kbGVycyhvcmlnaW5hbEV2ZW50SGFuZGxlciwgb3VyRXZlbnRIYW5kbGVyLCB7IGNoZWNrRm9yRGVmYXVsdFByZXZlbnRlZCA9IHRydWUgfSA9IHt9KSB7XG4gIHJldHVybiBmdW5jdGlvbiBoYW5kbGVFdmVudChldmVudCkge1xuICAgIG9yaWdpbmFsRXZlbnRIYW5kbGVyPy4oZXZlbnQpO1xuICAgIGlmIChjaGVja0ZvckRlZmF1bHRQcmV2ZW50ZWQgPT09IGZhbHNlIHx8ICFldmVudC5kZWZhdWx0UHJldmVudGVkKSB7XG4gICAgICByZXR1cm4gb3VyRXZlbnRIYW5kbGVyPy4oZXZlbnQpO1xuICAgIH1cbiAgfTtcbn1cbmV4cG9ydCB7XG4gIGNvbXBvc2VFdmVudEhhbmRsZXJzXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbImNvbXBvc2VFdmVudEhhbmRsZXJzIiwib3JpZ2luYWxFdmVudEhhbmRsZXIiLCJvdXJFdmVudEhhbmRsZXIiLCJjaGVja0ZvckRlZmF1bHRQcmV2ZW50ZWQiLCJoYW5kbGVFdmVudCIsImV2ZW50IiwiZGVmYXVsdFByZXZlbnRlZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-collection/dist/index.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/@radix-ui/react-collection/dist/index.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCollection: () => (/* binding */ createCollection),\n/* harmony export */   unstable_createCollection: () => (/* binding */ createCollection2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ createCollection,unstable_createCollection auto */ // src/collection-legacy.tsx\n\n\n\n\n\nfunction createCollection(name) {\n    const PROVIDER_NAME = name + \"CollectionProvider\";\n    const [createCollectionContext, createCollectionScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(PROVIDER_NAME);\n    const [CollectionProviderImpl, useCollectionContext] = createCollectionContext(PROVIDER_NAME, {\n        collectionRef: {\n            current: null\n        },\n        itemMap: /* @__PURE__ */ new Map()\n    });\n    const CollectionProvider = (props)=>{\n        const { scope, children } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const itemMap = react__WEBPACK_IMPORTED_MODULE_0__.useRef(/* @__PURE__ */ new Map()).current;\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, {\n            scope,\n            itemMap,\n            collectionRef: ref,\n            children\n        });\n    };\n    CollectionProvider.displayName = PROVIDER_NAME;\n    const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n    const CollectionSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(COLLECTION_SLOT_NAME);\n    const CollectionSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children } = props;\n        const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, context.collectionRef);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionSlotImpl, {\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n    const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n    const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n    const CollectionItemSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(ITEM_SLOT_NAME);\n    const CollectionItemSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children, ...itemData } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, ref);\n        const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            context.itemMap.set(ref, {\n                ref,\n                ...itemData\n            });\n            return ()=>void context.itemMap.delete(ref);\n        });\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionItemSlotImpl, {\n            ...{\n                [ITEM_DATA_ATTR]: \"\"\n            },\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n    function useCollection(scope) {\n        const context = useCollectionContext(name + \"CollectionConsumer\", scope);\n        const getItems = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n            const collectionNode = context.collectionRef.current;\n            if (!collectionNode) return [];\n            const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n            const items = Array.from(context.itemMap.values());\n            const orderedItems = items.sort((a, b)=>orderedNodes.indexOf(a.ref.current) - orderedNodes.indexOf(b.ref.current));\n            return orderedItems;\n        }, [\n            context.collectionRef,\n            context.itemMap\n        ]);\n        return getItems;\n    }\n    return [\n        {\n            Provider: CollectionProvider,\n            Slot: CollectionSlot,\n            ItemSlot: CollectionItemSlot\n        },\n        useCollection,\n        createCollectionScope\n    ];\n}\n// src/collection.tsx\n\n\n\n\n// src/ordered-dictionary.ts\nvar __instanciated = /* @__PURE__ */ new WeakMap();\nvar OrderedDict = class _OrderedDict extends Map {\n    #keys;\n    constructor(entries){\n        super(entries);\n        this.#keys = [\n            ...super.keys()\n        ];\n        __instanciated.set(this, true);\n    }\n    set(key, value) {\n        if (__instanciated.get(this)) {\n            if (this.has(key)) {\n                this.#keys[this.#keys.indexOf(key)] = key;\n            } else {\n                this.#keys.push(key);\n            }\n        }\n        super.set(key, value);\n        return this;\n    }\n    insert(index, key, value) {\n        const has = this.has(key);\n        const length = this.#keys.length;\n        const relativeIndex = toSafeInteger(index);\n        let actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n        const safeIndex = actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n        if (safeIndex === this.size || has && safeIndex === this.size - 1 || safeIndex === -1) {\n            this.set(key, value);\n            return this;\n        }\n        const size = this.size + (has ? 0 : 1);\n        if (relativeIndex < 0) {\n            actualIndex++;\n        }\n        const keys = [\n            ...this.#keys\n        ];\n        let nextValue;\n        let shouldSkip = false;\n        for(let i = actualIndex; i < size; i++){\n            if (actualIndex === i) {\n                let nextKey = keys[i];\n                if (keys[i] === key) {\n                    nextKey = keys[i + 1];\n                }\n                if (has) {\n                    this.delete(key);\n                }\n                nextValue = this.get(nextKey);\n                this.set(key, value);\n            } else {\n                if (!shouldSkip && keys[i - 1] === key) {\n                    shouldSkip = true;\n                }\n                const currentKey = keys[shouldSkip ? i : i - 1];\n                const currentValue = nextValue;\n                nextValue = this.get(currentKey);\n                this.delete(currentKey);\n                this.set(currentKey, currentValue);\n            }\n        }\n        return this;\n    }\n    with(index, key, value) {\n        const copy = new _OrderedDict(this);\n        copy.insert(index, key, value);\n        return copy;\n    }\n    before(key) {\n        const index = this.#keys.indexOf(key) - 1;\n        if (index < 0) {\n            return void 0;\n        }\n        return this.entryAt(index);\n    }\n    /**\n   * Sets a new key-value pair at the position before the given key.\n   */ setBefore(key, newKey, value) {\n        const index = this.#keys.indexOf(key);\n        if (index === -1) {\n            return this;\n        }\n        return this.insert(index, newKey, value);\n    }\n    after(key) {\n        let index = this.#keys.indexOf(key);\n        index = index === -1 || index === this.size - 1 ? -1 : index + 1;\n        if (index === -1) {\n            return void 0;\n        }\n        return this.entryAt(index);\n    }\n    /**\n   * Sets a new key-value pair at the position after the given key.\n   */ setAfter(key, newKey, value) {\n        const index = this.#keys.indexOf(key);\n        if (index === -1) {\n            return this;\n        }\n        return this.insert(index + 1, newKey, value);\n    }\n    first() {\n        return this.entryAt(0);\n    }\n    last() {\n        return this.entryAt(-1);\n    }\n    clear() {\n        this.#keys = [];\n        return super.clear();\n    }\n    delete(key) {\n        const deleted = super.delete(key);\n        if (deleted) {\n            this.#keys.splice(this.#keys.indexOf(key), 1);\n        }\n        return deleted;\n    }\n    deleteAt(index) {\n        const key = this.keyAt(index);\n        if (key !== void 0) {\n            return this.delete(key);\n        }\n        return false;\n    }\n    at(index) {\n        const key = at(this.#keys, index);\n        if (key !== void 0) {\n            return this.get(key);\n        }\n    }\n    entryAt(index) {\n        const key = at(this.#keys, index);\n        if (key !== void 0) {\n            return [\n                key,\n                this.get(key)\n            ];\n        }\n    }\n    indexOf(key) {\n        return this.#keys.indexOf(key);\n    }\n    keyAt(index) {\n        return at(this.#keys, index);\n    }\n    from(key, offset) {\n        const index = this.indexOf(key);\n        if (index === -1) {\n            return void 0;\n        }\n        let dest = index + offset;\n        if (dest < 0) dest = 0;\n        if (dest >= this.size) dest = this.size - 1;\n        return this.at(dest);\n    }\n    keyFrom(key, offset) {\n        const index = this.indexOf(key);\n        if (index === -1) {\n            return void 0;\n        }\n        let dest = index + offset;\n        if (dest < 0) dest = 0;\n        if (dest >= this.size) dest = this.size - 1;\n        return this.keyAt(dest);\n    }\n    find(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return entry;\n            }\n            index++;\n        }\n        return void 0;\n    }\n    findIndex(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return index;\n            }\n            index++;\n        }\n        return -1;\n    }\n    filter(predicate, thisArg) {\n        const entries = [];\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                entries.push(entry);\n            }\n            index++;\n        }\n        return new _OrderedDict(entries);\n    }\n    map(callbackfn, thisArg) {\n        const entries = [];\n        let index = 0;\n        for (const entry of this){\n            entries.push([\n                entry[0],\n                Reflect.apply(callbackfn, thisArg, [\n                    entry,\n                    index,\n                    this\n                ])\n            ]);\n            index++;\n        }\n        return new _OrderedDict(entries);\n    }\n    reduce(...args) {\n        const [callbackfn, initialValue] = args;\n        let index = 0;\n        let accumulator = initialValue ?? this.at(0);\n        for (const entry of this){\n            if (index === 0 && args.length === 1) {\n                accumulator = entry;\n            } else {\n                accumulator = Reflect.apply(callbackfn, this, [\n                    accumulator,\n                    entry,\n                    index,\n                    this\n                ]);\n            }\n            index++;\n        }\n        return accumulator;\n    }\n    reduceRight(...args) {\n        const [callbackfn, initialValue] = args;\n        let accumulator = initialValue ?? this.at(-1);\n        for(let index = this.size - 1; index >= 0; index--){\n            const entry = this.at(index);\n            if (index === this.size - 1 && args.length === 1) {\n                accumulator = entry;\n            } else {\n                accumulator = Reflect.apply(callbackfn, this, [\n                    accumulator,\n                    entry,\n                    index,\n                    this\n                ]);\n            }\n        }\n        return accumulator;\n    }\n    toSorted(compareFn) {\n        const entries = [\n            ...this.entries()\n        ].sort(compareFn);\n        return new _OrderedDict(entries);\n    }\n    toReversed() {\n        const reversed = new _OrderedDict();\n        for(let index = this.size - 1; index >= 0; index--){\n            const key = this.keyAt(index);\n            const element = this.get(key);\n            reversed.set(key, element);\n        }\n        return reversed;\n    }\n    toSpliced(...args) {\n        const entries = [\n            ...this.entries()\n        ];\n        entries.splice(...args);\n        return new _OrderedDict(entries);\n    }\n    slice(start, end) {\n        const result = new _OrderedDict();\n        let stop = this.size - 1;\n        if (start === void 0) {\n            return result;\n        }\n        if (start < 0) {\n            start = start + this.size;\n        }\n        if (end !== void 0 && end > 0) {\n            stop = end - 1;\n        }\n        for(let index = start; index <= stop; index++){\n            const key = this.keyAt(index);\n            const element = this.get(key);\n            result.set(key, element);\n        }\n        return result;\n    }\n    every(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (!Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return false;\n            }\n            index++;\n        }\n        return true;\n    }\n    some(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return true;\n            }\n            index++;\n        }\n        return false;\n    }\n};\nfunction at(array, index) {\n    if (\"at\" in Array.prototype) {\n        return Array.prototype.at.call(array, index);\n    }\n    const actualIndex = toSafeIndex(array, index);\n    return actualIndex === -1 ? void 0 : array[actualIndex];\n}\nfunction toSafeIndex(array, index) {\n    const length = array.length;\n    const relativeIndex = toSafeInteger(index);\n    const actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n    return actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n}\nfunction toSafeInteger(number) {\n    return number !== number || number === 0 ? 0 : Math.trunc(number);\n}\n// src/collection.tsx\n\nfunction createCollection2(name) {\n    const PROVIDER_NAME = name + \"CollectionProvider\";\n    const [createCollectionContext, createCollectionScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(PROVIDER_NAME);\n    const [CollectionContextProvider, useCollectionContext] = createCollectionContext(PROVIDER_NAME, {\n        collectionElement: null,\n        collectionRef: {\n            current: null\n        },\n        collectionRefObject: {\n            current: null\n        },\n        itemMap: new OrderedDict(),\n        setItemMap: ()=>void 0\n    });\n    const CollectionProvider = ({ state, ...props })=>{\n        return state ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, {\n            ...props,\n            state\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionInit, {\n            ...props\n        });\n    };\n    CollectionProvider.displayName = PROVIDER_NAME;\n    const CollectionInit = (props)=>{\n        const state = useInitCollection();\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, {\n            ...props,\n            state\n        });\n    };\n    CollectionInit.displayName = PROVIDER_NAME + \"Init\";\n    const CollectionProviderImpl = (props)=>{\n        const { scope, children, state } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const [collectionElement, setCollectionElement] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n        const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(ref, setCollectionElement);\n        const [itemMap, setItemMap] = state;\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            if (!collectionElement) return;\n            const observer = getChildListObserver(()=>{});\n            observer.observe(collectionElement, {\n                childList: true,\n                subtree: true\n            });\n            return ()=>{\n                observer.disconnect();\n            };\n        }, [\n            collectionElement\n        ]);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionContextProvider, {\n            scope,\n            itemMap,\n            setItemMap,\n            collectionRef: composeRefs,\n            collectionRefObject: ref,\n            collectionElement,\n            children\n        });\n    };\n    CollectionProviderImpl.displayName = PROVIDER_NAME + \"Impl\";\n    const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n    const CollectionSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(COLLECTION_SLOT_NAME);\n    const CollectionSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children } = props;\n        const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, context.collectionRef);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionSlotImpl, {\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n    const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n    const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n    const CollectionItemSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(ITEM_SLOT_NAME);\n    const CollectionItemSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children, ...itemData } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const [element, setElement] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, ref, setElement);\n        const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n        const { setItemMap } = context;\n        const itemDataRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(itemData);\n        if (!shallowEqual(itemDataRef.current, itemData)) {\n            itemDataRef.current = itemData;\n        }\n        const memoizedItemData = itemDataRef.current;\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            const itemData2 = memoizedItemData;\n            setItemMap((map)=>{\n                if (!element) {\n                    return map;\n                }\n                if (!map.has(element)) {\n                    map.set(element, {\n                        ...itemData2,\n                        element\n                    });\n                    return map.toSorted(sortByDocumentPosition);\n                }\n                return map.set(element, {\n                    ...itemData2,\n                    element\n                }).toSorted(sortByDocumentPosition);\n            });\n            return ()=>{\n                setItemMap((map)=>{\n                    if (!element || !map.has(element)) {\n                        return map;\n                    }\n                    map.delete(element);\n                    return new OrderedDict(map);\n                });\n            };\n        }, [\n            element,\n            memoizedItemData,\n            setItemMap\n        ]);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionItemSlotImpl, {\n            ...{\n                [ITEM_DATA_ATTR]: \"\"\n            },\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n    function useInitCollection() {\n        return react__WEBPACK_IMPORTED_MODULE_0__.useState(new OrderedDict());\n    }\n    function useCollection(scope) {\n        const { itemMap } = useCollectionContext(name + \"CollectionConsumer\", scope);\n        return itemMap;\n    }\n    const functions = {\n        createCollectionScope,\n        useCollection,\n        useInitCollection\n    };\n    return [\n        {\n            Provider: CollectionProvider,\n            Slot: CollectionSlot,\n            ItemSlot: CollectionItemSlot\n        },\n        functions\n    ];\n}\nfunction shallowEqual(a, b) {\n    if (a === b) return true;\n    if (typeof a !== \"object\" || typeof b !== \"object\") return false;\n    if (a == null || b == null) return false;\n    const keysA = Object.keys(a);\n    const keysB = Object.keys(b);\n    if (keysA.length !== keysB.length) return false;\n    for (const key of keysA){\n        if (!Object.prototype.hasOwnProperty.call(b, key)) return false;\n        if (a[key] !== b[key]) return false;\n    }\n    return true;\n}\nfunction isElementPreceding(a, b) {\n    return !!(b.compareDocumentPosition(a) & Node.DOCUMENT_POSITION_PRECEDING);\n}\nfunction sortByDocumentPosition(a, b) {\n    return !a[1].element || !b[1].element ? 0 : isElementPreceding(a[1].element, b[1].element) ? -1 : 1;\n}\nfunction getChildListObserver(callback) {\n    const observer = new MutationObserver((mutationsList)=>{\n        for (const mutation of mutationsList){\n            if (mutation.type === \"childList\") {\n                callback();\n                return;\n            }\n        }\n    });\n    return observer;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-collection/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-compose-refs/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeRefs: () => (/* binding */ composeRefs),\n/* harmony export */   useComposedRefs: () => (/* binding */ useComposedRefs)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/compose-refs/src/compose-refs.tsx\n\nfunction setRef(ref, value) {\n    if (typeof ref === \"function\") {\n        return ref(value);\n    } else if (ref !== null && ref !== void 0) {\n        ref.current = value;\n    }\n}\nfunction composeRefs(...refs) {\n    return (node)=>{\n        let hasCleanup = false;\n        const cleanups = refs.map((ref)=>{\n            const cleanup = setRef(ref, node);\n            if (!hasCleanup && typeof cleanup == \"function\") {\n                hasCleanup = true;\n            }\n            return cleanup;\n        });\n        if (hasCleanup) {\n            return ()=>{\n                for(let i = 0; i < cleanups.length; i++){\n                    const cleanup = cleanups[i];\n                    if (typeof cleanup == \"function\") {\n                        cleanup();\n                    } else {\n                        setRef(refs[i], null);\n                    }\n                }\n            };\n        }\n    };\n}\nfunction useComposedRefs(...refs) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(composeRefs(...refs), refs);\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@radix-ui/react-context/dist/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createContext: () => (/* binding */ createContext2),\n/* harmony export */   createContextScope: () => (/* binding */ createContextScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/context/src/create-context.tsx\n\n\nfunction createContext2(rootComponentName, defaultContext) {\n    const Context = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    const Provider = (props)=>{\n        const { children, ...context } = props;\n        const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>context, Object.values(context));\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, {\n            value,\n            children\n        });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName) {\n        const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n        if (context) return context;\n        if (defaultContext !== void 0) return defaultContext;\n        throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [\n        Provider,\n        useContext2\n    ];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n    let defaultContexts = [];\n    function createContext3(rootComponentName, defaultContext) {\n        const BaseContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n        const index = defaultContexts.length;\n        defaultContexts = [\n            ...defaultContexts,\n            defaultContext\n        ];\n        const Provider = (props)=>{\n            const { scope, children, ...context } = props;\n            const Context = scope?.[scopeName]?.[index] || BaseContext;\n            const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>context, Object.values(context));\n            return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, {\n                value,\n                children\n            });\n        };\n        Provider.displayName = rootComponentName + \"Provider\";\n        function useContext2(consumerName, scope) {\n            const Context = scope?.[scopeName]?.[index] || BaseContext;\n            const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n            if (context) return context;\n            if (defaultContext !== void 0) return defaultContext;\n            throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n        }\n        return [\n            Provider,\n            useContext2\n        ];\n    }\n    const createScope = ()=>{\n        const scopeContexts = defaultContexts.map((defaultContext)=>{\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n        });\n        return function useScope(scope) {\n            const contexts = scope?.[scopeName] || scopeContexts;\n            return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n                    [`__scope${scopeName}`]: {\n                        ...scope,\n                        [scopeName]: contexts\n                    }\n                }), [\n                scope,\n                contexts\n            ]);\n        };\n    };\n    createScope.scopeName = scopeName;\n    return [\n        createContext3,\n        composeContextScopes(createScope, ...createContextScopeDeps)\n    ];\n}\nfunction composeContextScopes(...scopes) {\n    const baseScope = scopes[0];\n    if (scopes.length === 1) return baseScope;\n    const createScope = ()=>{\n        const scopeHooks = scopes.map((createScope2)=>({\n                useScope: createScope2(),\n                scopeName: createScope2.scopeName\n            }));\n        return function useComposedScopes(overrideScopes) {\n            const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName })=>{\n                const scopeProps = useScope(overrideScopes);\n                const currentScope = scopeProps[`__scope${scopeName}`];\n                return {\n                    ...nextScopes2,\n                    ...currentScope\n                };\n            }, {});\n            return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n                    [`__scope${baseScope.scopeName}`]: nextScopes\n                }), [\n                nextScopes\n            ]);\n        };\n    };\n    createScope.scopeName = baseScope.scopeName;\n    return createScope;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-direction/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-direction/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DirectionProvider: () => (/* binding */ DirectionProvider),\n/* harmony export */   Provider: () => (/* binding */ Provider),\n/* harmony export */   useDirection: () => (/* binding */ useDirection)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/direction/src/direction.tsx\n\n\nvar DirectionContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(void 0);\nvar DirectionProvider = (props)=>{\n    const { dir, children } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DirectionContext.Provider, {\n        value: dir,\n        children\n    });\n};\nfunction useDirection(localDir) {\n    const globalDir = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DirectionContext);\n    return localDir || globalDir || \"ltr\";\n}\nvar Provider = DirectionProvider;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWRpcmVjdGlvbi9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBLDZDQUE2QztBQUNkO0FBQ1M7QUFDeEMsSUFBSUUsaUNBQW1CRixnREFBbUIsQ0FBQyxLQUFLO0FBQ2hELElBQUlJLG9CQUFvQixDQUFDQztJQUN2QixNQUFNLEVBQUVDLEdBQUcsRUFBRUMsUUFBUSxFQUFFLEdBQUdGO0lBQzFCLE9BQU8sYUFBYSxHQUFHSixzREFBR0EsQ0FBQ0MsaUJBQWlCTSxRQUFRLEVBQUU7UUFBRUMsT0FBT0g7UUFBS0M7SUFBUztBQUMvRTtBQUNBLFNBQVNHLGFBQWFDLFFBQVE7SUFDNUIsTUFBTUMsWUFBWVosNkNBQWdCLENBQUNFO0lBQ25DLE9BQU9TLFlBQVlDLGFBQWE7QUFDbEM7QUFDQSxJQUFJSixXQUFXSjtBQUtiLENBQ0Ysa0NBQWtDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcXVlcnljcmFmdC1zdHVkaW8vLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWRpcmVjdGlvbi9kaXN0L2luZGV4Lm1qcz83NTgzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL3JlYWN0L2RpcmVjdGlvbi9zcmMvZGlyZWN0aW9uLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyBqc3ggfSBmcm9tIFwicmVhY3QvanN4LXJ1bnRpbWVcIjtcbnZhciBEaXJlY3Rpb25Db250ZXh0ID0gUmVhY3QuY3JlYXRlQ29udGV4dCh2b2lkIDApO1xudmFyIERpcmVjdGlvblByb3ZpZGVyID0gKHByb3BzKSA9PiB7XG4gIGNvbnN0IHsgZGlyLCBjaGlsZHJlbiB9ID0gcHJvcHM7XG4gIHJldHVybiAvKiBAX19QVVJFX18gKi8ganN4KERpcmVjdGlvbkNvbnRleHQuUHJvdmlkZXIsIHsgdmFsdWU6IGRpciwgY2hpbGRyZW4gfSk7XG59O1xuZnVuY3Rpb24gdXNlRGlyZWN0aW9uKGxvY2FsRGlyKSB7XG4gIGNvbnN0IGdsb2JhbERpciA9IFJlYWN0LnVzZUNvbnRleHQoRGlyZWN0aW9uQ29udGV4dCk7XG4gIHJldHVybiBsb2NhbERpciB8fCBnbG9iYWxEaXIgfHwgXCJsdHJcIjtcbn1cbnZhciBQcm92aWRlciA9IERpcmVjdGlvblByb3ZpZGVyO1xuZXhwb3J0IHtcbiAgRGlyZWN0aW9uUHJvdmlkZXIsXG4gIFByb3ZpZGVyLFxuICB1c2VEaXJlY3Rpb25cbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJqc3giLCJEaXJlY3Rpb25Db250ZXh0IiwiY3JlYXRlQ29udGV4dCIsIkRpcmVjdGlvblByb3ZpZGVyIiwicHJvcHMiLCJkaXIiLCJjaGlsZHJlbiIsIlByb3ZpZGVyIiwidmFsdWUiLCJ1c2VEaXJlY3Rpb24iLCJsb2NhbERpciIsImdsb2JhbERpciIsInVzZUNvbnRleHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-direction/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Branch: () => (/* binding */ Branch),\n/* harmony export */   DismissableLayer: () => (/* binding */ DismissableLayer),\n/* harmony export */   DismissableLayerBranch: () => (/* binding */ DismissableLayerBranch),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-escape-keydown */ \"(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Branch,DismissableLayer,DismissableLayerBranch,Root auto */ // src/dismissable-layer.tsx\n\n\n\n\n\n\n\nvar DISMISSABLE_LAYER_NAME = \"DismissableLayer\";\nvar CONTEXT_UPDATE = \"dismissableLayer.update\";\nvar POINTER_DOWN_OUTSIDE = \"dismissableLayer.pointerDownOutside\";\nvar FOCUS_OUTSIDE = \"dismissableLayer.focusOutside\";\nvar originalBodyPointerEvents;\nvar DismissableLayerContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext({\n    layers: /* @__PURE__ */ new Set(),\n    layersWithOutsidePointerEventsDisabled: /* @__PURE__ */ new Set(),\n    branches: /* @__PURE__ */ new Set()\n});\nvar DismissableLayer = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { disableOutsidePointerEvents = false, onEscapeKeyDown, onPointerDownOutside, onFocusOutside, onInteractOutside, onDismiss, ...layerProps } = props;\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const ownerDocument = node?.ownerDocument ?? globalThis?.document;\n    const [, force] = react__WEBPACK_IMPORTED_MODULE_0__.useState({});\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, (node2)=>setNode(node2));\n    const layers = Array.from(context.layers);\n    const [highestLayerWithOutsidePointerEventsDisabled] = [\n        ...context.layersWithOutsidePointerEventsDisabled\n    ].slice(-1);\n    const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled);\n    const index = node ? layers.indexOf(node) : -1;\n    const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n    const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n    const pointerDownOutside = usePointerDownOutside((event)=>{\n        const target = event.target;\n        const isPointerDownOnBranch = [\n            ...context.branches\n        ].some((branch)=>branch.contains(target));\n        if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n        onPointerDownOutside?.(event);\n        onInteractOutside?.(event);\n        if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    const focusOutside = useFocusOutside((event)=>{\n        const target = event.target;\n        const isFocusInBranch = [\n            ...context.branches\n        ].some((branch)=>branch.contains(target));\n        if (isFocusInBranch) return;\n        onFocusOutside?.(event);\n        onInteractOutside?.(event);\n        if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    (0,_radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__.useEscapeKeydown)((event)=>{\n        const isHighestLayer = index === context.layers.size - 1;\n        if (!isHighestLayer) return;\n        onEscapeKeyDown?.(event);\n        if (!event.defaultPrevented && onDismiss) {\n            event.preventDefault();\n            onDismiss();\n        }\n    }, ownerDocument);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (!node) return;\n        if (disableOutsidePointerEvents) {\n            if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n                originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;\n                ownerDocument.body.style.pointerEvents = \"none\";\n            }\n            context.layersWithOutsidePointerEventsDisabled.add(node);\n        }\n        context.layers.add(node);\n        dispatchUpdate();\n        return ()=>{\n            if (disableOutsidePointerEvents && context.layersWithOutsidePointerEventsDisabled.size === 1) {\n                ownerDocument.body.style.pointerEvents = originalBodyPointerEvents;\n            }\n        };\n    }, [\n        node,\n        ownerDocument,\n        disableOutsidePointerEvents,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>{\n            if (!node) return;\n            context.layers.delete(node);\n            context.layersWithOutsidePointerEventsDisabled.delete(node);\n            dispatchUpdate();\n        };\n    }, [\n        node,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleUpdate = ()=>force({});\n        document.addEventListener(CONTEXT_UPDATE, handleUpdate);\n        return ()=>document.removeEventListener(CONTEXT_UPDATE, handleUpdate);\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...layerProps,\n        ref: composedRefs,\n        style: {\n            pointerEvents: isBodyPointerEventsDisabled ? isPointerEventsEnabled ? \"auto\" : \"none\" : void 0,\n            ...props.style\n        },\n        onFocusCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onFocusCapture, focusOutside.onFocusCapture),\n        onBlurCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onBlurCapture, focusOutside.onBlurCapture),\n        onPointerDownCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onPointerDownCapture, pointerDownOutside.onPointerDownCapture)\n    });\n});\nDismissableLayer.displayName = DISMISSABLE_LAYER_NAME;\nvar BRANCH_NAME = \"DismissableLayerBranch\";\nvar DismissableLayerBranch = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const node = ref.current;\n        if (node) {\n            context.branches.add(node);\n            return ()=>{\n                context.branches.delete(node);\n            };\n        }\n    }, [\n        context.branches\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...props,\n        ref: composedRefs\n    });\n});\nDismissableLayerBranch.displayName = BRANCH_NAME;\nfunction usePointerDownOutside(onPointerDownOutside, ownerDocument = globalThis?.document) {\n    const handlePointerDownOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onPointerDownOutside);\n    const isPointerInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handleClickRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(()=>{});\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handlePointerDown = (event)=>{\n            if (event.target && !isPointerInsideReactTreeRef.current) {\n                let handleAndDispatchPointerDownOutsideEvent2 = function() {\n                    handleAndDispatchCustomEvent(POINTER_DOWN_OUTSIDE, handlePointerDownOutside, eventDetail, {\n                        discrete: true\n                    });\n                };\n                var handleAndDispatchPointerDownOutsideEvent = handleAndDispatchPointerDownOutsideEvent2;\n                const eventDetail = {\n                    originalEvent: event\n                };\n                if (event.pointerType === \"touch\") {\n                    ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n                    handleClickRef.current = handleAndDispatchPointerDownOutsideEvent2;\n                    ownerDocument.addEventListener(\"click\", handleClickRef.current, {\n                        once: true\n                    });\n                } else {\n                    handleAndDispatchPointerDownOutsideEvent2();\n                }\n            } else {\n                ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n            }\n            isPointerInsideReactTreeRef.current = false;\n        };\n        const timerId = window.setTimeout(()=>{\n            ownerDocument.addEventListener(\"pointerdown\", handlePointerDown);\n        }, 0);\n        return ()=>{\n            window.clearTimeout(timerId);\n            ownerDocument.removeEventListener(\"pointerdown\", handlePointerDown);\n            ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n        };\n    }, [\n        ownerDocument,\n        handlePointerDownOutside\n    ]);\n    return {\n        // ensures we check React component tree (not just DOM tree)\n        onPointerDownCapture: ()=>isPointerInsideReactTreeRef.current = true\n    };\n}\nfunction useFocusOutside(onFocusOutside, ownerDocument = globalThis?.document) {\n    const handleFocusOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onFocusOutside);\n    const isFocusInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleFocus = (event)=>{\n            if (event.target && !isFocusInsideReactTreeRef.current) {\n                const eventDetail = {\n                    originalEvent: event\n                };\n                handleAndDispatchCustomEvent(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n                    discrete: false\n                });\n            }\n        };\n        ownerDocument.addEventListener(\"focusin\", handleFocus);\n        return ()=>ownerDocument.removeEventListener(\"focusin\", handleFocus);\n    }, [\n        ownerDocument,\n        handleFocusOutside\n    ]);\n    return {\n        onFocusCapture: ()=>isFocusInsideReactTreeRef.current = true,\n        onBlurCapture: ()=>isFocusInsideReactTreeRef.current = false\n    };\n}\nfunction dispatchUpdate() {\n    const event = new CustomEvent(CONTEXT_UPDATE);\n    document.dispatchEvent(event);\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n    const target = detail.originalEvent.target;\n    const event = new CustomEvent(name, {\n        bubbles: false,\n        cancelable: true,\n        detail\n    });\n    if (handler) target.addEventListener(name, handler, {\n        once: true\n    });\n    if (discrete) {\n        (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.dispatchDiscreteCustomEvent)(target, event);\n    } else {\n        target.dispatchEvent(event);\n    }\n}\nvar Root = DismissableLayer;\nvar Branch = DismissableLayerBranch;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@radix-ui/react-portal/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Portal,Root auto */ // src/portal.tsx\n\n\n\n\n\nvar PORTAL_NAME = \"Portal\";\nvar Portal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { container: containerProp, ...portalProps } = props;\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__.useLayoutEffect)(()=>setMounted(true), []);\n    const container = containerProp || mounted && globalThis?.document?.body;\n    return container ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...portalProps,\n        ref: forwardedRef\n    }), container) : null;\n});\nPortal.displayName = PORTAL_NAME;\nvar Root = Portal;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@radix-ui/react-presence/dist/index.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Presence: () => (/* binding */ Presence),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Presence,Root auto */ // src/presence.tsx\n\n\n\n// src/use-state-machine.tsx\n\nfunction useStateMachine(initialState, machine) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useReducer((state, event)=>{\n        const nextState = machine[state][event];\n        return nextState ?? state;\n    }, initialState);\n}\n// src/presence.tsx\nvar Presence = (props)=>{\n    const { present, children } = props;\n    const presence = usePresence(present);\n    const child = typeof children === \"function\" ? children({\n        present: presence.isPresent\n    }) : react__WEBPACK_IMPORTED_MODULE_0__.Children.only(children);\n    const ref = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__.useComposedRefs)(presence.ref, getElementRef(child));\n    const forceMount = typeof children === \"function\";\n    return forceMount || presence.isPresent ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(child, {\n        ref\n    }) : null;\n};\nPresence.displayName = \"Presence\";\nfunction usePresence(present) {\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const stylesRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const prevPresentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(present);\n    const prevAnimationNameRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"none\");\n    const initialState = present ? \"mounted\" : \"unmounted\";\n    const [state, send] = useStateMachine(initialState, {\n        mounted: {\n            UNMOUNT: \"unmounted\",\n            ANIMATION_OUT: \"unmountSuspended\"\n        },\n        unmountSuspended: {\n            MOUNT: \"mounted\",\n            ANIMATION_END: \"unmounted\"\n        },\n        unmounted: {\n            MOUNT: \"mounted\"\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const currentAnimationName = getAnimationName(stylesRef.current);\n        prevAnimationNameRef.current = state === \"mounted\" ? currentAnimationName : \"none\";\n    }, [\n        state\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)(()=>{\n        const styles = stylesRef.current;\n        const wasPresent = prevPresentRef.current;\n        const hasPresentChanged = wasPresent !== present;\n        if (hasPresentChanged) {\n            const prevAnimationName = prevAnimationNameRef.current;\n            const currentAnimationName = getAnimationName(styles);\n            if (present) {\n                send(\"MOUNT\");\n            } else if (currentAnimationName === \"none\" || styles?.display === \"none\") {\n                send(\"UNMOUNT\");\n            } else {\n                const isAnimating = prevAnimationName !== currentAnimationName;\n                if (wasPresent && isAnimating) {\n                    send(\"ANIMATION_OUT\");\n                } else {\n                    send(\"UNMOUNT\");\n                }\n            }\n            prevPresentRef.current = present;\n        }\n    }, [\n        present,\n        send\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)(()=>{\n        if (node) {\n            let timeoutId;\n            const ownerWindow = node.ownerDocument.defaultView ?? window;\n            const handleAnimationEnd = (event)=>{\n                const currentAnimationName = getAnimationName(stylesRef.current);\n                const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n                if (event.target === node && isCurrentAnimation) {\n                    send(\"ANIMATION_END\");\n                    if (!prevPresentRef.current) {\n                        const currentFillMode = node.style.animationFillMode;\n                        node.style.animationFillMode = \"forwards\";\n                        timeoutId = ownerWindow.setTimeout(()=>{\n                            if (node.style.animationFillMode === \"forwards\") {\n                                node.style.animationFillMode = currentFillMode;\n                            }\n                        });\n                    }\n                }\n            };\n            const handleAnimationStart = (event)=>{\n                if (event.target === node) {\n                    prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n                }\n            };\n            node.addEventListener(\"animationstart\", handleAnimationStart);\n            node.addEventListener(\"animationcancel\", handleAnimationEnd);\n            node.addEventListener(\"animationend\", handleAnimationEnd);\n            return ()=>{\n                ownerWindow.clearTimeout(timeoutId);\n                node.removeEventListener(\"animationstart\", handleAnimationStart);\n                node.removeEventListener(\"animationcancel\", handleAnimationEnd);\n                node.removeEventListener(\"animationend\", handleAnimationEnd);\n            };\n        } else {\n            send(\"ANIMATION_END\");\n        }\n    }, [\n        node,\n        send\n    ]);\n    return {\n        isPresent: [\n            \"mounted\",\n            \"unmountSuspended\"\n        ].includes(state),\n        ref: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node2)=>{\n            stylesRef.current = node2 ? getComputedStyle(node2) : null;\n            setNode(node2);\n        }, [])\n    };\n}\nfunction getAnimationName(styles) {\n    return styles?.animationName || \"none\";\n}\nfunction getElementRef(element) {\n    let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n    let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.ref;\n    }\n    getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n    mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.props.ref;\n    }\n    return element.props.ref || element.ref;\n}\nvar Root = Presence;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-primitive/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Primitive: () => (/* binding */ Primitive),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   dispatchDiscreteCustomEvent: () => (/* binding */ dispatchDiscreteCustomEvent)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/primitive.tsx\n\n\n\n\nvar NODES = [\n    \"a\",\n    \"button\",\n    \"div\",\n    \"form\",\n    \"h2\",\n    \"h3\",\n    \"img\",\n    \"input\",\n    \"label\",\n    \"li\",\n    \"nav\",\n    \"ol\",\n    \"p\",\n    \"select\",\n    \"span\",\n    \"svg\",\n    \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node)=>{\n    const Slot = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(`Primitive.${node}`);\n    const Node = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { asChild, ...primitiveProps } = props;\n        const Comp = asChild ? Slot : node;\n        if (false) {}\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Comp, {\n            ...primitiveProps,\n            ref: forwardedRef\n        });\n    });\n    Node.displayName = `Primitive.${node}`;\n    return {\n        ...primitive,\n        [node]: Node\n    };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n    if (target) react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(()=>target.dispatchEvent(event));\n}\nvar Root = Primitive;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-scroll-area/dist/index.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/@radix-ui/react-scroll-area/dist/index.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Corner: () => (/* binding */ Corner),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   ScrollArea: () => (/* binding */ ScrollArea),\n/* harmony export */   ScrollAreaCorner: () => (/* binding */ ScrollAreaCorner),\n/* harmony export */   ScrollAreaScrollbar: () => (/* binding */ ScrollAreaScrollbar),\n/* harmony export */   ScrollAreaThumb: () => (/* binding */ ScrollAreaThumb),\n/* harmony export */   ScrollAreaViewport: () => (/* binding */ ScrollAreaViewport),\n/* harmony export */   Scrollbar: () => (/* binding */ Scrollbar),\n/* harmony export */   Thumb: () => (/* binding */ Thumb),\n/* harmony export */   Viewport: () => (/* binding */ Viewport),\n/* harmony export */   createScrollAreaScope: () => (/* binding */ createScrollAreaScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_number__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/number */ \"(ssr)/./node_modules/@radix-ui/number/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Corner,Root,ScrollArea,ScrollAreaCorner,ScrollAreaScrollbar,ScrollAreaThumb,ScrollAreaViewport,Scrollbar,Thumb,Viewport,createScrollAreaScope auto */ // src/scroll-area.tsx\n\n\n\n\n\n\n\n\n\n\n// src/use-state-machine.ts\n\nfunction useStateMachine(initialState, machine) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useReducer((state, event)=>{\n        const nextState = machine[state][event];\n        return nextState ?? state;\n    }, initialState);\n}\n// src/scroll-area.tsx\n\nvar SCROLL_AREA_NAME = \"ScrollArea\";\nvar [createScrollAreaContext, createScrollAreaScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(SCROLL_AREA_NAME);\nvar [ScrollAreaProvider, useScrollAreaContext] = createScrollAreaContext(SCROLL_AREA_NAME);\nvar ScrollArea = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, type = \"hover\", dir, scrollHideDelay = 600, ...scrollAreaProps } = props;\n    const [scrollArea, setScrollArea] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [viewport, setViewport] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [scrollbarX, setScrollbarX] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [scrollbarY, setScrollbarY] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [cornerWidth, setCornerWidth] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [cornerHeight, setCornerHeight] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [scrollbarXEnabled, setScrollbarXEnabled] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [scrollbarYEnabled, setScrollbarYEnabled] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node)=>setScrollArea(node));\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_4__.useDirection)(dir);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaProvider, {\n        scope: __scopeScrollArea,\n        type,\n        dir: direction,\n        scrollHideDelay,\n        scrollArea,\n        viewport,\n        onViewportChange: setViewport,\n        content,\n        onContentChange: setContent,\n        scrollbarX,\n        onScrollbarXChange: setScrollbarX,\n        scrollbarXEnabled,\n        onScrollbarXEnabledChange: setScrollbarXEnabled,\n        scrollbarY,\n        onScrollbarYChange: setScrollbarY,\n        scrollbarYEnabled,\n        onScrollbarYEnabledChange: setScrollbarYEnabled,\n        onCornerWidthChange: setCornerWidth,\n        onCornerHeightChange: setCornerHeight,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n            dir: direction,\n            ...scrollAreaProps,\n            ref: composedRefs,\n            style: {\n                position: \"relative\",\n                // Pass corner sizes as CSS vars to reduce re-renders of context consumers\n                [\"--radix-scroll-area-corner-width\"]: cornerWidth + \"px\",\n                [\"--radix-scroll-area-corner-height\"]: cornerHeight + \"px\",\n                ...props.style\n            }\n        })\n    });\n});\nScrollArea.displayName = SCROLL_AREA_NAME;\nvar VIEWPORT_NAME = \"ScrollAreaViewport\";\nvar ScrollAreaViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, children, nonce, ...viewportProps } = props;\n    const context = useScrollAreaContext(VIEWPORT_NAME, __scopeScrollArea);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref, context.onViewportChange);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"style\", {\n                dangerouslySetInnerHTML: {\n                    __html: `[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}`\n                },\n                nonce\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n                \"data-radix-scroll-area-viewport\": \"\",\n                ...viewportProps,\n                ref: composedRefs,\n                style: {\n                    /**\n             * We don't support `visible` because the intention is to have at least one scrollbar\n             * if this component is used and `visible` will behave like `auto` in that case\n             * https://developer.mozilla.org/en-US/docs/Web/CSS/overflow#description\n             *\n             * We don't handle `auto` because the intention is for the native implementation\n             * to be hidden if using this component. We just want to ensure the node is scrollable\n             * so could have used either `scroll` or `auto` here. We picked `scroll` to prevent\n             * the browser from having to work out whether to render native scrollbars or not,\n             * we tell it to with the intention of hiding them in CSS.\n             */ overflowX: context.scrollbarXEnabled ? \"scroll\" : \"hidden\",\n                    overflowY: context.scrollbarYEnabled ? \"scroll\" : \"hidden\",\n                    ...props.style\n                },\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", {\n                    ref: context.onContentChange,\n                    style: {\n                        minWidth: \"100%\",\n                        display: \"table\"\n                    },\n                    children\n                })\n            })\n        ]\n    });\n});\nScrollAreaViewport.displayName = VIEWPORT_NAME;\nvar SCROLLBAR_NAME = \"ScrollAreaScrollbar\";\nvar ScrollAreaScrollbar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const { onScrollbarXEnabledChange, onScrollbarYEnabledChange } = context;\n    const isHorizontal = props.orientation === \"horizontal\";\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        isHorizontal ? onScrollbarXEnabledChange(true) : onScrollbarYEnabledChange(true);\n        return ()=>{\n            isHorizontal ? onScrollbarXEnabledChange(false) : onScrollbarYEnabledChange(false);\n        };\n    }, [\n        isHorizontal,\n        onScrollbarXEnabledChange,\n        onScrollbarYEnabledChange\n    ]);\n    return context.type === \"hover\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarHover, {\n        ...scrollbarProps,\n        ref: forwardedRef,\n        forceMount\n    }) : context.type === \"scroll\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarScroll, {\n        ...scrollbarProps,\n        ref: forwardedRef,\n        forceMount\n    }) : context.type === \"auto\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarAuto, {\n        ...scrollbarProps,\n        ref: forwardedRef,\n        forceMount\n    }) : context.type === \"always\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarVisible, {\n        ...scrollbarProps,\n        ref: forwardedRef\n    }) : null;\n});\nScrollAreaScrollbar.displayName = SCROLLBAR_NAME;\nvar ScrollAreaScrollbarHover = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const [visible, setVisible] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const scrollArea = context.scrollArea;\n        let hideTimer = 0;\n        if (scrollArea) {\n            const handlePointerEnter = ()=>{\n                window.clearTimeout(hideTimer);\n                setVisible(true);\n            };\n            const handlePointerLeave = ()=>{\n                hideTimer = window.setTimeout(()=>setVisible(false), context.scrollHideDelay);\n            };\n            scrollArea.addEventListener(\"pointerenter\", handlePointerEnter);\n            scrollArea.addEventListener(\"pointerleave\", handlePointerLeave);\n            return ()=>{\n                window.clearTimeout(hideTimer);\n                scrollArea.removeEventListener(\"pointerenter\", handlePointerEnter);\n                scrollArea.removeEventListener(\"pointerleave\", handlePointerLeave);\n            };\n        }\n    }, [\n        context.scrollArea,\n        context.scrollHideDelay\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || visible,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarAuto, {\n            \"data-state\": visible ? \"visible\" : \"hidden\",\n            ...scrollbarProps,\n            ref: forwardedRef\n        })\n    });\n});\nvar ScrollAreaScrollbarScroll = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const isHorizontal = props.orientation === \"horizontal\";\n    const debounceScrollEnd = useDebounceCallback(()=>send(\"SCROLL_END\"), 100);\n    const [state, send] = useStateMachine(\"hidden\", {\n        hidden: {\n            SCROLL: \"scrolling\"\n        },\n        scrolling: {\n            SCROLL_END: \"idle\",\n            POINTER_ENTER: \"interacting\"\n        },\n        interacting: {\n            SCROLL: \"interacting\",\n            POINTER_LEAVE: \"idle\"\n        },\n        idle: {\n            HIDE: \"hidden\",\n            SCROLL: \"scrolling\",\n            POINTER_ENTER: \"interacting\"\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (state === \"idle\") {\n            const hideTimer = window.setTimeout(()=>send(\"HIDE\"), context.scrollHideDelay);\n            return ()=>window.clearTimeout(hideTimer);\n        }\n    }, [\n        state,\n        context.scrollHideDelay,\n        send\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const viewport = context.viewport;\n        const scrollDirection = isHorizontal ? \"scrollLeft\" : \"scrollTop\";\n        if (viewport) {\n            let prevScrollPos = viewport[scrollDirection];\n            const handleScroll = ()=>{\n                const scrollPos = viewport[scrollDirection];\n                const hasScrollInDirectionChanged = prevScrollPos !== scrollPos;\n                if (hasScrollInDirectionChanged) {\n                    send(\"SCROLL\");\n                    debounceScrollEnd();\n                }\n                prevScrollPos = scrollPos;\n            };\n            viewport.addEventListener(\"scroll\", handleScroll);\n            return ()=>viewport.removeEventListener(\"scroll\", handleScroll);\n        }\n    }, [\n        context.viewport,\n        isHorizontal,\n        send,\n        debounceScrollEnd\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || state !== \"hidden\",\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarVisible, {\n            \"data-state\": state === \"hidden\" ? \"hidden\" : \"visible\",\n            ...scrollbarProps,\n            ref: forwardedRef,\n            onPointerEnter: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerEnter, ()=>send(\"POINTER_ENTER\")),\n            onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerLeave, ()=>send(\"POINTER_LEAVE\"))\n        })\n    });\n});\nvar ScrollAreaScrollbarAuto = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const { forceMount, ...scrollbarProps } = props;\n    const [visible, setVisible] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const isHorizontal = props.orientation === \"horizontal\";\n    const handleResize = useDebounceCallback(()=>{\n        if (context.viewport) {\n            const isOverflowX = context.viewport.offsetWidth < context.viewport.scrollWidth;\n            const isOverflowY = context.viewport.offsetHeight < context.viewport.scrollHeight;\n            setVisible(isHorizontal ? isOverflowX : isOverflowY);\n        }\n    }, 10);\n    useResizeObserver(context.viewport, handleResize);\n    useResizeObserver(context.content, handleResize);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || visible,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarVisible, {\n            \"data-state\": visible ? \"visible\" : \"hidden\",\n            ...scrollbarProps,\n            ref: forwardedRef\n        })\n    });\n});\nvar ScrollAreaScrollbarVisible = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { orientation = \"vertical\", ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const thumbRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const pointerOffsetRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const [sizes, setSizes] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        content: 0,\n        viewport: 0,\n        scrollbar: {\n            size: 0,\n            paddingStart: 0,\n            paddingEnd: 0\n        }\n    });\n    const thumbRatio = getThumbRatio(sizes.viewport, sizes.content);\n    const commonProps = {\n        ...scrollbarProps,\n        sizes,\n        onSizesChange: setSizes,\n        hasThumb: Boolean(thumbRatio > 0 && thumbRatio < 1),\n        onThumbChange: (thumb)=>thumbRef.current = thumb,\n        onThumbPointerUp: ()=>pointerOffsetRef.current = 0,\n        onThumbPointerDown: (pointerPos)=>pointerOffsetRef.current = pointerPos\n    };\n    function getScrollPosition(pointerPos, dir) {\n        return getScrollPositionFromPointer(pointerPos, pointerOffsetRef.current, sizes, dir);\n    }\n    if (orientation === \"horizontal\") {\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarX, {\n            ...commonProps,\n            ref: forwardedRef,\n            onThumbPositionChange: ()=>{\n                if (context.viewport && thumbRef.current) {\n                    const scrollPos = context.viewport.scrollLeft;\n                    const offset = getThumbOffsetFromScroll(scrollPos, sizes, context.dir);\n                    thumbRef.current.style.transform = `translate3d(${offset}px, 0, 0)`;\n                }\n            },\n            onWheelScroll: (scrollPos)=>{\n                if (context.viewport) context.viewport.scrollLeft = scrollPos;\n            },\n            onDragScroll: (pointerPos)=>{\n                if (context.viewport) {\n                    context.viewport.scrollLeft = getScrollPosition(pointerPos, context.dir);\n                }\n            }\n        });\n    }\n    if (orientation === \"vertical\") {\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarY, {\n            ...commonProps,\n            ref: forwardedRef,\n            onThumbPositionChange: ()=>{\n                if (context.viewport && thumbRef.current) {\n                    const scrollPos = context.viewport.scrollTop;\n                    const offset = getThumbOffsetFromScroll(scrollPos, sizes);\n                    thumbRef.current.style.transform = `translate3d(0, ${offset}px, 0)`;\n                }\n            },\n            onWheelScroll: (scrollPos)=>{\n                if (context.viewport) context.viewport.scrollTop = scrollPos;\n            },\n            onDragScroll: (pointerPos)=>{\n                if (context.viewport) context.viewport.scrollTop = getScrollPosition(pointerPos);\n            }\n        });\n    }\n    return null;\n});\nvar ScrollAreaScrollbarX = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { sizes, onSizesChange, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const [computedStyle, setComputedStyle] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref, context.onScrollbarXChange);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (ref.current) setComputedStyle(getComputedStyle(ref.current));\n    }, [\n        ref\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarImpl, {\n        \"data-orientation\": \"horizontal\",\n        ...scrollbarProps,\n        ref: composeRefs,\n        sizes,\n        style: {\n            bottom: 0,\n            left: context.dir === \"rtl\" ? \"var(--radix-scroll-area-corner-width)\" : 0,\n            right: context.dir === \"ltr\" ? \"var(--radix-scroll-area-corner-width)\" : 0,\n            [\"--radix-scroll-area-thumb-width\"]: getThumbSize(sizes) + \"px\",\n            ...props.style\n        },\n        onThumbPointerDown: (pointerPos)=>props.onThumbPointerDown(pointerPos.x),\n        onDragScroll: (pointerPos)=>props.onDragScroll(pointerPos.x),\n        onWheelScroll: (event, maxScrollPos)=>{\n            if (context.viewport) {\n                const scrollPos = context.viewport.scrollLeft + event.deltaX;\n                props.onWheelScroll(scrollPos);\n                if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {\n                    event.preventDefault();\n                }\n            }\n        },\n        onResize: ()=>{\n            if (ref.current && context.viewport && computedStyle) {\n                onSizesChange({\n                    content: context.viewport.scrollWidth,\n                    viewport: context.viewport.offsetWidth,\n                    scrollbar: {\n                        size: ref.current.clientWidth,\n                        paddingStart: toInt(computedStyle.paddingLeft),\n                        paddingEnd: toInt(computedStyle.paddingRight)\n                    }\n                });\n            }\n        }\n    });\n});\nvar ScrollAreaScrollbarY = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { sizes, onSizesChange, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const [computedStyle, setComputedStyle] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref, context.onScrollbarYChange);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (ref.current) setComputedStyle(getComputedStyle(ref.current));\n    }, [\n        ref\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarImpl, {\n        \"data-orientation\": \"vertical\",\n        ...scrollbarProps,\n        ref: composeRefs,\n        sizes,\n        style: {\n            top: 0,\n            right: context.dir === \"ltr\" ? 0 : void 0,\n            left: context.dir === \"rtl\" ? 0 : void 0,\n            bottom: \"var(--radix-scroll-area-corner-height)\",\n            [\"--radix-scroll-area-thumb-height\"]: getThumbSize(sizes) + \"px\",\n            ...props.style\n        },\n        onThumbPointerDown: (pointerPos)=>props.onThumbPointerDown(pointerPos.y),\n        onDragScroll: (pointerPos)=>props.onDragScroll(pointerPos.y),\n        onWheelScroll: (event, maxScrollPos)=>{\n            if (context.viewport) {\n                const scrollPos = context.viewport.scrollTop + event.deltaY;\n                props.onWheelScroll(scrollPos);\n                if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {\n                    event.preventDefault();\n                }\n            }\n        },\n        onResize: ()=>{\n            if (ref.current && context.viewport && computedStyle) {\n                onSizesChange({\n                    content: context.viewport.scrollHeight,\n                    viewport: context.viewport.offsetHeight,\n                    scrollbar: {\n                        size: ref.current.clientHeight,\n                        paddingStart: toInt(computedStyle.paddingTop),\n                        paddingEnd: toInt(computedStyle.paddingBottom)\n                    }\n                });\n            }\n        }\n    });\n});\nvar [ScrollbarProvider, useScrollbarContext] = createScrollAreaContext(SCROLLBAR_NAME);\nvar ScrollAreaScrollbarImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, sizes, hasThumb, onThumbChange, onThumbPointerUp, onThumbPointerDown, onThumbPositionChange, onDragScroll, onWheelScroll, onResize, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, __scopeScrollArea);\n    const [scrollbar, setScrollbar] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node)=>setScrollbar(node));\n    const rectRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const prevWebkitUserSelectRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"\");\n    const viewport = context.viewport;\n    const maxScrollPos = sizes.content - sizes.viewport;\n    const handleWheelScroll = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onWheelScroll);\n    const handleThumbPositionChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbPositionChange);\n    const handleResize = useDebounceCallback(onResize, 10);\n    function handleDragScroll(event) {\n        if (rectRef.current) {\n            const x = event.clientX - rectRef.current.left;\n            const y = event.clientY - rectRef.current.top;\n            onDragScroll({\n                x,\n                y\n            });\n        }\n    }\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleWheel = (event)=>{\n            const element = event.target;\n            const isScrollbarWheel = scrollbar?.contains(element);\n            if (isScrollbarWheel) handleWheelScroll(event, maxScrollPos);\n        };\n        document.addEventListener(\"wheel\", handleWheel, {\n            passive: false\n        });\n        return ()=>document.removeEventListener(\"wheel\", handleWheel, {\n                passive: false\n            });\n    }, [\n        viewport,\n        scrollbar,\n        maxScrollPos,\n        handleWheelScroll\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(handleThumbPositionChange, [\n        sizes,\n        handleThumbPositionChange\n    ]);\n    useResizeObserver(scrollbar, handleResize);\n    useResizeObserver(context.content, handleResize);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollbarProvider, {\n        scope: __scopeScrollArea,\n        scrollbar,\n        hasThumb,\n        onThumbChange: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbChange),\n        onThumbPointerUp: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbPointerUp),\n        onThumbPositionChange: handleThumbPositionChange,\n        onThumbPointerDown: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbPointerDown),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n            ...scrollbarProps,\n            ref: composeRefs,\n            style: {\n                position: \"absolute\",\n                ...scrollbarProps.style\n            },\n            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerDown, (event)=>{\n                const mainPointer = 0;\n                if (event.button === mainPointer) {\n                    const element = event.target;\n                    element.setPointerCapture(event.pointerId);\n                    rectRef.current = scrollbar.getBoundingClientRect();\n                    prevWebkitUserSelectRef.current = document.body.style.webkitUserSelect;\n                    document.body.style.webkitUserSelect = \"none\";\n                    if (context.viewport) context.viewport.style.scrollBehavior = \"auto\";\n                    handleDragScroll(event);\n                }\n            }),\n            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerMove, handleDragScroll),\n            onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerUp, (event)=>{\n                const element = event.target;\n                if (element.hasPointerCapture(event.pointerId)) {\n                    element.releasePointerCapture(event.pointerId);\n                }\n                document.body.style.webkitUserSelect = prevWebkitUserSelectRef.current;\n                if (context.viewport) context.viewport.style.scrollBehavior = \"\";\n                rectRef.current = null;\n            })\n        })\n    });\n});\nvar THUMB_NAME = \"ScrollAreaThumb\";\nvar ScrollAreaThumb = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...thumbProps } = props;\n    const scrollbarContext = useScrollbarContext(THUMB_NAME, props.__scopeScrollArea);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || scrollbarContext.hasThumb,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaThumbImpl, {\n            ref: forwardedRef,\n            ...thumbProps\n        })\n    });\n});\nvar ScrollAreaThumbImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, style, ...thumbProps } = props;\n    const scrollAreaContext = useScrollAreaContext(THUMB_NAME, __scopeScrollArea);\n    const scrollbarContext = useScrollbarContext(THUMB_NAME, __scopeScrollArea);\n    const { onThumbPositionChange } = scrollbarContext;\n    const composedRef = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node)=>scrollbarContext.onThumbChange(node));\n    const removeUnlinkedScrollListenerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(void 0);\n    const debounceScrollEnd = useDebounceCallback(()=>{\n        if (removeUnlinkedScrollListenerRef.current) {\n            removeUnlinkedScrollListenerRef.current();\n            removeUnlinkedScrollListenerRef.current = void 0;\n        }\n    }, 100);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const viewport = scrollAreaContext.viewport;\n        if (viewport) {\n            const handleScroll = ()=>{\n                debounceScrollEnd();\n                if (!removeUnlinkedScrollListenerRef.current) {\n                    const listener = addUnlinkedScrollListener(viewport, onThumbPositionChange);\n                    removeUnlinkedScrollListenerRef.current = listener;\n                    onThumbPositionChange();\n                }\n            };\n            onThumbPositionChange();\n            viewport.addEventListener(\"scroll\", handleScroll);\n            return ()=>viewport.removeEventListener(\"scroll\", handleScroll);\n        }\n    }, [\n        scrollAreaContext.viewport,\n        debounceScrollEnd,\n        onThumbPositionChange\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n        \"data-state\": scrollbarContext.hasThumb ? \"visible\" : \"hidden\",\n        ...thumbProps,\n        ref: composedRef,\n        style: {\n            width: \"var(--radix-scroll-area-thumb-width)\",\n            height: \"var(--radix-scroll-area-thumb-height)\",\n            ...style\n        },\n        onPointerDownCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerDownCapture, (event)=>{\n            const thumb = event.target;\n            const thumbRect = thumb.getBoundingClientRect();\n            const x = event.clientX - thumbRect.left;\n            const y = event.clientY - thumbRect.top;\n            scrollbarContext.onThumbPointerDown({\n                x,\n                y\n            });\n        }),\n        onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerUp, scrollbarContext.onThumbPointerUp)\n    });\n});\nScrollAreaThumb.displayName = THUMB_NAME;\nvar CORNER_NAME = \"ScrollAreaCorner\";\nvar ScrollAreaCorner = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useScrollAreaContext(CORNER_NAME, props.__scopeScrollArea);\n    const hasBothScrollbarsVisible = Boolean(context.scrollbarX && context.scrollbarY);\n    const hasCorner = context.type !== \"scroll\" && hasBothScrollbarsVisible;\n    return hasCorner ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaCornerImpl, {\n        ...props,\n        ref: forwardedRef\n    }) : null;\n});\nScrollAreaCorner.displayName = CORNER_NAME;\nvar ScrollAreaCornerImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, ...cornerProps } = props;\n    const context = useScrollAreaContext(CORNER_NAME, __scopeScrollArea);\n    const [width, setWidth] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [height, setHeight] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const hasSize = Boolean(width && height);\n    useResizeObserver(context.scrollbarX, ()=>{\n        const height2 = context.scrollbarX?.offsetHeight || 0;\n        context.onCornerHeightChange(height2);\n        setHeight(height2);\n    });\n    useResizeObserver(context.scrollbarY, ()=>{\n        const width2 = context.scrollbarY?.offsetWidth || 0;\n        context.onCornerWidthChange(width2);\n        setWidth(width2);\n    });\n    return hasSize ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n        ...cornerProps,\n        ref: forwardedRef,\n        style: {\n            width,\n            height,\n            position: \"absolute\",\n            right: context.dir === \"ltr\" ? 0 : void 0,\n            left: context.dir === \"rtl\" ? 0 : void 0,\n            bottom: 0,\n            ...props.style\n        }\n    }) : null;\n});\nfunction toInt(value) {\n    return value ? parseInt(value, 10) : 0;\n}\nfunction getThumbRatio(viewportSize, contentSize) {\n    const ratio = viewportSize / contentSize;\n    return isNaN(ratio) ? 0 : ratio;\n}\nfunction getThumbSize(sizes) {\n    const ratio = getThumbRatio(sizes.viewport, sizes.content);\n    const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n    const thumbSize = (sizes.scrollbar.size - scrollbarPadding) * ratio;\n    return Math.max(thumbSize, 18);\n}\nfunction getScrollPositionFromPointer(pointerPos, pointerOffset, sizes, dir = \"ltr\") {\n    const thumbSizePx = getThumbSize(sizes);\n    const thumbCenter = thumbSizePx / 2;\n    const offset = pointerOffset || thumbCenter;\n    const thumbOffsetFromEnd = thumbSizePx - offset;\n    const minPointerPos = sizes.scrollbar.paddingStart + offset;\n    const maxPointerPos = sizes.scrollbar.size - sizes.scrollbar.paddingEnd - thumbOffsetFromEnd;\n    const maxScrollPos = sizes.content - sizes.viewport;\n    const scrollRange = dir === \"ltr\" ? [\n        0,\n        maxScrollPos\n    ] : [\n        maxScrollPos * -1,\n        0\n    ];\n    const interpolate = linearScale([\n        minPointerPos,\n        maxPointerPos\n    ], scrollRange);\n    return interpolate(pointerPos);\n}\nfunction getThumbOffsetFromScroll(scrollPos, sizes, dir = \"ltr\") {\n    const thumbSizePx = getThumbSize(sizes);\n    const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n    const scrollbar = sizes.scrollbar.size - scrollbarPadding;\n    const maxScrollPos = sizes.content - sizes.viewport;\n    const maxThumbPos = scrollbar - thumbSizePx;\n    const scrollClampRange = dir === \"ltr\" ? [\n        0,\n        maxScrollPos\n    ] : [\n        maxScrollPos * -1,\n        0\n    ];\n    const scrollWithoutMomentum = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_9__.clamp)(scrollPos, scrollClampRange);\n    const interpolate = linearScale([\n        0,\n        maxScrollPos\n    ], [\n        0,\n        maxThumbPos\n    ]);\n    return interpolate(scrollWithoutMomentum);\n}\nfunction linearScale(input, output) {\n    return (value)=>{\n        if (input[0] === input[1] || output[0] === output[1]) return output[0];\n        const ratio = (output[1] - output[0]) / (input[1] - input[0]);\n        return output[0] + ratio * (value - input[0]);\n    };\n}\nfunction isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos) {\n    return scrollPos > 0 && scrollPos < maxScrollPos;\n}\nvar addUnlinkedScrollListener = (node, handler = ()=>{})=>{\n    let prevPosition = {\n        left: node.scrollLeft,\n        top: node.scrollTop\n    };\n    let rAF = 0;\n    (function loop() {\n        const position = {\n            left: node.scrollLeft,\n            top: node.scrollTop\n        };\n        const isHorizontalScroll = prevPosition.left !== position.left;\n        const isVerticalScroll = prevPosition.top !== position.top;\n        if (isHorizontalScroll || isVerticalScroll) handler();\n        prevPosition = position;\n        rAF = window.requestAnimationFrame(loop);\n    })();\n    return ()=>window.cancelAnimationFrame(rAF);\n};\nfunction useDebounceCallback(callback, delay) {\n    const handleCallback = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(callback);\n    const debounceTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>()=>window.clearTimeout(debounceTimerRef.current), []);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        window.clearTimeout(debounceTimerRef.current);\n        debounceTimerRef.current = window.setTimeout(handleCallback, delay);\n    }, [\n        handleCallback,\n        delay\n    ]);\n}\nfunction useResizeObserver(element, onResize) {\n    const handleResize = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onResize);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_10__.useLayoutEffect)(()=>{\n        let rAF = 0;\n        if (element) {\n            const resizeObserver = new ResizeObserver(()=>{\n                cancelAnimationFrame(rAF);\n                rAF = window.requestAnimationFrame(handleResize);\n            });\n            resizeObserver.observe(element);\n            return ()=>{\n                window.cancelAnimationFrame(rAF);\n                resizeObserver.unobserve(element);\n            };\n        }\n    }, [\n        element,\n        handleResize\n    ]);\n}\nvar Root = ScrollArea;\nvar Viewport = ScrollAreaViewport;\nvar Scrollbar = ScrollAreaScrollbar;\nvar Thumb = ScrollAreaThumb;\nvar Corner = ScrollAreaCorner;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-scroll-area/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@radix-ui/react-slot/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Slot),\n/* harmony export */   Slot: () => (/* binding */ Slot),\n/* harmony export */   Slottable: () => (/* binding */ Slottable),\n/* harmony export */   createSlot: () => (/* binding */ createSlot),\n/* harmony export */   createSlottable: () => (/* binding */ createSlottable)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/slot.tsx\n\n\n\n// @__NO_SIDE_EFFECTS__\nfunction createSlot(ownerName) {\n    const SlotClone = /* @__PURE__ */ createSlotClone(ownerName);\n    const Slot2 = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { children, ...slotProps } = props;\n        const childrenArray = react__WEBPACK_IMPORTED_MODULE_0__.Children.toArray(children);\n        const slottable = childrenArray.find(isSlottable);\n        if (slottable) {\n            const newElement = slottable.props.children;\n            const newChildren = childrenArray.map((child)=>{\n                if (child === slottable) {\n                    if (react__WEBPACK_IMPORTED_MODULE_0__.Children.count(newElement) > 1) return react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null);\n                    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? newElement.props.children : null;\n                } else {\n                    return child;\n                }\n            });\n            return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, {\n                ...slotProps,\n                ref: forwardedRef,\n                children: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(newElement, void 0, newChildren) : null\n            });\n        }\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, {\n            ...slotProps,\n            ref: forwardedRef,\n            children\n        });\n    });\n    Slot2.displayName = `${ownerName}.Slot`;\n    return Slot2;\n}\nvar Slot = /* @__PURE__ */ createSlot(\"Slot\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlotClone(ownerName) {\n    const SlotClone = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { children, ...slotProps } = props;\n        if (/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(children)) {\n            const childrenRef = getElementRef(children);\n            const props2 = mergeProps(slotProps, children.props);\n            if (children.type !== react__WEBPACK_IMPORTED_MODULE_0__.Fragment) {\n                props2.ref = forwardedRef ? (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.composeRefs)(forwardedRef, childrenRef) : childrenRef;\n            }\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(children, props2);\n        }\n        return react__WEBPACK_IMPORTED_MODULE_0__.Children.count(children) > 1 ? react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null) : null;\n    });\n    SlotClone.displayName = `${ownerName}.SlotClone`;\n    return SlotClone;\n}\nvar SLOTTABLE_IDENTIFIER = Symbol(\"radix.slottable\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlottable(ownerName) {\n    const Slottable2 = ({ children })=>{\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n            children\n        });\n    };\n    Slottable2.displayName = `${ownerName}.Slottable`;\n    Slottable2.__radixId = SLOTTABLE_IDENTIFIER;\n    return Slottable2;\n}\nvar Slottable = /* @__PURE__ */ createSlottable(\"Slottable\");\nfunction isSlottable(child) {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(child) && typeof child.type === \"function\" && \"__radixId\" in child.type && child.type.__radixId === SLOTTABLE_IDENTIFIER;\n}\nfunction mergeProps(slotProps, childProps) {\n    const overrideProps = {\n        ...childProps\n    };\n    for(const propName in childProps){\n        const slotPropValue = slotProps[propName];\n        const childPropValue = childProps[propName];\n        const isHandler = /^on[A-Z]/.test(propName);\n        if (isHandler) {\n            if (slotPropValue && childPropValue) {\n                overrideProps[propName] = (...args)=>{\n                    const result = childPropValue(...args);\n                    slotPropValue(...args);\n                    return result;\n                };\n            } else if (slotPropValue) {\n                overrideProps[propName] = slotPropValue;\n            }\n        } else if (propName === \"style\") {\n            overrideProps[propName] = {\n                ...slotPropValue,\n                ...childPropValue\n            };\n        } else if (propName === \"className\") {\n            overrideProps[propName] = [\n                slotPropValue,\n                childPropValue\n            ].filter(Boolean).join(\" \");\n        }\n    }\n    return {\n        ...slotProps,\n        ...overrideProps\n    };\n}\nfunction getElementRef(element) {\n    let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n    let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.ref;\n    }\n    getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n    mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.props.ref;\n    }\n    return element.props.ref || element.ref;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-toast/dist/index.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/@radix-ui/react-toast/dist/index.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Action: () => (/* binding */ Action),\n/* harmony export */   Close: () => (/* binding */ Close),\n/* harmony export */   Description: () => (/* binding */ Description),\n/* harmony export */   Provider: () => (/* binding */ Provider),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   Title: () => (/* binding */ Title),\n/* harmony export */   Toast: () => (/* binding */ Toast),\n/* harmony export */   ToastAction: () => (/* binding */ ToastAction),\n/* harmony export */   ToastClose: () => (/* binding */ ToastClose),\n/* harmony export */   ToastDescription: () => (/* binding */ ToastDescription),\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   ToastTitle: () => (/* binding */ ToastTitle),\n/* harmony export */   ToastViewport: () => (/* binding */ ToastViewport),\n/* harmony export */   Viewport: () => (/* binding */ Viewport),\n/* harmony export */   createToastScope: () => (/* binding */ createToastScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/./node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-visually-hidden */ \"(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Action,Close,Description,Provider,Root,Title,Toast,ToastAction,ToastClose,ToastDescription,ToastProvider,ToastTitle,ToastViewport,Viewport,createToastScope auto */ // src/toast.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar PROVIDER_NAME = \"ToastProvider\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__.createCollection)(\"Toast\");\nvar [createToastContext, createToastScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__.createContextScope)(\"Toast\", [\n    createCollectionScope\n]);\nvar [ToastProviderProvider, useToastProviderContext] = createToastContext(PROVIDER_NAME);\nvar ToastProvider = (props)=>{\n    const { __scopeToast, label = \"Notification\", duration = 5e3, swipeDirection = \"right\", swipeThreshold = 50, children } = props;\n    const [viewport, setViewport] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [toastCount, setToastCount] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const isFocusedToastEscapeKeyDownRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const isClosePausedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    if (!label.trim()) {\n        console.error(`Invalid prop \\`label\\` supplied to \\`${PROVIDER_NAME}\\`. Expected non-empty \\`string\\`.`);\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Provider, {\n        scope: __scopeToast,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastProviderProvider, {\n            scope: __scopeToast,\n            label,\n            duration,\n            swipeDirection,\n            swipeThreshold,\n            toastCount,\n            viewport,\n            onViewportChange: setViewport,\n            onToastAdd: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>setToastCount((prevCount)=>prevCount + 1), []),\n            onToastRemove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>setToastCount((prevCount)=>prevCount - 1), []),\n            isFocusedToastEscapeKeyDownRef,\n            isClosePausedRef,\n            children\n        })\n    });\n};\nToastProvider.displayName = PROVIDER_NAME;\nvar VIEWPORT_NAME = \"ToastViewport\";\nvar VIEWPORT_DEFAULT_HOTKEY = [\n    \"F8\"\n];\nvar VIEWPORT_PAUSE = \"toast.viewportPause\";\nvar VIEWPORT_RESUME = \"toast.viewportResume\";\nvar ToastViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, hotkey = VIEWPORT_DEFAULT_HOTKEY, label = \"Notifications ({hotkey})\", ...viewportProps } = props;\n    const context = useToastProviderContext(VIEWPORT_NAME, __scopeToast);\n    const getItems = useCollection(__scopeToast);\n    const wrapperRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const headFocusProxyRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const tailFocusProxyRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, ref, context.onViewportChange);\n    const hotkeyLabel = hotkey.join(\"+\").replace(/Key/g, \"\").replace(/Digit/g, \"\");\n    const hasToasts = context.toastCount > 0;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleKeyDown = (event)=>{\n            const isHotkeyPressed = hotkey.length !== 0 && hotkey.every((key)=>event[key] || event.code === key);\n            if (isHotkeyPressed) ref.current?.focus();\n        };\n        document.addEventListener(\"keydown\", handleKeyDown);\n        return ()=>document.removeEventListener(\"keydown\", handleKeyDown);\n    }, [\n        hotkey\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const wrapper = wrapperRef.current;\n        const viewport = ref.current;\n        if (hasToasts && wrapper && viewport) {\n            const handlePause = ()=>{\n                if (!context.isClosePausedRef.current) {\n                    const pauseEvent = new CustomEvent(VIEWPORT_PAUSE);\n                    viewport.dispatchEvent(pauseEvent);\n                    context.isClosePausedRef.current = true;\n                }\n            };\n            const handleResume = ()=>{\n                if (context.isClosePausedRef.current) {\n                    const resumeEvent = new CustomEvent(VIEWPORT_RESUME);\n                    viewport.dispatchEvent(resumeEvent);\n                    context.isClosePausedRef.current = false;\n                }\n            };\n            const handleFocusOutResume = (event)=>{\n                const isFocusMovingOutside = !wrapper.contains(event.relatedTarget);\n                if (isFocusMovingOutside) handleResume();\n            };\n            const handlePointerLeaveResume = ()=>{\n                const isFocusInside = wrapper.contains(document.activeElement);\n                if (!isFocusInside) handleResume();\n            };\n            wrapper.addEventListener(\"focusin\", handlePause);\n            wrapper.addEventListener(\"focusout\", handleFocusOutResume);\n            wrapper.addEventListener(\"pointermove\", handlePause);\n            wrapper.addEventListener(\"pointerleave\", handlePointerLeaveResume);\n            window.addEventListener(\"blur\", handlePause);\n            window.addEventListener(\"focus\", handleResume);\n            return ()=>{\n                wrapper.removeEventListener(\"focusin\", handlePause);\n                wrapper.removeEventListener(\"focusout\", handleFocusOutResume);\n                wrapper.removeEventListener(\"pointermove\", handlePause);\n                wrapper.removeEventListener(\"pointerleave\", handlePointerLeaveResume);\n                window.removeEventListener(\"blur\", handlePause);\n                window.removeEventListener(\"focus\", handleResume);\n            };\n        }\n    }, [\n        hasToasts,\n        context.isClosePausedRef\n    ]);\n    const getSortedTabbableCandidates = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(({ tabbingDirection })=>{\n        const toastItems = getItems();\n        const tabbableCandidates = toastItems.map((toastItem)=>{\n            const toastNode = toastItem.ref.current;\n            const toastTabbableCandidates = [\n                toastNode,\n                ...getTabbableCandidates(toastNode)\n            ];\n            return tabbingDirection === \"forwards\" ? toastTabbableCandidates : toastTabbableCandidates.reverse();\n        });\n        return (tabbingDirection === \"forwards\" ? tabbableCandidates.reverse() : tabbableCandidates).flat();\n    }, [\n        getItems\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const viewport = ref.current;\n        if (viewport) {\n            const handleKeyDown = (event)=>{\n                const isMetaKey = event.altKey || event.ctrlKey || event.metaKey;\n                const isTabKey = event.key === \"Tab\" && !isMetaKey;\n                if (isTabKey) {\n                    const focusedElement = document.activeElement;\n                    const isTabbingBackwards = event.shiftKey;\n                    const targetIsViewport = event.target === viewport;\n                    if (targetIsViewport && isTabbingBackwards) {\n                        headFocusProxyRef.current?.focus();\n                        return;\n                    }\n                    const tabbingDirection = isTabbingBackwards ? \"backwards\" : \"forwards\";\n                    const sortedCandidates = getSortedTabbableCandidates({\n                        tabbingDirection\n                    });\n                    const index = sortedCandidates.findIndex((candidate)=>candidate === focusedElement);\n                    if (focusFirst(sortedCandidates.slice(index + 1))) {\n                        event.preventDefault();\n                    } else {\n                        isTabbingBackwards ? headFocusProxyRef.current?.focus() : tailFocusProxyRef.current?.focus();\n                    }\n                }\n            };\n            viewport.addEventListener(\"keydown\", handleKeyDown);\n            return ()=>viewport.removeEventListener(\"keydown\", handleKeyDown);\n        }\n    }, [\n        getItems,\n        getSortedTabbableCandidates\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_6__.Branch, {\n        ref: wrapperRef,\n        role: \"region\",\n        \"aria-label\": label.replace(\"{hotkey}\", hotkeyLabel),\n        tabIndex: -1,\n        style: {\n            pointerEvents: hasToasts ? void 0 : \"none\"\n        },\n        children: [\n            hasToasts && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusProxy, {\n                ref: headFocusProxyRef,\n                onFocusFromOutsideViewport: ()=>{\n                    const tabbableCandidates = getSortedTabbableCandidates({\n                        tabbingDirection: \"forwards\"\n                    });\n                    focusFirst(tabbableCandidates);\n                }\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Slot, {\n                scope: __scopeToast,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.ol, {\n                    tabIndex: -1,\n                    ...viewportProps,\n                    ref: composedRefs\n                })\n            }),\n            hasToasts && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusProxy, {\n                ref: tailFocusProxyRef,\n                onFocusFromOutsideViewport: ()=>{\n                    const tabbableCandidates = getSortedTabbableCandidates({\n                        tabbingDirection: \"backwards\"\n                    });\n                    focusFirst(tabbableCandidates);\n                }\n            })\n        ]\n    });\n});\nToastViewport.displayName = VIEWPORT_NAME;\nvar FOCUS_PROXY_NAME = \"ToastFocusProxy\";\nvar FocusProxy = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, onFocusFromOutsideViewport, ...proxyProps } = props;\n    const context = useToastProviderContext(FOCUS_PROXY_NAME, __scopeToast);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_8__.VisuallyHidden, {\n        \"aria-hidden\": true,\n        tabIndex: 0,\n        ...proxyProps,\n        ref: forwardedRef,\n        style: {\n            position: \"fixed\"\n        },\n        onFocus: (event)=>{\n            const prevFocusedElement = event.relatedTarget;\n            const isFocusFromOutsideViewport = !context.viewport?.contains(prevFocusedElement);\n            if (isFocusFromOutsideViewport) onFocusFromOutsideViewport();\n        }\n    });\n});\nFocusProxy.displayName = FOCUS_PROXY_NAME;\nvar TOAST_NAME = \"Toast\";\nvar TOAST_SWIPE_START = \"toast.swipeStart\";\nvar TOAST_SWIPE_MOVE = \"toast.swipeMove\";\nvar TOAST_SWIPE_CANCEL = \"toast.swipeCancel\";\nvar TOAST_SWIPE_END = \"toast.swipeEnd\";\nvar Toast = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, open: openProp, defaultOpen, onOpenChange, ...toastProps } = props;\n    const [open, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_9__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen ?? true,\n        onChange: onOpenChange,\n        caller: TOAST_NAME\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_10__.Presence, {\n        present: forceMount || open,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastImpl, {\n            open,\n            ...toastProps,\n            ref: forwardedRef,\n            onClose: ()=>setOpen(false),\n            onPause: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(props.onPause),\n            onResume: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(props.onResume),\n            onSwipeStart: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeStart, (event)=>{\n                event.currentTarget.setAttribute(\"data-swipe\", \"start\");\n            }),\n            onSwipeMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeMove, (event)=>{\n                const { x, y } = event.detail.delta;\n                event.currentTarget.setAttribute(\"data-swipe\", \"move\");\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-move-x\", `${x}px`);\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-move-y\", `${y}px`);\n            }),\n            onSwipeCancel: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeCancel, (event)=>{\n                event.currentTarget.setAttribute(\"data-swipe\", \"cancel\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-x\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-y\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-end-x\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-end-y\");\n            }),\n            onSwipeEnd: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeEnd, (event)=>{\n                const { x, y } = event.detail.delta;\n                event.currentTarget.setAttribute(\"data-swipe\", \"end\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-x\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-y\");\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-end-x\", `${x}px`);\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-end-y\", `${y}px`);\n                setOpen(false);\n            })\n        })\n    });\n});\nToast.displayName = TOAST_NAME;\nvar [ToastInteractiveProvider, useToastInteractiveContext] = createToastContext(TOAST_NAME, {\n    onClose () {}\n});\nvar ToastImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, type = \"foreground\", duration: durationProp, open, onClose, onEscapeKeyDown, onPause, onResume, onSwipeStart, onSwipeMove, onSwipeCancel, onSwipeEnd, ...toastProps } = props;\n    const context = useToastProviderContext(TOAST_NAME, __scopeToast);\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, (node2)=>setNode(node2));\n    const pointerStartRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const swipeDeltaRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const duration = durationProp || context.duration;\n    const closeTimerStartTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const closeTimerRemainingTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(duration);\n    const closeTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const { onToastAdd, onToastRemove } = context;\n    const handleClose = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(()=>{\n        const isFocusInToast = node?.contains(document.activeElement);\n        if (isFocusInToast) context.viewport?.focus();\n        onClose();\n    });\n    const startTimer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((duration2)=>{\n        if (!duration2 || duration2 === Infinity) return;\n        window.clearTimeout(closeTimerRef.current);\n        closeTimerStartTimeRef.current = /* @__PURE__ */ new Date().getTime();\n        closeTimerRef.current = window.setTimeout(handleClose, duration2);\n    }, [\n        handleClose\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const viewport = context.viewport;\n        if (viewport) {\n            const handleResume = ()=>{\n                startTimer(closeTimerRemainingTimeRef.current);\n                onResume?.();\n            };\n            const handlePause = ()=>{\n                const elapsedTime = /* @__PURE__ */ new Date().getTime() - closeTimerStartTimeRef.current;\n                closeTimerRemainingTimeRef.current = closeTimerRemainingTimeRef.current - elapsedTime;\n                window.clearTimeout(closeTimerRef.current);\n                onPause?.();\n            };\n            viewport.addEventListener(VIEWPORT_PAUSE, handlePause);\n            viewport.addEventListener(VIEWPORT_RESUME, handleResume);\n            return ()=>{\n                viewport.removeEventListener(VIEWPORT_PAUSE, handlePause);\n                viewport.removeEventListener(VIEWPORT_RESUME, handleResume);\n            };\n        }\n    }, [\n        context.viewport,\n        duration,\n        onPause,\n        onResume,\n        startTimer\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (open && !context.isClosePausedRef.current) startTimer(duration);\n    }, [\n        open,\n        duration,\n        context.isClosePausedRef,\n        startTimer\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        onToastAdd();\n        return ()=>onToastRemove();\n    }, [\n        onToastAdd,\n        onToastRemove\n    ]);\n    const announceTextContent = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        return node ? getAnnounceTextContent(node) : null;\n    }, [\n        node\n    ]);\n    if (!context.viewport) return null;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n        children: [\n            announceTextContent && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastAnnounce, {\n                __scopeToast,\n                role: \"status\",\n                \"aria-live\": type === \"foreground\" ? \"assertive\" : \"polite\",\n                \"aria-atomic\": true,\n                children: announceTextContent\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastInteractiveProvider, {\n                scope: __scopeToast,\n                onClose: handleClose,\n                children: /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.ItemSlot, {\n                    scope: __scopeToast,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_6__.Root, {\n                        asChild: true,\n                        onEscapeKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(onEscapeKeyDown, ()=>{\n                            if (!context.isFocusedToastEscapeKeyDownRef.current) handleClose();\n                            context.isFocusedToastEscapeKeyDownRef.current = false;\n                        }),\n                        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.li, {\n                            role: \"status\",\n                            \"aria-live\": \"off\",\n                            \"aria-atomic\": true,\n                            tabIndex: 0,\n                            \"data-state\": open ? \"open\" : \"closed\",\n                            \"data-swipe-direction\": context.swipeDirection,\n                            ...toastProps,\n                            ref: composedRefs,\n                            style: {\n                                userSelect: \"none\",\n                                touchAction: \"none\",\n                                ...props.style\n                            },\n                            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                                if (event.key !== \"Escape\") return;\n                                onEscapeKeyDown?.(event.nativeEvent);\n                                if (!event.nativeEvent.defaultPrevented) {\n                                    context.isFocusedToastEscapeKeyDownRef.current = true;\n                                    handleClose();\n                                }\n                            }),\n                            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerDown, (event)=>{\n                                if (event.button !== 0) return;\n                                pointerStartRef.current = {\n                                    x: event.clientX,\n                                    y: event.clientY\n                                };\n                            }),\n                            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerMove, (event)=>{\n                                if (!pointerStartRef.current) return;\n                                const x = event.clientX - pointerStartRef.current.x;\n                                const y = event.clientY - pointerStartRef.current.y;\n                                const hasSwipeMoveStarted = Boolean(swipeDeltaRef.current);\n                                const isHorizontalSwipe = [\n                                    \"left\",\n                                    \"right\"\n                                ].includes(context.swipeDirection);\n                                const clamp = [\n                                    \"left\",\n                                    \"up\"\n                                ].includes(context.swipeDirection) ? Math.min : Math.max;\n                                const clampedX = isHorizontalSwipe ? clamp(0, x) : 0;\n                                const clampedY = !isHorizontalSwipe ? clamp(0, y) : 0;\n                                const moveStartBuffer = event.pointerType === \"touch\" ? 10 : 2;\n                                const delta = {\n                                    x: clampedX,\n                                    y: clampedY\n                                };\n                                const eventDetail = {\n                                    originalEvent: event,\n                                    delta\n                                };\n                                if (hasSwipeMoveStarted) {\n                                    swipeDeltaRef.current = delta;\n                                    handleAndDispatchCustomEvent(TOAST_SWIPE_MOVE, onSwipeMove, eventDetail, {\n                                        discrete: false\n                                    });\n                                } else if (isDeltaInDirection(delta, context.swipeDirection, moveStartBuffer)) {\n                                    swipeDeltaRef.current = delta;\n                                    handleAndDispatchCustomEvent(TOAST_SWIPE_START, onSwipeStart, eventDetail, {\n                                        discrete: false\n                                    });\n                                    event.target.setPointerCapture(event.pointerId);\n                                } else if (Math.abs(x) > moveStartBuffer || Math.abs(y) > moveStartBuffer) {\n                                    pointerStartRef.current = null;\n                                }\n                            }),\n                            onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerUp, (event)=>{\n                                const delta = swipeDeltaRef.current;\n                                const target = event.target;\n                                if (target.hasPointerCapture(event.pointerId)) {\n                                    target.releasePointerCapture(event.pointerId);\n                                }\n                                swipeDeltaRef.current = null;\n                                pointerStartRef.current = null;\n                                if (delta) {\n                                    const toast = event.currentTarget;\n                                    const eventDetail = {\n                                        originalEvent: event,\n                                        delta\n                                    };\n                                    if (isDeltaInDirection(delta, context.swipeDirection, context.swipeThreshold)) {\n                                        handleAndDispatchCustomEvent(TOAST_SWIPE_END, onSwipeEnd, eventDetail, {\n                                            discrete: true\n                                        });\n                                    } else {\n                                        handleAndDispatchCustomEvent(TOAST_SWIPE_CANCEL, onSwipeCancel, eventDetail, {\n                                            discrete: true\n                                        });\n                                    }\n                                    toast.addEventListener(\"click\", (event2)=>event2.preventDefault(), {\n                                        once: true\n                                    });\n                                }\n                            })\n                        })\n                    })\n                }), context.viewport)\n            })\n        ]\n    });\n});\nvar ToastAnnounce = (props)=>{\n    const { __scopeToast, children, ...announceProps } = props;\n    const context = useToastProviderContext(TOAST_NAME, __scopeToast);\n    const [renderAnnounceText, setRenderAnnounceText] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [isAnnounced, setIsAnnounced] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    useNextFrame(()=>setRenderAnnounceText(true));\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const timer = window.setTimeout(()=>setIsAnnounced(true), 1e3);\n        return ()=>window.clearTimeout(timer);\n    }, []);\n    return isAnnounced ? null : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__.Portal, {\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_8__.VisuallyHidden, {\n            ...announceProps,\n            children: renderAnnounceText && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n                children: [\n                    context.label,\n                    \" \",\n                    children\n                ]\n            })\n        })\n    });\n};\nvar TITLE_NAME = \"ToastTitle\";\nvar ToastTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, ...titleProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.div, {\n        ...titleProps,\n        ref: forwardedRef\n    });\n});\nToastTitle.displayName = TITLE_NAME;\nvar DESCRIPTION_NAME = \"ToastDescription\";\nvar ToastDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, ...descriptionProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.div, {\n        ...descriptionProps,\n        ref: forwardedRef\n    });\n});\nToastDescription.displayName = DESCRIPTION_NAME;\nvar ACTION_NAME = \"ToastAction\";\nvar ToastAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { altText, ...actionProps } = props;\n    if (!altText.trim()) {\n        console.error(`Invalid prop \\`altText\\` supplied to \\`${ACTION_NAME}\\`. Expected non-empty \\`string\\`.`);\n        return null;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastAnnounceExclude, {\n        altText,\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastClose, {\n            ...actionProps,\n            ref: forwardedRef\n        })\n    });\n});\nToastAction.displayName = ACTION_NAME;\nvar CLOSE_NAME = \"ToastClose\";\nvar ToastClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, ...closeProps } = props;\n    const interactiveContext = useToastInteractiveContext(CLOSE_NAME, __scopeToast);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastAnnounceExclude, {\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.button, {\n            type: \"button\",\n            ...closeProps,\n            ref: forwardedRef,\n            onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onClick, interactiveContext.onClose)\n        })\n    });\n});\nToastClose.displayName = CLOSE_NAME;\nvar ToastAnnounceExclude = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, altText, ...announceExcludeProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.div, {\n        \"data-radix-toast-announce-exclude\": \"\",\n        \"data-radix-toast-announce-alt\": altText || void 0,\n        ...announceExcludeProps,\n        ref: forwardedRef\n    });\n});\nfunction getAnnounceTextContent(container) {\n    const textContent = [];\n    const childNodes = Array.from(container.childNodes);\n    childNodes.forEach((node)=>{\n        if (node.nodeType === node.TEXT_NODE && node.textContent) textContent.push(node.textContent);\n        if (isHTMLElement(node)) {\n            const isHidden = node.ariaHidden || node.hidden || node.style.display === \"none\";\n            const isExcluded = node.dataset.radixToastAnnounceExclude === \"\";\n            if (!isHidden) {\n                if (isExcluded) {\n                    const altText = node.dataset.radixToastAnnounceAlt;\n                    if (altText) textContent.push(altText);\n                } else {\n                    textContent.push(...getAnnounceTextContent(node));\n                }\n            }\n        }\n    });\n    return textContent;\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n    const currentTarget = detail.originalEvent.currentTarget;\n    const event = new CustomEvent(name, {\n        bubbles: true,\n        cancelable: true,\n        detail\n    });\n    if (handler) currentTarget.addEventListener(name, handler, {\n        once: true\n    });\n    if (discrete) {\n        (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.dispatchDiscreteCustomEvent)(currentTarget, event);\n    } else {\n        currentTarget.dispatchEvent(event);\n    }\n}\nvar isDeltaInDirection = (delta, direction, threshold = 0)=>{\n    const deltaX = Math.abs(delta.x);\n    const deltaY = Math.abs(delta.y);\n    const isDeltaX = deltaX > deltaY;\n    if (direction === \"left\" || direction === \"right\") {\n        return isDeltaX && deltaX > threshold;\n    } else {\n        return !isDeltaX && deltaY > threshold;\n    }\n};\nfunction useNextFrame(callback = ()=>{}) {\n    const fn = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(callback);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_14__.useLayoutEffect)(()=>{\n        let raf1 = 0;\n        let raf2 = 0;\n        raf1 = window.requestAnimationFrame(()=>raf2 = window.requestAnimationFrame(fn));\n        return ()=>{\n            window.cancelAnimationFrame(raf1);\n            window.cancelAnimationFrame(raf2);\n        };\n    }, [\n        fn\n    ]);\n}\nfunction isHTMLElement(node) {\n    return node.nodeType === node.ELEMENT_NODE;\n}\nfunction getTabbableCandidates(container) {\n    const nodes = [];\n    const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n        acceptNode: (node)=>{\n            const isHiddenInput = node.tagName === \"INPUT\" && node.type === \"hidden\";\n            if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n            return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n        }\n    });\n    while(walker.nextNode())nodes.push(walker.currentNode);\n    return nodes;\n}\nfunction focusFirst(candidates) {\n    const previouslyFocusedElement = document.activeElement;\n    return candidates.some((candidate)=>{\n        if (candidate === previouslyFocusedElement) return true;\n        candidate.focus();\n        return document.activeElement !== previouslyFocusedElement;\n    });\n}\nvar Provider = ToastProvider;\nvar Viewport = ToastViewport;\nvar Root2 = Toast;\nvar Title = ToastTitle;\nvar Description = ToastDescription;\nvar Action = ToastAction;\nvar Close = ToastClose;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-toast/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCallbackRef: () => (/* binding */ useCallbackRef)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-callback-ref/src/use-callback-ref.tsx\n\nfunction useCallbackRef(callback) {\n    const callbackRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(callback);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        callbackRef.current = callback;\n    });\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>(...args)=>callbackRef.current?.(...args), []);\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1jYWxsYmFjay1yZWYvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSwyREFBMkQ7QUFDNUI7QUFDL0IsU0FBU0MsZUFBZUMsUUFBUTtJQUM5QixNQUFNQyxjQUFjSCx5Q0FBWSxDQUFDRTtJQUNqQ0YsNENBQWUsQ0FBQztRQUNkRyxZQUFZRyxPQUFPLEdBQUdKO0lBQ3hCO0lBQ0EsT0FBT0YsMENBQWEsQ0FBQyxJQUFNLENBQUMsR0FBR1EsT0FBU0wsWUFBWUcsT0FBTyxNQUFNRSxPQUFPLEVBQUU7QUFDNUU7QUFHRSxDQUNGLGtDQUFrQyIsInNvdXJjZXMiOlsid2VicGFjazovL3F1ZXJ5Y3JhZnQtc3R1ZGlvLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC11c2UtY2FsbGJhY2stcmVmL2Rpc3QvaW5kZXgubWpzPzExZjAiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvdXNlLWNhbGxiYWNrLXJlZi9zcmMvdXNlLWNhbGxiYWNrLXJlZi50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuZnVuY3Rpb24gdXNlQ2FsbGJhY2tSZWYoY2FsbGJhY2spIHtcbiAgY29uc3QgY2FsbGJhY2tSZWYgPSBSZWFjdC51c2VSZWYoY2FsbGJhY2spO1xuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNhbGxiYWNrUmVmLmN1cnJlbnQgPSBjYWxsYmFjaztcbiAgfSk7XG4gIHJldHVybiBSZWFjdC51c2VNZW1vKCgpID0+ICguLi5hcmdzKSA9PiBjYWxsYmFja1JlZi5jdXJyZW50Py4oLi4uYXJncyksIFtdKTtcbn1cbmV4cG9ydCB7XG4gIHVzZUNhbGxiYWNrUmVmXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlQ2FsbGJhY2tSZWYiLCJjYWxsYmFjayIsImNhbGxiYWNrUmVmIiwidXNlUmVmIiwidXNlRWZmZWN0IiwiY3VycmVudCIsInVzZU1lbW8iLCJhcmdzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useControllableState: () => (/* binding */ useControllableState),\n/* harmony export */   useControllableStateReducer: () => (/* binding */ useControllableStateReducer)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_effect_event__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-effect-event */ \"(ssr)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs\");\n// src/use-controllable-state.tsx\n\n\nvar useInsertionEffect = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useInsertionEffect \".trim().toString()] || _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect;\nfunction useControllableState({ prop, defaultProp, onChange = ()=>{}, caller }) {\n    const [uncontrolledProp, setUncontrolledProp, onChangeRef] = useUncontrolledState({\n        defaultProp,\n        onChange\n    });\n    const isControlled = prop !== void 0;\n    const value = isControlled ? prop : uncontrolledProp;\n    if (true) {\n        const isControlledRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(prop !== void 0);\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            const wasControlled = isControlledRef.current;\n            if (wasControlled !== isControlled) {\n                const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n                const to = isControlled ? \"controlled\" : \"uncontrolled\";\n                console.warn(`${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`);\n            }\n            isControlledRef.current = isControlled;\n        }, [\n            isControlled,\n            caller\n        ]);\n    }\n    const setValue = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((nextValue)=>{\n        if (isControlled) {\n            const value2 = isFunction(nextValue) ? nextValue(prop) : nextValue;\n            if (value2 !== prop) {\n                onChangeRef.current?.(value2);\n            }\n        } else {\n            setUncontrolledProp(nextValue);\n        }\n    }, [\n        isControlled,\n        prop,\n        setUncontrolledProp,\n        onChangeRef\n    ]);\n    return [\n        value,\n        setValue\n    ];\n}\nfunction useUncontrolledState({ defaultProp, onChange }) {\n    const [value, setValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(defaultProp);\n    const prevValueRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(value);\n    const onChangeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(onChange);\n    useInsertionEffect(()=>{\n        onChangeRef.current = onChange;\n    }, [\n        onChange\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (prevValueRef.current !== value) {\n            onChangeRef.current?.(value);\n            prevValueRef.current = value;\n        }\n    }, [\n        value,\n        prevValueRef\n    ]);\n    return [\n        value,\n        setValue,\n        onChangeRef\n    ];\n}\nfunction isFunction(value) {\n    return typeof value === \"function\";\n}\n// src/use-controllable-state-reducer.tsx\n\n\nvar SYNC_STATE = Symbol(\"RADIX:SYNC_STATE\");\nfunction useControllableStateReducer(reducer, userArgs, initialArg, init) {\n    const { prop: controlledState, defaultProp, onChange: onChangeProp, caller } = userArgs;\n    const isControlled = controlledState !== void 0;\n    const onChange = (0,_radix_ui_react_use_effect_event__WEBPACK_IMPORTED_MODULE_2__.useEffectEvent)(onChangeProp);\n    if (true) {\n        const isControlledRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(controlledState !== void 0);\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            const wasControlled = isControlledRef.current;\n            if (wasControlled !== isControlled) {\n                const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n                const to = isControlled ? \"controlled\" : \"uncontrolled\";\n                console.warn(`${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`);\n            }\n            isControlledRef.current = isControlled;\n        }, [\n            isControlled,\n            caller\n        ]);\n    }\n    const args = [\n        {\n            ...initialArg,\n            state: defaultProp\n        }\n    ];\n    if (init) {\n        args.push(init);\n    }\n    const [internalState, dispatch] = react__WEBPACK_IMPORTED_MODULE_0__.useReducer((state2, action)=>{\n        if (action.type === SYNC_STATE) {\n            return {\n                ...state2,\n                state: action.state\n            };\n        }\n        const next = reducer(state2, action);\n        if (isControlled && !Object.is(next.state, state2.state)) {\n            onChange(next.state);\n        }\n        return next;\n    }, ...args);\n    const uncontrolledState = internalState.state;\n    const prevValueRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(uncontrolledState);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (prevValueRef.current !== uncontrolledState) {\n            prevValueRef.current = uncontrolledState;\n            if (!isControlled) {\n                onChange(uncontrolledState);\n            }\n        }\n    }, [\n        onChange,\n        uncontrolledState,\n        prevValueRef,\n        isControlled\n    ]);\n    const state = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        const isControlled2 = controlledState !== void 0;\n        if (isControlled2) {\n            return {\n                ...internalState,\n                state: controlledState\n            };\n        }\n        return internalState;\n    }, [\n        internalState,\n        controlledState\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (isControlled && !Object.is(controlledState, internalState.state)) {\n            dispatch({\n                type: SYNC_STATE,\n                state: controlledState\n            });\n        }\n    }, [\n        controlledState,\n        internalState.state,\n        isControlled\n    ]);\n    return [\n        state,\n        dispatch\n    ];\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEffectEvent: () => (/* binding */ useEffectEvent)\n/* harmony export */ });\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// src/use-effect-event.tsx\n\n\nvar useReactEffectEvent = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useEffectEvent \".trim().toString()];\nvar useReactInsertionEffect = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useInsertionEffect \".trim().toString()];\nfunction useEffectEvent(callback) {\n    if (typeof useReactEffectEvent === \"function\") {\n        return useReactEffectEvent(callback);\n    }\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(()=>{\n        throw new Error(\"Cannot call an event handler while rendering.\");\n    });\n    if (typeof useReactInsertionEffect === \"function\") {\n        useReactInsertionEffect(()=>{\n            ref.current = callback;\n        });\n    } else {\n        (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(()=>{\n            ref.current = callback;\n        });\n    }\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>(...args)=>ref.current?.(...args), []);\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEscapeKeydown: () => (/* binding */ useEscapeKeydown)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n// packages/react/use-escape-keydown/src/use-escape-keydown.tsx\n\n\nfunction useEscapeKeydown(onEscapeKeyDownProp, ownerDocument = globalThis?.document) {\n    const onEscapeKeyDown = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__.useCallbackRef)(onEscapeKeyDownProp);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleKeyDown = (event)=>{\n            if (event.key === \"Escape\") {\n                onEscapeKeyDown(event);\n            }\n        };\n        ownerDocument.addEventListener(\"keydown\", handleKeyDown, {\n            capture: true\n        });\n        return ()=>ownerDocument.removeEventListener(\"keydown\", handleKeyDown, {\n                capture: true\n            });\n    }, [\n        onEscapeKeyDown,\n        ownerDocument\n    ]);\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLayoutEffect: () => (/* binding */ useLayoutEffect2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-layout-effect/src/use-layout-effect.tsx\n\nvar useLayoutEffect2 = globalThis?.document ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : ()=>{};\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0L2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUEsNkRBQTZEO0FBQzlCO0FBQy9CLElBQUlDLG1CQUFtQkMsWUFBWUMsV0FBV0gsa0RBQXFCLEdBQUcsS0FDdEU7QUFHRSxDQUNGLGtDQUFrQyIsInNvdXJjZXMiOlsid2VicGFjazovL3F1ZXJ5Y3JhZnQtc3R1ZGlvLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC11c2UtbGF5b3V0LWVmZmVjdC9kaXN0L2luZGV4Lm1qcz8yZDZmIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL3JlYWN0L3VzZS1sYXlvdXQtZWZmZWN0L3NyYy91c2UtbGF5b3V0LWVmZmVjdC50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xudmFyIHVzZUxheW91dEVmZmVjdDIgPSBnbG9iYWxUaGlzPy5kb2N1bWVudCA/IFJlYWN0LnVzZUxheW91dEVmZmVjdCA6ICgpID0+IHtcbn07XG5leHBvcnQge1xuICB1c2VMYXlvdXRFZmZlY3QyIGFzIHVzZUxheW91dEVmZmVjdFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZUxheW91dEVmZmVjdDIiLCJnbG9iYWxUaGlzIiwiZG9jdW1lbnQiLCJ1c2VMYXlvdXRFZmZlY3QiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   VISUALLY_HIDDEN_STYLES: () => (/* binding */ VISUALLY_HIDDEN_STYLES),\n/* harmony export */   VisuallyHidden: () => (/* binding */ VisuallyHidden)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/visually-hidden.tsx\n\n\n\nvar VISUALLY_HIDDEN_STYLES = Object.freeze({\n    // See: https://github.com/twbs/bootstrap/blob/main/scss/mixins/_visually-hidden.scss\n    position: \"absolute\",\n    border: 0,\n    width: 1,\n    height: 1,\n    padding: 0,\n    margin: -1,\n    overflow: \"hidden\",\n    clip: \"rect(0, 0, 0, 0)\",\n    whiteSpace: \"nowrap\",\n    wordWrap: \"normal\"\n});\nvar NAME = \"VisuallyHidden\";\nvar VisuallyHidden = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.span, {\n        ...props,\n        ref: forwardedRef,\n        style: {\n            ...VISUALLY_HIDDEN_STYLES,\n            ...props.style\n        }\n    });\n});\nVisuallyHidden.displayName = NAME;\nvar Root = VisuallyHidden;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\n");

/***/ })

};
;