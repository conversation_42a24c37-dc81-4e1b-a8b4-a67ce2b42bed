'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  SendIcon,
  BrainCircuitIcon,
  UserIcon,
  CopyIcon,
  ThumbsUpIcon,
  ThumbsDownIcon,
  SparklesIcon
} from 'lucide-react';
import { formatRelativeTime } from '@/lib/utils';
import { useQueryStore } from '@/stores/query-store';

interface ChatInterfaceProps {
  onQueryGenerated: (query: string) => void;
}

export function ChatInterface({ onQueryGenerated }: ChatInterfaceProps) {
  const {
    messages,
    isAILoading,
    generateQuery,
    setActiveTab,
    saveQueryToDatabase
  } = useQueryStore();
  const [inputValue, setInputValue] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isAILoading) return;

    const input = inputValue;
    setInputValue('');

    await generateQuery(input);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleCopyQuery = (sql: string) => {
    navigator.clipboard.writeText(sql);
  };

  const handleUseQuery = async (sql: string, userInput?: string, explanation?: string) => {
    onQueryGenerated(sql);
    setActiveTab('editor');

    // Save to database if we have the required data
    if (userInput && explanation) {
      try {
        await saveQueryToDatabase(userInput, sql, explanation);
      } catch (error) {
        console.error('Failed to save query to database:', error);
      }
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-6 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex gap-3 ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            {message.role === 'assistant' && (
              <div className="flex-shrink-0">
                <div className="h-8 w-8 bg-primary rounded-full flex items-center justify-center">
                  <BrainCircuitIcon className="h-4 w-4 text-primary-foreground" />
                </div>
              </div>
            )}
            
            <div className={`max-w-2xl ${message.role === 'user' ? 'order-first' : ''}`}>
              <Card className={`p-4 ${message.role === 'user' ? 'bg-primary text-primary-foreground' : ''}`}>
                <div className="space-y-3">
                  <div className="text-sm">{message.content}</div>
                  
                  {message.type === 'query' && message.metadata?.sql && (
                    <div className="space-y-3">
                      <div className="bg-muted rounded-lg p-3">
                        <div className="flex items-center justify-between mb-2">
                          <Badge variant="secondary" className="text-xs">
                            <SparklesIcon className="h-3 w-3 mr-1" />
                            Generated SQL
                          </Badge>
                          <div className="flex gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleCopyQuery(message.metadata!.sql!)}
                            >
                              <CopyIcon className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                        <pre className="text-xs font-mono overflow-x-auto">
                          <code>{message.metadata.sql}</code>
                        </pre>
                      </div>
                      
                      {message.metadata.explanation && (
                        <div className="text-sm text-muted-foreground">
                          <strong>Explanation:</strong> {message.metadata.explanation}
                        </div>
                      )}
                      
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          onClick={() => handleUseQuery(
                            message.metadata!.sql!,
                            message.metadata?.userInput,
                            message.metadata?.explanation
                          )}
                        >
                          Use in Editor
                        </Button>
                        <Button variant="outline" size="sm">
                          <ThumbsUpIcon className="h-3 w-3 mr-1" />
                          Good
                        </Button>
                        <Button variant="outline" size="sm">
                          <ThumbsDownIcon className="h-3 w-3 mr-1" />
                          Improve
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
                
                <div className="text-xs text-muted-foreground mt-2">
                  {formatRelativeTime(message.timestamp)}
                </div>
              </Card>
            </div>
            
            {message.role === 'user' && (
              <div className="flex-shrink-0">
                <div className="h-8 w-8 bg-muted rounded-full flex items-center justify-center">
                  <UserIcon className="h-4 w-4 text-muted-foreground" />
                </div>
              </div>
            )}
          </div>
        ))}
        
        {isAILoading && (
          <div className="flex gap-3">
            <div className="flex-shrink-0">
              <div className="h-8 w-8 bg-primary rounded-full flex items-center justify-center">
                <BrainCircuitIcon className="h-4 w-4 text-primary-foreground animate-pulse" />
              </div>
            </div>
            <Card className="p-4">
              <div className="flex items-center gap-2">
                <div className="loading-spinner" />
                <span className="text-sm text-muted-foreground">AI is thinking...</span>
              </div>
            </Card>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="border-t border-border p-4">
        <div className="flex gap-3">
          <div className="flex-1">
            <textarea
              ref={textareaRef}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Describe the data you want to query... (e.g., 'Show me the top 10 customers by revenue this month')"
              className="querycraft-textarea resize-none"
              rows={3}
              disabled={isAILoading}
            />
          </div>
          <Button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isAILoading}
            size="lg"
          >
            <SendIcon className="h-4 w-4" />
          </Button>
        </div>
        <div className="text-xs text-muted-foreground mt-2">
          Press Enter to send, Shift+Enter for new line
        </div>
      </div>
    </div>
  );
}
