'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ChatInterface } from '@/components/chat/chat-interface';
import { SQLEditor } from '@/components/editor/sql-editor';
import { SchemaExplorer } from '@/components/dashboard/schema-explorer';
import { QueryResults } from '@/components/dashboard/query-results';
import { QueryHistory } from '@/components/dashboard/query-history';
import { useQueryStore } from '@/stores/query-store';
import {
  PlayIcon,
  SaveIcon,
  ShareIcon,
  HistoryIcon,
  DatabaseIcon,
  BrainCircuitIcon,
  ZapIcon,
  TrendingUpIcon
} from 'lucide-react';

export function QueryWorkspace() {
  const {
    activeTab,
    setActiveTab,
    currentQuery,
    setCurrentQuery,
    currentDatabase
  } = useQueryStore();
  const [isConnected, setIsConnected] = useState(false);

  const tabs = [
    { id: 'chat', label: 'AI Assistant', icon: BrainCircuitIcon },
    { id: 'editor', label: 'SQL Editor', icon: DatabaseIcon },
    { id: 'results', label: 'Results', icon: TrendingUpIcon },
    { id: 'history', label: 'History', icon: HistoryIcon },
  ];

  return (
    <div className="flex h-full">
      {/* Left Panel - Schema Explorer */}
      <div className="w-80 border-r border-border bg-card">
        <div className="p-4 border-b border-border">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-semibold">Database Schema</h3>
            <Badge variant={isConnected ? 'default' : 'secondary'}>
              {isConnected ? 'Connected' : 'Not Connected'}
            </Badge>
          </div>
          {!isConnected && (
            <Button 
              variant="outline" 
              size="sm" 
              className="w-full"
              onClick={() => setIsConnected(true)}
            >
              <DatabaseIcon className="h-4 w-4 mr-2" />
              Connect Database
            </Button>
          )}
        </div>
        <div className="flex-1 overflow-auto">
          <SchemaExplorer isConnected={isConnected} />
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {/* Tab Navigation */}
        <div className="border-b border-border bg-card">
          <div className="flex items-center justify-between px-6 py-3">
            <div className="flex space-x-1">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                      activeTab === tab.id
                        ? 'bg-primary text-primary-foreground'
                        : 'text-muted-foreground hover:text-foreground hover:bg-muted'
                    }`}
                  >
                    <Icon className="h-4 w-4" />
                    {tab.label}
                  </button>
                );
              })}
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <HistoryIcon className="h-4 w-4 mr-2" />
                History
              </Button>
              <Button variant="outline" size="sm">
                <SaveIcon className="h-4 w-4 mr-2" />
                Save
              </Button>
              <Button variant="outline" size="sm">
                <ShareIcon className="h-4 w-4 mr-2" />
                Share
              </Button>
              {activeTab === 'editor' && (
                <Button size="sm">
                  <PlayIcon className="h-4 w-4 mr-2" />
                  Run Query
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Content Area */}
        <div className="flex-1 overflow-hidden">
          {activeTab === 'chat' && (
            <ChatInterface
              onQueryGenerated={(query) => {
                setCurrentQuery(query);
              }}
            />
          )}

          {activeTab === 'editor' && (
            <div className="h-full flex flex-col">
              <div className="flex-1 p-6">
                <SQLEditor
                  value={currentQuery}
                  onChange={setCurrentQuery}
                  language={currentDatabase?.databaseType || 'postgresql'}
                />
              </div>
            </div>
          )}
          
          {activeTab === 'results' && (
            <div className="h-full p-6">
              <QueryResults />
            </div>
          )}

          {activeTab === 'history' && (
            <div className="h-full p-6">
              <QueryHistory />
            </div>
          )}
        </div>
      </div>

      {/* Right Panel - AI Insights */}
      <div className="w-80 border-l border-border bg-card">
        <div className="p-4 border-b border-border">
          <h3 className="font-semibold flex items-center gap-2">
            <ZapIcon className="h-4 w-4" />
            AI Insights
          </h3>
        </div>
        <div className="p-4 space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Query Suggestions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="text-sm text-muted-foreground">
                No active query to analyze. Start by describing what you want to query in the AI Assistant.
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Performance Tips</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="text-sm text-muted-foreground">
                Connect to a database to get performance recommendations.
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Recent Activity</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="text-sm text-muted-foreground">
                No recent queries found.
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
