'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  HistoryIcon, 
  ClockIcon, 
  DatabaseIcon,
  PlayIcon,
  CopyIcon,
  ThumbsUpIcon,
  ThumbsDownIcon
} from 'lucide-react';
import { useQueryStore } from '@/stores/query-store';
import { formatRelativeTime } from '@/lib/utils';

interface QueryHistoryProps {
  userId?: string;
}

export function QueryHistory({ userId }: QueryHistoryProps) {
  const [queries, setQueries] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const { setCurrentQuery, setActiveTab, currentUser } = useQueryStore();

  useEffect(() => {
    const loadQueryHistory = async () => {
      if (!userId && !currentUser) return;
      
      const userIdToUse = userId || currentUser?.id;
      if (!userIdToUse) return;

      try {
        setLoading(true);
        const response = await fetch(`/api/queries?type=history&userId=${userIdToUse}&limit=50`);
        if (response.ok) {
          const data = await response.json();
          setQueries(data);
        }
      } catch (error) {
        console.error('Failed to load query history:', error);
      } finally {
        setLoading(false);
      }
    };

    loadQueryHistory();
  }, [userId, currentUser]);

  const handleUseQuery = (sql: string) => {
    setCurrentQuery(sql);
    setActiveTab('editor');
  };

  const handleCopyQuery = (sql: string) => {
    navigator.clipboard.writeText(sql);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'EXECUTED':
        return 'bg-green-100 text-green-800';
      case 'FAILED':
        return 'bg-red-100 text-red-800';
      case 'GENERATED':
        return 'bg-blue-100 text-blue-800';
      case 'OPTIMIZED':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <HistoryIcon className="h-5 w-5" />
            Query History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="loading-spinner" />
            <span className="ml-2 text-sm text-muted-foreground">Loading history...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <HistoryIcon className="h-5 w-5" />
          Query History
          <Badge variant="secondary" className="ml-auto">
            {queries.length} queries
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[600px]">
          {queries.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <HistoryIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No queries in history yet</p>
              <p className="text-sm">Start generating queries to see them here</p>
            </div>
          ) : (
            <div className="space-y-4">
              {queries.map((query) => (
                <Card key={query.id} className="border-l-4 border-l-primary/20">
                  <CardContent className="p-4">
                    <div className="space-y-3">
                      {/* Header */}
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <p className="text-sm font-medium text-foreground">
                            {query.userInput}
                          </p>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge className={getStatusColor(query.status)}>
                              {query.status}
                            </Badge>
                            <div className="flex items-center gap-1 text-xs text-muted-foreground">
                              <DatabaseIcon className="h-3 w-3" />
                              {query.databaseType}
                            </div>
                            <div className="flex items-center gap-1 text-xs text-muted-foreground">
                              <ClockIcon className="h-3 w-3" />
                              {formatRelativeTime(new Date(query.createdAt))}
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* SQL Query */}
                      <div className="bg-muted rounded-lg p-3">
                        <pre className="text-xs font-mono overflow-x-auto whitespace-pre-wrap">
                          <code>{query.generatedSQL}</code>
                        </pre>
                      </div>

                      {/* Explanation */}
                      {query.explanation && (
                        <div className="text-sm text-muted-foreground">
                          <strong>Explanation:</strong> {query.explanation}
                        </div>
                      )}

                      {/* Performance Data */}
                      {query.executionTime && (
                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                          <span>Execution: {query.executionTime}ms</span>
                          {query.rowsAffected && (
                            <span>Rows: {query.rowsAffected}</span>
                          )}
                          {query.userFeedback && (
                            <div className="flex items-center gap-1">
                              <span>Rating:</span>
                              <div className="flex">
                                {[...Array(5)].map((_, i) => (
                                  <ThumbsUpIcon
                                    key={i}
                                    className={`h-3 w-3 ${
                                      i < query.userFeedback
                                        ? 'text-yellow-500 fill-current'
                                        : 'text-gray-300'
                                    }`}
                                  />
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      )}

                      {/* Actions */}
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleUseQuery(query.generatedSQL)}
                        >
                          <PlayIcon className="h-3 w-3 mr-1" />
                          Use
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleCopyQuery(query.generatedSQL)}
                        >
                          <CopyIcon className="h-3 w-3 mr-1" />
                          Copy
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
