'use client';

import { useEffect } from 'react';
import { useQueryStore } from '@/stores/query-store';

interface DatabaseProviderProps {
  children: React.ReactNode;
}

export function DatabaseProvider({ children }: DatabaseProviderProps) {
  const { 
    setCurrentUser, 
    loadUserConnections, 
    loadUserSessions,
    createNewSession,
    currentUser,
    connections,
    currentSession 
  } = useQueryStore();

  useEffect(() => {
    // Initialize with demo user
    const initializeDemoUser = async () => {
      if (!currentUser) {
        const demoUser = {
          id: 'demo-user-id',
          name: 'Demo User',
          email: '<EMAIL>'
        };
        
        setCurrentUser(demoUser);
        
        // Load user data
        try {
          await loadUserConnections(demoUser.id);
          await loadUserSessions(demoUser.id);
          
          // Create a session if none exists and we have connections
          const state = useQueryStore.getState();
          if (!state.currentSession && state.connections.length > 0) {
            await createNewSession(demoUser.id, state.connections[0].id);
          }
        } catch (error) {
          console.error('Failed to initialize demo user:', error);
        }
      }
    };

    initializeDemoUser();
  }, [currentUser, setCurrentUser, loadUserConnections, loadUserSessions, createNewSession]);

  return <>{children}</>;
}
