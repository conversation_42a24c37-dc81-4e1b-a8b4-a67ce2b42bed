'use client';

import { useEffect } from 'react';
import { useQueryStore } from '@/stores/query-store';

interface DatabaseProviderProps {
  children: React.ReactNode;
}

export function DatabaseProvider({ children }: DatabaseProviderProps) {
  const { 
    setCurrentUser, 
    loadUserConnections, 
    loadUserSessions,
    createNewSession,
    currentUser,
    connections,
    currentSession 
  } = useQueryStore();

  useEffect(() => {
    // Initialize with demo user from database
    const initializeDemoUser = async () => {
      if (!currentUser) {
        try {
          // Fetch the demo user from the database
          const response = await fetch('/api/users?email=<EMAIL>');
          if (response.ok) {
            const demoUser = await response.json();

            setCurrentUser({
              id: demoUser.id,
              name: demoUser.name,
              email: demoUser.email
            });

            // Load user data
            await loadUserConnections(demoUser.id);
            await loadUserSessions(demoUser.id);

            // Set the first connection as current if available
            const state = useQueryStore.getState();
            if (state.connections.length > 0) {
              useQueryStore.getState().setCurrentDatabase(state.connections[0]);
            }
          } else {
            console.error('Demo user not found in database');
          }
        } catch (error) {
          console.error('Failed to initialize demo user:', error);
        }
      }
    };

    initializeDemoUser();
  }, [currentUser, setCurrentUser, loadUserConnections, loadUserSessions]);

  return <>{children}</>;
}
