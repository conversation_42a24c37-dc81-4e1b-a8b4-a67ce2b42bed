import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { ChatMessage, DatabaseConnection, GeneratedQuery, QuerySession } from '@/types';
import { QueryService } from '@/lib/database/services/query-service';
import { ConnectionService } from '@/lib/database/services/connection-service';

interface QueryState {
  // Current session
  currentSession: QuerySession | null;
  currentQuery: string;
  currentDatabase: DatabaseConnection | null;

  // Chat state
  messages: ChatMessage[];
  isAILoading: boolean;

  // Query history
  queryHistory: GeneratedQuery[];

  // Database connections
  connections: DatabaseConnection[];

  // UI state
  activeTab: 'chat' | 'editor' | 'results' | 'history';
  sidebarOpen: boolean;

  // User state
  currentUser: { id: string; name: string; email: string } | null;
  
  // Actions
  setCurrentQuery: (query: string) => void;
  setCurrentDatabase: (database: DatabaseConnection | null) => void;
  addMessage: (message: ChatMessage) => void;
  setAILoading: (loading: boolean) => void;
  addToHistory: (query: GeneratedQuery) => void;
  setActiveTab: (tab: 'chat' | 'editor' | 'results' | 'history') => void;
  setSidebarOpen: (open: boolean) => void;
  addConnection: (connection: DatabaseConnection) => void;
  removeConnection: (id: string) => void;
  clearMessages: () => void;
  
  // AI Actions
  generateQuery: (userInput: string) => Promise<void>;
  optimizeQuery: (query: string) => Promise<string>;
  explainQuery: (query: string) => Promise<string>;

  // Database integration
  setCurrentUser: (user: { id: string; name: string; email: string }) => void;
  loadUserSessions: (userId: string) => Promise<void>;
  loadUserConnections: (userId: string) => Promise<void>;
  createNewSession: (userId: string, connectionId: string) => Promise<void>;
  saveQueryToDatabase: (userInput: string, sql: string, explanation: string) => Promise<void>;
}

export const useQueryStore = create<QueryState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        currentSession: null,
        currentQuery: '',
        currentDatabase: null,
        messages: [
          {
            id: '1',
            role: 'assistant',
            content: 'Hello! I\'m your AI SQL assistant. I can help you write, debug, and optimize SQL queries. What would you like to work on today?',
            timestamp: new Date(),
            type: 'text'
          }
        ],
        isAILoading: false,
        queryHistory: [],
        connections: [],
        activeTab: 'chat',
        sidebarOpen: true,
        currentUser: null,

        // Actions
        setCurrentQuery: (query) => set({ currentQuery: query }),
        
        setCurrentDatabase: (database) => set({ currentDatabase: database }),
        
        addMessage: (message) => set((state) => ({
          messages: [...state.messages, message]
        })),
        
        setAILoading: (loading) => set({ isAILoading: loading }),
        
        addToHistory: (query) => set((state) => ({
          queryHistory: [query, ...state.queryHistory.slice(0, 49)] // Keep last 50
        })),
        
        setActiveTab: (tab) => set({ activeTab: tab }),
        
        setSidebarOpen: (open) => set({ sidebarOpen: open }),
        
        addConnection: (connection) => set((state) => ({
          connections: [...state.connections, connection]
        })),
        
        removeConnection: (id) => set((state) => ({
          connections: state.connections.filter(conn => conn.id !== id)
        })),
        
        clearMessages: () => set({
          messages: [
            {
              id: '1',
              role: 'assistant',
              content: 'Hello! I\'m your AI SQL assistant. I can help you write, debug, and optimize SQL queries. What would you like to work on today?',
              timestamp: new Date(),
              type: 'text'
            }
          ]
        }),

        // AI Actions
        generateQuery: async (userInput: string) => {
          const { addMessage, setAILoading, setCurrentQuery, setActiveTab, currentDatabase } = get();
          
          // Add user message
          const userMessage: ChatMessage = {
            id: Date.now().toString(),
            role: 'user',
            content: userInput,
            timestamp: new Date(),
            type: 'text'
          };
          addMessage(userMessage);
          setAILoading(true);

          try {
            // Simulate AI processing
            await new Promise(resolve => setTimeout(resolve, 1500));
            
            // Generate mock SQL based on user input
            const mockSQL = generateMockSQL(userInput, currentDatabase?.databaseType || 'postgresql');
            
            const assistantMessage: ChatMessage = {
              id: (Date.now() + 1).toString(),
              role: 'assistant',
              content: 'I\'ve generated a SQL query based on your request. Here\'s what I came up with:',
              timestamp: new Date(),
              type: 'query',
              metadata: {
                sql: mockSQL,
                explanation: generateExplanation(userInput),
                userInput: userInput,
                suggestions: [
                  'Consider adding appropriate indexes for better performance',
                  'Add error handling for edge cases',
                  'Test with sample data before running on production'
                ]
              }
            };
            
            addMessage(assistantMessage);
            setCurrentQuery(mockSQL);
            
            // Add to history
            const historyEntry: GeneratedQuery = {
              id: Date.now().toString(),
              sessionId: 'current',
              userInput,
              generatedSQL: mockSQL,
              explanation: generateExplanation(userInput),
              createdAt: new Date()
            };
            
            get().addToHistory(historyEntry);
            
          } catch (error) {
            const errorMessage: ChatMessage = {
              id: (Date.now() + 1).toString(),
              role: 'assistant',
              content: 'I apologize, but I encountered an error while generating your query. Please try again or rephrase your request.',
              timestamp: new Date(),
              type: 'text'
            };
            addMessage(errorMessage);
          } finally {
            setAILoading(false);
          }
        },

        optimizeQuery: async (query: string) => {
          // Mock optimization
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          const optimizedQuery = `-- Optimized version
${query}
-- Added index hints and optimizations`;
          
          return optimizedQuery;
        },

        explainQuery: async (query: string) => {
          // Mock explanation
          await new Promise(resolve => setTimeout(resolve, 800));

          return `This query performs the following operations:
1. Selects data from the specified tables
2. Applies filtering conditions
3. Groups and aggregates results
4. Orders the output for better readability`;
        },

        // Database integration actions
        setCurrentUser: (user) => set({ currentUser: user }),

        loadUserSessions: async (userId: string) => {
          try {
            const response = await fetch(`/api/queries?type=sessions&userId=${userId}`);
            if (response.ok) {
              const sessions = await response.json();
              // Update state with sessions if needed
              console.log('Loaded sessions:', sessions);
            }
          } catch (error) {
            console.error('Failed to load user sessions:', error);
          }
        },

        loadUserConnections: async (userId: string) => {
          try {
            const response = await fetch(`/api/connections?userId=${userId}`);
            if (response.ok) {
              const connections = await response.json();
              set({ connections });
            }
          } catch (error) {
            console.error('Failed to load user connections:', error);
          }
        },

        createNewSession: async (userId: string, connectionId: string) => {
          try {
            const response = await fetch('/api/queries?type=session', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                userId,
                databaseConnectionId: connectionId,
                name: `Session ${new Date().toLocaleString()}`,
              }),
            });

            if (response.ok) {
              const session = await response.json();
              set({ currentSession: session });
              console.log('Created new session:', session);
            }
          } catch (error) {
            console.error('Failed to create new session:', error);
          }
        },

        saveQueryToDatabase: async (userInput: string, sql: string, explanation: string) => {
          const { currentSession, currentUser, currentDatabase } = get();

          if (!currentSession || !currentUser || !currentDatabase) {
            console.warn('Cannot save query: missing session, user, or database');
            return;
          }

          try {
            const response = await fetch('/api/queries?type=query', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                sessionId: currentSession.id,
                userId: currentUser.id,
                userInput,
                generatedSQL: sql,
                explanation,
                databaseType: currentDatabase.databaseType,
              }),
            });

            if (response.ok) {
              const query = await response.json();
              console.log('Saved query to database:', query);
            }
          } catch (error) {
            console.error('Failed to save query to database:', error);
          }
        }
      }),
      {
        name: 'querycraft-store',
        partialize: (state) => ({
          queryHistory: state.queryHistory,
          connections: state.connections,
          sidebarOpen: state.sidebarOpen,
          currentDatabase: state.currentDatabase
        })
      }
    ),
    {
      name: 'querycraft-store'
    }
  )
);

// Helper functions
function generateMockSQL(userInput: string, dbType: string): string {
  const input = userInput.toLowerCase();
  
  if (input.includes('customer') && input.includes('revenue')) {
    return `SELECT 
  c.customer_id,
  c.name,
  c.email,
  COUNT(o.order_id) as total_orders,
  SUM(o.total_amount) as total_revenue
FROM customers c
LEFT JOIN orders o ON c.customer_id = o.customer_id
WHERE o.order_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY c.customer_id, c.name, c.email
ORDER BY total_revenue DESC
LIMIT 10;`;
  }
  
  if (input.includes('product') && input.includes('sales')) {
    return `SELECT 
  p.product_id,
  p.name as product_name,
  COUNT(oi.item_id) as items_sold,
  SUM(oi.quantity * oi.unit_price) as total_sales
FROM products p
JOIN order_items oi ON p.product_id = oi.product_id
JOIN orders o ON oi.order_id = o.order_id
WHERE o.order_date >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY p.product_id, p.name
ORDER BY total_sales DESC;`;
  }
  
  if (input.includes('order') && input.includes('status')) {
    return `SELECT 
  status,
  COUNT(*) as order_count,
  AVG(total_amount) as avg_order_value
FROM orders
WHERE order_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY status
ORDER BY order_count DESC;`;
  }
  
  // Default query
  return `SELECT *
FROM your_table
WHERE condition = 'value'
ORDER BY created_at DESC
LIMIT 10;`;
}

function generateExplanation(userInput: string): string {
  const input = userInput.toLowerCase();
  
  if (input.includes('customer') && input.includes('revenue')) {
    return 'This query retrieves the top customers by revenue in the last 30 days, including their contact information and order statistics.';
  }
  
  if (input.includes('product') && input.includes('sales')) {
    return 'This query analyzes product sales performance over the last 7 days, showing which products are selling best.';
  }
  
  if (input.includes('order') && input.includes('status')) {
    return 'This query provides an overview of order statuses and their distribution over the last 30 days.';
  }
  
  return 'This query retrieves data based on your specified criteria with appropriate filtering and sorting.';
}
