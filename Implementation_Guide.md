# QueryCraft Studio - Implementation Guide

## Overview

This guide provides detailed technical implementation recommendations for building QueryCraft Studio, including specific technology choices, architecture patterns, and best practices for each component of the system.

## Technology Stack Implementation

### Frontend Implementation

#### Framework: Next.js 14 with TypeScript
```bash
# Project initialization
npx create-next-app@latest querycraft-studio --typescript --tailwind --eslint --app

# Key dependencies
npm install @monaco-editor/react
npm install @tanstack/react-query
npm install zustand
npm install @headlessui/react
npm install framer-motion
npm install d3
npm install @types/d3
```

#### Project Structure
```
src/
├── app/                    # Next.js 13+ app directory
│   ├── (auth)/            # Authentication routes
│   ├── dashboard/         # Main application
│   ├── api/              # API routes
│   └── globals.css       # Global styles
├── components/
│   ├── ui/               # Reusable UI components
│   ├── editor/           # SQL editor components
│   ├── chat/             # Chat interface
│   └── visualization/    # Query visualization
├── lib/
│   ├── api/              # API client functions
│   ├── stores/           # Zustand stores
│   ├── utils/            # Utility functions
│   └── types/            # TypeScript definitions
└── hooks/                # Custom React hooks
```

#### Key Components Implementation

**SQL Editor Component**:
```typescript
import { Editor } from '@monaco-editor/react';
import { useCallback, useRef } from 'react';

interface SQLEditorProps {
  value: string;
  onChange: (value: string) => void;
  language: 'mysql' | 'postgresql';
  readOnly?: boolean;
}

export function SQLEditor({ value, onChange, language, readOnly = false }: SQLEditorProps) {
  const editorRef = useRef(null);

  const handleEditorDidMount = useCallback((editor: any, monaco: any) => {
    editorRef.current = editor;
    
    // Configure SQL language features
    monaco.languages.registerCompletionItemProvider(language, {
      provideCompletionItems: (model: any, position: any) => {
        // Custom autocomplete logic
        return { suggestions: [] };
      }
    });
  }, [language]);

  return (
    <Editor
      height="400px"
      language={language}
      value={value}
      onChange={(value) => onChange(value || '')}
      onMount={handleEditorDidMount}
      options={{
        readOnly,
        minimap: { enabled: false },
        fontSize: 14,
        lineNumbers: 'on',
        roundedSelection: false,
        scrollBeyondLastLine: false,
        automaticLayout: true,
      }}
    />
  );
}
```

### Backend Implementation

#### Framework: Node.js with Fastify
```bash
# Project initialization
npm init -y
npm install fastify @fastify/cors @fastify/jwt @fastify/websocket
npm install prisma @prisma/client
npm install ioredis
npm install openai
npm install zod
npm install @types/node typescript ts-node
```

#### Project Structure
```
src/
├── agents/               # AI agent implementations
│   ├── orchestrator/
│   ├── nlp/
│   ├── sql-generation/
│   ├── schema-intelligence/
│   ├── debugging/
│   ├── optimization/
│   └── explanation/
├── api/                  # API route handlers
├── lib/
│   ├── database/         # Database connections
│   ├── cache/            # Redis cache
│   ├── queue/            # Message queue
│   └── auth/             # Authentication
├── types/                # TypeScript definitions
└── utils/                # Utility functions
```

#### Agent Base Class
```typescript
import { EventEmitter } from 'events';
import { Logger } from 'pino';

export abstract class BaseAgent extends EventEmitter {
  protected logger: Logger;
  protected agentId: string;
  protected capabilities: string[];

  constructor(agentId: string, logger: Logger) {
    super();
    this.agentId = agentId;
    this.logger = logger.child({ agent: agentId });
    this.capabilities = [];
  }

  abstract async process(input: any, context: AgentContext): Promise<any>;

  protected async sendMessage(targetAgent: string, message: AgentMessage): Promise<void> {
    this.emit('message', {
      from: this.agentId,
      to: targetAgent,
      payload: message,
      timestamp: new Date(),
    });
  }

  protected async logActivity(activity: string, metadata?: any): Promise<void> {
    this.logger.info({ activity, metadata }, `Agent activity: ${activity}`);
  }
}

export interface AgentContext {
  sessionId: string;
  userId: string;
  databaseType: 'mysql' | 'postgresql';
  schemaContext?: any;
  conversationHistory?: any[];
}

export interface AgentMessage {
  type: string;
  data: any;
  correlationId?: string;
  priority?: 'low' | 'medium' | 'high' | 'critical';
}
```

#### SQL Generation Agent Implementation
```typescript
import { BaseAgent } from '../base-agent';
import { OpenAI } from 'openai';

export class SQLGenerationAgent extends BaseAgent {
  private openai: OpenAI;
  private promptTemplates: Map<string, string>;

  constructor(logger: Logger, openaiApiKey: string) {
    super('sql-generation', logger);
    this.openai = new OpenAI({ apiKey: openaiApiKey });
    this.initializePromptTemplates();
  }

  async process(input: QuerySpecification, context: AgentContext): Promise<GeneratedQuery> {
    try {
      const prompt = this.buildPrompt(input, context);
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          { role: 'system', content: this.getSystemPrompt(context.databaseType) },
          { role: 'user', content: prompt }
        ],
        temperature: 0.1,
        max_tokens: 1000,
      });

      const generatedSQL = this.parseResponse(response.choices[0].message.content);
      
      return {
        sql: generatedSQL.query,
        explanation: generatedSQL.explanation,
        optimizationNotes: generatedSQL.notes,
        estimatedPerformance: await this.estimatePerformance(generatedSQL.query),
        dialectSpecificFeatures: this.identifyDialectFeatures(generatedSQL.query, context.databaseType)
      };
    } catch (error) {
      this.logger.error({ error }, 'SQL generation failed');
      throw new Error('Failed to generate SQL query');
    }
  }

  private buildPrompt(input: QuerySpecification, context: AgentContext): string {
    const template = this.promptTemplates.get('basic-generation');
    return template
      .replace('{intent}', JSON.stringify(input.intent))
      .replace('{schema}', JSON.stringify(input.schemaMetadata))
      .replace('{database}', context.databaseType)
      .replace('{optimization}', input.optimizationLevel);
  }

  private getSystemPrompt(databaseType: string): string {
    return `You are an expert SQL developer specializing in ${databaseType}. 
    Generate optimized, syntactically correct SQL queries based on user requirements.
    Always consider performance implications and use database-specific best practices.`;
  }
}
```

### Database Implementation

#### Application Database Schema (PostgreSQL)
```sql
-- Users and authentication
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Database connections
CREATE TABLE database_connections (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    database_type VARCHAR(50) NOT NULL,
    connection_string_encrypted TEXT NOT NULL,
    schema_metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Query sessions and history
CREATE TABLE query_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    database_connection_id UUID REFERENCES database_connections(id),
    session_data JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Generated queries and their metadata
CREATE TABLE generated_queries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES query_sessions(id) ON DELETE CASCADE,
    user_input TEXT NOT NULL,
    generated_sql TEXT NOT NULL,
    explanation TEXT,
    performance_metrics JSONB,
    user_feedback INTEGER CHECK (user_feedback BETWEEN 1 AND 5),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_database_connections_user_id ON database_connections(user_id);
CREATE INDEX idx_query_sessions_user_id ON query_sessions(user_id);
CREATE INDEX idx_generated_queries_session_id ON generated_queries(session_id);
CREATE INDEX idx_generated_queries_created_at ON generated_queries(created_at);
```

### AI/ML Infrastructure

#### OpenAI Integration
```typescript
import { OpenAI } from 'openai';
import { RateLimiter } from 'limiter';

export class AIService {
  private openai: OpenAI;
  private rateLimiter: RateLimiter;

  constructor(apiKey: string) {
    this.openai = new OpenAI({ apiKey });
    this.rateLimiter = new RateLimiter({ tokensPerInterval: 100, interval: 'minute' });
  }

  async generateCompletion(prompt: string, options: CompletionOptions = {}): Promise<string> {
    await this.rateLimiter.removeTokens(1);

    const response = await this.openai.chat.completions.create({
      model: options.model || 'gpt-4',
      messages: [
        { role: 'system', content: options.systemPrompt || 'You are a helpful assistant.' },
        { role: 'user', content: prompt }
      ],
      temperature: options.temperature || 0.1,
      max_tokens: options.maxTokens || 1000,
    });

    return response.choices[0].message.content || '';
  }
}

interface CompletionOptions {
  model?: string;
  systemPrompt?: string;
  temperature?: number;
  maxTokens?: number;
}
```

#### Vector Database Integration (Pinecone)
```typescript
import { PineconeClient } from '@pinecone-database/pinecone';

export class VectorService {
  private pinecone: PineconeClient;
  private indexName: string;

  constructor(apiKey: string, environment: string, indexName: string) {
    this.pinecone = new PineconeClient();
    this.indexName = indexName;
    this.initialize(apiKey, environment);
  }

  private async initialize(apiKey: string, environment: string): Promise<void> {
    await this.pinecone.init({
      apiKey,
      environment,
    });
  }

  async searchSimilar(vector: number[], topK: number = 10): Promise<any[]> {
    const index = this.pinecone.Index(this.indexName);
    
    const queryResponse = await index.query({
      queryRequest: {
        vector,
        topK,
        includeMetadata: true,
      },
    });

    return queryResponse.matches || [];
  }

  async upsertVectors(vectors: Array<{ id: string; values: number[]; metadata?: any }>): Promise<void> {
    const index = this.pinecone.Index(this.indexName);
    
    await index.upsert({
      upsertRequest: {
        vectors,
      },
    });
  }
}
```

### Deployment Configuration

#### Docker Configuration
```dockerfile
# Dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json ./
RUN npm ci --only=production

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000

CMD ["node", "server.js"]
```

#### Kubernetes Deployment
```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: querycraft-studio
spec:
  replicas: 3
  selector:
    matchLabels:
      app: querycraft-studio
  template:
    metadata:
      labels:
        app: querycraft-studio
    spec:
      containers:
      - name: app
        image: querycraft-studio:latest
        ports:
        - containerPort: 3000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: database-url
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: openai-api-key
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
---
apiVersion: v1
kind: Service
metadata:
  name: querycraft-studio-service
spec:
  selector:
    app: querycraft-studio
  ports:
  - protocol: TCP
    port: 80
    targetPort: 3000
  type: LoadBalancer
```

### Monitoring and Observability

#### Application Monitoring
```typescript
import { createLogger } from 'pino';
import { metrics } from '@opentelemetry/api-metrics';

export class MonitoringService {
  private logger = createLogger({
    level: process.env.LOG_LEVEL || 'info',
    transport: {
      target: 'pino-pretty',
      options: {
        colorize: true
      }
    }
  });

  private meter = metrics.getMeter('querycraft-studio');
  private queryGenerationCounter = this.meter.createCounter('query_generation_total');
  private queryGenerationDuration = this.meter.createHistogram('query_generation_duration_ms');

  logQueryGeneration(userId: string, duration: number, success: boolean): void {
    this.logger.info({
      userId,
      duration,
      success,
      event: 'query_generation'
    });

    this.queryGenerationCounter.add(1, {
      success: success.toString(),
      user_id: userId
    });

    this.queryGenerationDuration.record(duration, {
      success: success.toString()
    });
  }
}
```

This implementation guide provides the foundation for building QueryCraft Studio with modern, scalable technologies and best practices. Each component is designed for maintainability, performance, and future extensibility.
